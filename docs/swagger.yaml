basePath: /api/v1
definitions:
  handlers.ValidateLicenseRequest:
    type: object
  handlers.ValidateLicenseResponse:
    properties:
      account: {}
      cache_hit:
        example: false
        type: boolean
      claims:
        additionalProperties: true
        type: object
      errors:
        items:
          type: string
        type: array
      expires_at:
        example: "2025-12-31T23:59:59Z"
        type: string
      license: {}
      machines_allowed:
        example: 5
        type: integer
      machines_used:
        example: 2
        type: integer
      policy: {}
      valid:
        example: true
        type: boolean
      validation_time:
        example: "2025-07-12T16:15:30Z"
        type: string
      warnings:
        items:
          type: string
        type: array
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: GoKeys API Support
    url: https://gokeys.com/support
  description: Enterprise License Management Platform with comprehensive license validation,
    machine tracking, and policy management capabilities.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://gokeys.com/terms
  title: GoKeys License Management API
  version: "1.0"
paths:
  /licenses:
    get:
      consumes:
      - application/json
      description: Get a paginated list of licenses for the authenticated account
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Page size (default: 20, max: 100)'
        in: query
        name: page_size
        type: integer
      - description: 'Sort field (default: created_at)'
        in: query
        name: sort_by
        type: string
      - description: 'Sort order: ASC or DESC (default: DESC)'
        in: query
        name: sort_order
        type: string
      - description: Search term for license key or name
        in: query
        name: search
        type: string
      - description: Filter by license status
        in: query
        name: status
        type: string
      - description: Filter by product ID
        in: query
        name: product_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of licenses with pagination
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Authentication required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List Licenses
      tags:
      - Licenses
  /licenses/quick-validate:
    get:
      consumes:
      - application/json
      description: Perform a quick license validation with minimal response data
      parameters:
      - description: License key to validate
        in: query
        name: license_key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License is valid
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Missing license key
          schema:
            additionalProperties: true
            type: object
        "403":
          description: License is invalid
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - LicenseKeyAuth: []
      summary: Quick License Validation
      tags:
      - Licenses
  /licenses/validate:
    get:
      consumes:
      - application/json
      description: Validate a license key using query parameters
      parameters:
      - description: License key to validate
        example: '"LIC-12345-ABCDE-67890-FGHIJ"'
        in: query
        name: license_key
        required: true
        type: string
      - description: Machine fingerprint
        example: '"fp-mac-12345678"'
        in: query
        name: machine_fingerprint
        type: string
      - description: Environment name
        example: '"production"'
        in: query
        name: environment
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: License validation result
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - LicenseKeyAuth: []
      summary: Validate License (GET)
      tags:
      - Licenses
    post:
      consumes:
      - application/json
      description: Validate a license key with optional machine fingerprint and environment
      parameters:
      - description: License validation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ValidateLicenseRequest'
      produces:
      - application/json
      responses:
        "200":
          description: License is valid
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "403":
          description: License is invalid
          schema:
            $ref: '#/definitions/handlers.ValidateLicenseResponse'
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - LicenseKeyAuth: []
      summary: Validate License (POST)
      tags:
      - Licenses
securityDefinitions:
  ApiKeyAuth:
    description: API key for authentication
    in: header
    name: X-API-Key
    type: apiKey
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
  LicenseKeyAuth:
    description: License key for validation endpoints
    in: header
    name: X-License-Key
    type: apiKey
swagger: "2.0"
tags:
- description: Authentication and authorization endpoints
  name: Authentication
- description: License management and validation
  name: Licenses
- description: Account management
  name: Accounts
- description: Product management
  name: Products
- description: Policy configuration
  name: Policies
- description: Machine registration and tracking
  name: Machines
- description: User management
  name: Users
- description: System health and monitoring
  name: Health
- description: System metrics and analytics
  name: Metrics
