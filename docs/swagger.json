{"swagger": "2.0", "info": {"description": "Enterprise License Management Platform with comprehensive license validation, machine tracking, and policy management capabilities.", "title": "GoKeys License Management API", "termsOfService": "https://gokeys.com/terms", "contact": {"name": "GoKeys API Support", "url": "https://gokeys.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/licenses": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of licenses for the authenticated account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "List Licenses", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Page size (default: 20, max: 100)", "name": "page_size", "in": "query"}, {"type": "string", "description": "Sort field (default: created_at)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort order: ASC or DESC (default: DESC)", "name": "sort_order", "in": "query"}, {"type": "string", "description": "Search term for license key or name", "name": "search", "in": "query"}, {"type": "string", "description": "Filter by license status", "name": "status", "in": "query"}, {"type": "string", "description": "Filter by product ID", "name": "product_id", "in": "query"}], "responses": {"200": {"description": "List of licenses with pagination", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Authentication required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/quick-validate": {"get": {"security": [{"LicenseKeyAuth": []}], "description": "Perform a quick license validation with minimal response data", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Quick License Validation", "parameters": [{"type": "string", "description": "License key to validate", "name": "license_key", "in": "query", "required": true}], "responses": {"200": {"description": "License is valid", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Missing license key", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "License is invalid", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/licenses/validate": {"get": {"security": [{"LicenseKeyAuth": []}], "description": "Validate a license key using query parameters", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate License (GET)", "parameters": [{"type": "string", "example": "\"LIC-12345-ABCDE-67890-FGHIJ\"", "description": "License key to validate", "name": "license_key", "in": "query", "required": true}, {"type": "string", "example": "\"fp-mac-12345678\"", "description": "Machine fingerprint", "name": "machine_fingerprint", "in": "query"}, {"type": "string", "example": "\"production\"", "description": "Environment name", "name": "environment", "in": "query"}], "responses": {"200": {"description": "License validation result", "schema": {"$ref": "#/definitions/handlers.ValidateLicenseResponse"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"LicenseKeyAuth": []}], "description": "Validate a license key with optional machine fingerprint and environment", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Licenses"], "summary": "Validate License (POST)", "parameters": [{"description": "License validation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ValidateLicenseRequest"}}], "responses": {"200": {"description": "License is valid", "schema": {"$ref": "#/definitions/handlers.ValidateLicenseResponse"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "License is invalid", "schema": {"$ref": "#/definitions/handlers.ValidateLicenseResponse"}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"handlers.ValidateLicenseRequest": {"type": "object"}, "handlers.ValidateLicenseResponse": {"type": "object", "properties": {"account": {}, "cache_hit": {"type": "boolean", "example": false}, "claims": {"type": "object", "additionalProperties": true}, "errors": {"type": "array", "items": {"type": "string"}}, "expires_at": {"type": "string", "example": "2025-12-31T23:59:59Z"}, "license": {}, "machines_allowed": {"type": "integer", "example": 5}, "machines_used": {"type": "integer", "example": 2}, "policy": {}, "valid": {"type": "boolean", "example": true}, "validation_time": {"type": "string", "example": "2025-07-12T16:15:30Z"}, "warnings": {"type": "array", "items": {"type": "string"}}}}}, "securityDefinitions": {"ApiKeyAuth": {"description": "API key for authentication", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-Key", "in": "header"}, "BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "LicenseKeyAuth": {"description": "License key for validation endpoints", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-License-Key", "in": "header"}}, "tags": [{"description": "Authentication and authorization endpoints", "name": "Authentication"}, {"description": "License management and validation", "name": "Licenses"}, {"description": "Account management", "name": "Accounts"}, {"description": "Product management", "name": "Products"}, {"description": "Policy configuration", "name": "Policies"}, {"description": "Machine registration and tracking", "name": "Machines"}, {"description": "User management", "name": "Users"}, {"description": "System health and monitoring", "name": "Health"}, {"description": "System metrics and analytics", "name": "Metrics"}]}