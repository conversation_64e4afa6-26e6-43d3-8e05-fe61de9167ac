server:
  host: "0.0.0.0"
  port: 8080
  environment: "development"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

database:
  host: "localhost"
  port: 5432
  user: "gokeys"
  password: "gokeys_dev_password"
  dbname: "gokeys_dev"
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  address: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conn: 5

metrics:
  enabled: true
  port: 9090
  path: "/metrics"
  victoria_metrics:
    enabled: false
    endpoint: ""
    username: ""
    password: ""

nats:
  url: "nats://localhost:4222"
  cluster_id: "gokeys-cluster"
  client_id: "gokeys-client"
  max_reconnects: 10
  reconnect_delay: 2
  connection_name: "GoKeys"

security:
  jwt_secret: "development-secret-change-in-production"
  jwt_expiration: 3600
  bcrypt_cost: 12
  rate_limit_enabled: true
  rate_limit_requests: 100
  rate_limit_window: 60
  cors_enabled: true
  cors_origins:
    - "*"

logging:
  level: "info"
  format: "json"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true