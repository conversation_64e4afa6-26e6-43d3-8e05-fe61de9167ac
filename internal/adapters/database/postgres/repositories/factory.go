package repositories

import (
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// RepositoryFactory creates and manages all repository instances
type RepositoryFactory struct {
	db *gorm.DB
	
	// Repository instances
	account          repositories.AccountRepository
	product          repositories.ProductRepository
	policy           repositories.PolicyRepository
	license          repositories.LicenseRepository
	machine          repositories.MachineRepository
	user             repositories.UserRepository
	token            repositories.TokenRepository
	session          repositories.SessionRepository
	environment      repositories.EnvironmentRepository
	group            repositories.GroupRepository
	entitlement      repositories.EntitlementRepository
	role             repositories.RoleRepository
	permission       repositories.PermissionRepository
	plan             repositories.PlanRepository
	machineComponent repositories.MachineComponentRepository
	machineProcess   repositories.MachineProcessRepository
	secondFactor     repositories.SecondFactorRepository
	webhookEndpoint  repositories.WebhookEndpointRepository
}

// NewRepositoryFactory creates a new repository factory
func NewRepositoryFactory(db *gorm.DB) *RepositoryFactory {
	return &RepositoryFactory{
		db: db,
	}
}

// Account returns the account repository
func (f *RepositoryFactory) Account() repositories.AccountRepository {
	if f.account == nil {
		f.account = NewAccountRepository(f.db)
	}
	return f.account
}

// Product returns the product repository
func (f *RepositoryFactory) Product() repositories.ProductRepository {
	if f.product == nil {
		f.product = NewProductRepository(f.db)
	}
	return f.product
}

// Policy returns the policy repository
func (f *RepositoryFactory) Policy() repositories.PolicyRepository {
	if f.policy == nil {
		f.policy = NewPolicyRepository(f.db)
	}
	return f.policy
}

// License returns the license repository
func (f *RepositoryFactory) License() repositories.LicenseRepository {
	if f.license == nil {
		f.license = NewLicenseRepository(f.db)
	}
	return f.license
}

// Machine returns the machine repository
func (f *RepositoryFactory) Machine() repositories.MachineRepository {
	if f.machine == nil {
		f.machine = NewMachineRepository(f.db)
	}
	return f.machine
}

// User returns the user repository
func (f *RepositoryFactory) User() repositories.UserRepository {
	if f.user == nil {
		f.user = NewUserRepository(f.db)
	}
	return f.user
}

// Token returns the token repository
func (f *RepositoryFactory) Token() repositories.TokenRepository {
	if f.token == nil {
		f.token = NewTokenRepository(f.db)
	}
	return f.token
}

// Session returns the session repository
func (f *RepositoryFactory) Session() repositories.SessionRepository {
	if f.session == nil {
		f.session = NewSessionRepository(f.db)
	}
	return f.session
}

// Environment returns the environment repository
func (f *RepositoryFactory) Environment() repositories.EnvironmentRepository {
	if f.environment == nil {
		f.environment = NewEnvironmentRepository(f.db)
	}
	return f.environment
}

// Group returns the group repository
func (f *RepositoryFactory) Group() repositories.GroupRepository {
	if f.group == nil {
		f.group = NewGroupRepository(f.db)
	}
	return f.group
}

// Entitlement returns the entitlement repository
func (f *RepositoryFactory) Entitlement() repositories.EntitlementRepository {
	if f.entitlement == nil {
		f.entitlement = NewEntitlementRepository(f.db)
	}
	return f.entitlement
}

// Role returns the role repository
func (f *RepositoryFactory) Role() repositories.RoleRepository {
	if f.role == nil {
		f.role = NewRoleRepository(f.db)
	}
	return f.role
}

// Permission returns the permission repository
func (f *RepositoryFactory) Permission() repositories.PermissionRepository {
	if f.permission == nil {
		f.permission = NewPermissionRepository(f.db)
	}
	return f.permission
}

// Plan returns the plan repository
func (f *RepositoryFactory) Plan() repositories.PlanRepository {
	if f.plan == nil {
		f.plan = NewPlanRepository(f.db)
	}
	return f.plan
}

// MachineComponent returns the machine component repository
func (f *RepositoryFactory) MachineComponent() repositories.MachineComponentRepository {
	if f.machineComponent == nil {
		f.machineComponent = NewMachineComponentRepository(f.db)
	}
	return f.machineComponent
}

// MachineProcess returns the machine process repository
func (f *RepositoryFactory) MachineProcess() repositories.MachineProcessRepository {
	if f.machineProcess == nil {
		f.machineProcess = NewMachineProcessRepository(f.db)
	}
	return f.machineProcess
}

// SecondFactor returns the second factor repository
func (f *RepositoryFactory) SecondFactor() repositories.SecondFactorRepository {
	if f.secondFactor == nil {
		f.secondFactor = NewSecondFactorRepository(f.db)
	}
	return f.secondFactor
}

// WebhookEndpoint returns the webhook endpoint repository
func (f *RepositoryFactory) WebhookEndpoint() repositories.WebhookEndpointRepository {
	if f.webhookEndpoint == nil {
		f.webhookEndpoint = NewWebhookEndpointRepository(f.db)
	}
	return f.webhookEndpoint
}

// GetDB returns the underlying database connection
func (f *RepositoryFactory) GetDB() *gorm.DB {
	return f.db
}

// Transaction wraps operations in a database transaction
func (f *RepositoryFactory) Transaction(fn func(*RepositoryFactory) error) error {
	return f.db.Transaction(func(tx *gorm.DB) error {
		txFactory := NewRepositoryFactory(tx)
		return fn(txFactory)
	})
}