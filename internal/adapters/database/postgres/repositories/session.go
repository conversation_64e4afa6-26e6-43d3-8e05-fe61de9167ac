package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// SessionRepositoryImpl implements SessionRepository interface
type SessionRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Session]
}

// NewSessionRepository creates a new session repository
func NewSessionRepository(db *gorm.DB) repositories.SessionRepository {
	return &SessionRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Session](db),
	}
}

// GetByToken retrieves a session by token ID
func (r *SessionRepositoryImpl) GetByToken(ctx context.Context, tokenID uuid.UUID) (*entities.Session, error) {
	var session entities.Session
	err := r.GetDB().WithContext(ctx).
		Where("token_id = ?", tokenID).
		First(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

// CleanupExpired removes expired sessions
func (r *SessionRepositoryImpl) CleanupExpired(ctx context.Context) (int64, error) {
	result := r.GetDB().WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Delete(&entities.Session{})
	return result.RowsAffected, result.Error
}

// UpdateLastUsed updates the session's last used timestamp
func (r *SessionRepositoryImpl) UpdateLastUsed(ctx context.Context, sessionID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.Session{}).
		Where("id = ?", sessionID).
		Updates(map[string]interface{}{
			"last_used_at": time.Now(),
			"updated_at":   time.Now(),
		}).Error
}