package repositories

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TokenRepositoryImpl implements TokenRepository interface
type TokenRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Token]
}

// NewTokenRepository creates a new token repository
func NewTokenRepository(db *gorm.DB) repositories.TokenRepository {
	return &TokenRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Token](db),
	}
}

// GetByBearer retrieves a token by its bearer string
func (r *TokenRepositoryImpl) GetByBearer(ctx context.Context, bearer string) (*entities.Token, error) {
	var token entities.Token
	err := r.GetDB().WithContext(ctx).
		Where("digest = ? OR token = ?", bearer, bearer).
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetByBearerTypeAndID retrieves tokens by bearer type and ID
func (r *TokenRepositoryImpl) GetByBearerTypeAndID(ctx context.Context, bearerType string, bearerID uuid.UUID) ([]*entities.Token, error) {
	var tokens []*entities.Token
	err := r.GetDB().WithContext(ctx).
		Where("bearer_type = ? AND bearer_id = ?", bearerType, bearerID).
		Find(&tokens).Error
	return tokens, err
}

// CleanupExpired removes expired tokens
func (r *TokenRepositoryImpl) CleanupExpired(ctx context.Context) (int64, error) {
	result := r.GetDB().WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Delete(&entities.Token{})
	return result.RowsAffected, result.Error
}

// GetByAccount retrieves all tokens for an account
func (r *TokenRepositoryImpl) GetByAccount(ctx context.Context, accountID uuid.UUID) ([]*entities.Token, error) {
	var tokens []*entities.Token
	err := r.GetDB().WithContext(ctx).
		Where("account_id = ?", accountID).
		Find(&tokens).Error
	return tokens, err
}

// UpdateLastUsed updates the token's last used timestamp
func (r *TokenRepositoryImpl) UpdateLastUsed(ctx context.Context, tokenID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.Token{}).
		Where("id = ?", tokenID).
		Updates(map[string]interface{}{
			"last_used":  time.Now(),
			"updated_at": time.Now(),
		}).Error
}
