package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// MachineRepositoryImpl implements MachineRepository interface
type MachineRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Machine]
}

// NewMachineRepository creates a new machine repository
func NewMachineRepository(db *gorm.DB) repositories.MachineRepository {
	return &MachineRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Machine](db),
	}
}

// GetByFingerprint retrieves a machine by its fingerprint
func (r *MachineRepositoryImpl) GetByFingerprint(ctx context.Context, fingerprint string, accountID uuid.UUID) (*entities.Machine, error) {
	var machine entities.Machine
	err := r.GetDB().WithContext(ctx).
		Preload("License").
		Preload("Policy").
		Preload("Owner").
		Preload("Group").
		Where("fingerprint = ? AND account_id = ?", fingerprint, accountID).
		First(&machine).Error
	if err != nil {
		return nil, err
	}
	return &machine, nil
}

// GetByLicense retrieves machines by license ID
func (r *MachineRepositoryImpl) GetByLicense(ctx context.Context, licenseID uuid.UUID) ([]*entities.Machine, error) {
	var machines []*entities.Machine
	err := r.GetDB().WithContext(ctx).
		Preload("License").
		Preload("Policy").
		Where("license_id = ?", licenseID).
		Find(&machines).Error
	return machines, err
}

// UpdateHeartbeat updates machine heartbeat timestamp
func (r *MachineRepositoryImpl) UpdateHeartbeat(ctx context.Context, machineID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.Machine{}).
		Where("id = ?", machineID).
		Updates(map[string]interface{}{
			"last_heartbeat_at": time.Now(),
			"updated_at":        time.Now(),
		}).Error
}

// GetStale retrieves machines that haven't sent heartbeat for a while
func (r *MachineRepositoryImpl) GetStale(ctx context.Context, accountID uuid.UUID, olderThan time.Time) ([]*entities.Machine, error) {
	var machines []*entities.Machine
	err := r.GetDB().WithContext(ctx).
		Preload("License").
		Preload("Policy").
		Where("account_id = ? AND (last_heartbeat_at IS NULL OR last_heartbeat_at < ?)", accountID, olderThan).
		Find(&machines).Error
	return machines, err
}