package repositories

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// Environment Repository
type EnvironmentRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Environment]
}

func NewEnvironmentRepository(db *gorm.DB) repositories.EnvironmentRepository {
	return &EnvironmentRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Environment](db),
	}
}

func (r *EnvironmentRepositoryImpl) GetByCode(ctx context.Context, code string, accountID uuid.UUID) (*entities.Environment, error) {
	var env entities.Environment
	err := r.GetDB().WithContext(ctx).Where("code = ? AND account_id = ?", code, accountID).First(&env).Error
	if err != nil {
		return nil, err
	}
	return &env, nil
}

func (r *EnvironmentRepositoryImpl) GetByIsolationStrategy(ctx context.Context, strategy string, accountID uuid.UUID) ([]*entities.Environment, error) {
	var envs []*entities.Environment
	err := r.GetDB().WithContext(ctx).Where("isolation_strategy = ? AND account_id = ?", strategy, accountID).Find(&envs).Error
	return envs, err
}

// Group Repository
type GroupRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Group]
}

func NewGroupRepository(db *gorm.DB) repositories.GroupRepository {
	return &GroupRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Group](db),
	}
}

func (r *GroupRepositoryImpl) GetByName(ctx context.Context, name string, accountID uuid.UUID) (*entities.Group, error) {
	var group entities.Group
	err := r.GetDB().WithContext(ctx).Where("name = ? AND account_id = ?", name, accountID).First(&group).Error
	if err != nil {
		return nil, err
	}
	return &group, nil
}

func (r *GroupRepositoryImpl) GetWithUsers(ctx context.Context, groupID uuid.UUID) (*entities.Group, error) {
	var group entities.Group
	err := r.GetDB().WithContext(ctx).Preload("Users").Where("id = ?", groupID).First(&group).Error
	if err != nil {
		return nil, err
	}
	return &group, nil
}

// Entitlement Repository
type EntitlementRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Entitlement]
}

func NewEntitlementRepository(db *gorm.DB) repositories.EntitlementRepository {
	return &EntitlementRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Entitlement](db),
	}
}

func (r *EntitlementRepositoryImpl) GetByCode(ctx context.Context, code string, accountID uuid.UUID) (*entities.Entitlement, error) {
	var ent entities.Entitlement
	err := r.GetDB().WithContext(ctx).Where("code = ? AND account_id = ?", code, accountID).First(&ent).Error
	if err != nil {
		return nil, err
	}
	return &ent, nil
}

func (r *EntitlementRepositoryImpl) GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.Entitlement, error) {
	var ents []*entities.Entitlement
	err := r.GetDB().WithContext(ctx).
		Joins("JOIN policy_entitlements ON entitlements.id = policy_entitlements.entitlement_id").
		Where("policy_entitlements.policy_id = ?", policyID).
		Find(&ents).Error
	return ents, err
}

// Role Repository
type RoleRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Role]
}

func NewRoleRepository(db *gorm.DB) repositories.RoleRepository {
	return &RoleRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Role](db),
	}
}

func (r *RoleRepositoryImpl) GetByResource(ctx context.Context, resourceType string, resourceID uuid.UUID, accountID uuid.UUID) ([]*entities.Role, error) {
	var roles []*entities.Role
	err := r.GetDB().WithContext(ctx).
		Where("resource_type = ? AND resource_id = ? AND account_id = ?", resourceType, resourceID, accountID).
		Find(&roles).Error
	return roles, err
}

func (r *RoleRepositoryImpl) GetUserRoles(ctx context.Context, userID uuid.UUID) ([]*entities.Role, error) {
	var roles []*entities.Role
	// This would need a join table for user_roles
	err := r.GetDB().WithContext(ctx).Find(&roles).Error
	return roles, err
}

// Permission Repository
type PermissionRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Permission]
}

func NewPermissionRepository(db *gorm.DB) repositories.PermissionRepository {
	return &PermissionRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Permission](db),
	}
}

func (r *PermissionRepositoryImpl) GetByAction(ctx context.Context, action string) (*entities.Permission, error) {
	var perm entities.Permission
	err := r.GetDB().WithContext(ctx).Where("action = ?", action).First(&perm).Error
	if err != nil {
		return nil, err
	}
	return &perm, nil
}

func (r *PermissionRepositoryImpl) GetByRole(ctx context.Context, roleID uuid.UUID) ([]*entities.Permission, error) {
	var perms []*entities.Permission
	err := r.GetDB().WithContext(ctx).
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&perms).Error
	return perms, err
}

// Plan Repository
type PlanRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Plan]
}

func NewPlanRepository(db *gorm.DB) repositories.PlanRepository {
	return &PlanRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Plan](db),
	}
}

func (r *PlanRepositoryImpl) GetByPlanID(ctx context.Context, planID string) (*entities.Plan, error) {
	var plan entities.Plan
	err := r.GetDB().WithContext(ctx).Where("plan_id = ?", planID).First(&plan).Error
	if err != nil {
		return nil, err
	}
	return &plan, nil
}

func (r *PlanRepositoryImpl) GetPublicPlans(ctx context.Context) ([]*entities.Plan, error) {
	var plans []*entities.Plan
	err := r.GetDB().WithContext(ctx).Where("private = ?", false).Find(&plans).Error
	return plans, err
}

// Machine Component Repository
type MachineComponentRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.MachineComponent]
}

func NewMachineComponentRepository(db *gorm.DB) repositories.MachineComponentRepository {
	return &MachineComponentRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.MachineComponent](db),
	}
}

func (r *MachineComponentRepositoryImpl) GetByMachine(ctx context.Context, machineID uuid.UUID) ([]*entities.MachineComponent, error) {
	var components []*entities.MachineComponent
	err := r.GetDB().WithContext(ctx).Where("machine_id = ?", machineID).Find(&components).Error
	return components, err
}

func (r *MachineComponentRepositoryImpl) GetByFingerprint(ctx context.Context, fingerprint string, machineID uuid.UUID) (*entities.MachineComponent, error) {
	var component entities.MachineComponent
	err := r.GetDB().WithContext(ctx).Where("fingerprint = ? AND machine_id = ?", fingerprint, machineID).First(&component).Error
	if err != nil {
		return nil, err
	}
	return &component, nil
}

// Machine Process Repository
type MachineProcessRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.MachineProcess]
}

func NewMachineProcessRepository(db *gorm.DB) repositories.MachineProcessRepository {
	return &MachineProcessRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.MachineProcess](db),
	}
}

func (r *MachineProcessRepositoryImpl) GetByMachine(ctx context.Context, machineID uuid.UUID) ([]*entities.MachineProcess, error) {
	var processes []*entities.MachineProcess
	err := r.GetDB().WithContext(ctx).Where("machine_id = ?", machineID).Find(&processes).Error
	return processes, err
}

func (r *MachineProcessRepositoryImpl) GetByPID(ctx context.Context, pid int, machineID uuid.UUID) (*entities.MachineProcess, error) {
	var process entities.MachineProcess
	err := r.GetDB().WithContext(ctx).Where("pid = ? AND machine_id = ?", pid, machineID).First(&process).Error
	if err != nil {
		return nil, err
	}
	return &process, nil
}

func (r *MachineProcessRepositoryImpl) UpdateHeartbeat(ctx context.Context, processID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.MachineProcess{}).
		Where("id = ?", processID).
		Update("last_heartbeat_at", gorm.Expr("NOW()")).Error
}

// Second Factor Repository
type SecondFactorRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.SecondFactor]
}

func NewSecondFactorRepository(db *gorm.DB) repositories.SecondFactorRepository {
	return &SecondFactorRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.SecondFactor](db),
	}
}

func (r *SecondFactorRepositoryImpl) GetByUser(ctx context.Context, userID uuid.UUID) ([]*entities.SecondFactor, error) {
	var factors []*entities.SecondFactor
	err := r.GetDB().WithContext(ctx).Where("user_id = ?", userID).Find(&factors).Error
	return factors, err
}

func (r *SecondFactorRepositoryImpl) GetEnabledByUser(ctx context.Context, userID uuid.UUID) ([]*entities.SecondFactor, error) {
	var factors []*entities.SecondFactor
	err := r.GetDB().WithContext(ctx).Where("user_id = ? AND enabled = ?", userID, true).Find(&factors).Error
	return factors, err
}