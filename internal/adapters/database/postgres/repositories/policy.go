package repositories

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// PolicyRepositoryImpl implements PolicyRepository interface
type PolicyRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Policy]
}

// NewPolicyRepository creates a new policy repository
func NewPolicyRepository(db *gorm.DB) repositories.PolicyRepository {
	return &PolicyRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Policy](db),
	}
}

// GetByProduct retrieves policies by product ID
func (r *PolicyRepositoryImpl) GetByProduct(ctx context.Context, productID uuid.UUID) ([]*entities.Policy, error) {
	var policies []*entities.Policy
	err := r.GetDB().WithContext(ctx).
		Where("product_id = ?", productID).
		Find(&policies).Error
	return policies, err
}

// GetByScheme retrieves policies by cryptographic scheme and account ID
func (r *PolicyRepositoryImpl) GetByScheme(ctx context.Context, scheme string, accountID uuid.UUID) ([]*entities.Policy, error) {
	var policies []*entities.Policy
	err := r.GetDB().WithContext(ctx).
		Where("scheme = ? AND account_id = ?", scheme, accountID).
		Find(&policies).Error
	return policies, err
}