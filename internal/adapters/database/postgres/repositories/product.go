package repositories

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// ProductRepositoryImpl implements ProductRepository interface
type ProductRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Product]
}

// NewProductRepository creates a new product repository
func NewProductRepository(db *gorm.DB) repositories.ProductRepository {
	return &ProductRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Product](db),
	}
}

// GetByCode retrieves a product by its code and account ID
func (r *ProductRepositoryImpl) GetByCode(ctx context.Context, code string, accountID uuid.UUID) (*entities.Product, error) {
	var product entities.Product
	err := r.GetDB().WithContext(ctx).
		Where("code = ? AND account_id = ?", code, accountID).
		First(&product).Error
	if err != nil {
		return nil, err
	}
	return &product, nil
}

// GetByAccountID retrieves all products for an account
func (r *ProductRepositoryImpl) GetByAccountID(ctx context.Context, accountID uuid.UUID) ([]*entities.Product, error) {
	var products []*entities.Product
	err := r.GetDB().WithContext(ctx).
		Where("account_id = ?", accountID).
		Find(&products).Error
	return products, err
}