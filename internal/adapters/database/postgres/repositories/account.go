package repositories

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// AccountRepositoryImpl implements AccountRepository interface
type AccountRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Account]
}

// NewAccountRepository creates a new account repository
func NewAccountRepository(db *gorm.DB) repositories.AccountRepository {
	return &AccountRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Account](db),
	}
}

// GetBySlug retrieves an account by its slug
func (r *AccountRepositoryImpl) GetBySlug(ctx context.Context, slug string) (*entities.Account, error) {
	var account entities.Account
	err := r.GetDB().WithContext(ctx).Where("slug = ?", slug).First(&account).Error
	if err != nil {
		return nil, err
	}
	return &account, nil
}

// GetByDomain retrieves an account by its domain
func (r *AccountRepositoryImpl) GetByDomain(ctx context.Context, domain string) (*entities.Account, error) {
	var account entities.Account
	err := r.GetDB().WithContext(ctx).Where("domain = ?", domain).First(&account).Error
	if err != nil {
		return nil, err
	}
	return &account, nil
}

// GetBySubdomain retrieves an account by its subdomain
func (r *AccountRepositoryImpl) GetBySubdomain(ctx context.Context, subdomain string) (*entities.Account, error) {
	var account entities.Account
	err := r.GetDB().WithContext(ctx).Where("subdomain = ?", subdomain).First(&account).Error
	if err != nil {
		return nil, err
	}
	return &account, nil
}

// UpdatePlan updates an account's plan
func (r *AccountRepositoryImpl) UpdatePlan(ctx context.Context, accountID uuid.UUID, planID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.Account{}).
		Where("id = ?", accountID).
		Update("plan_id", planID).Error
}