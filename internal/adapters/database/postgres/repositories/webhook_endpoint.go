package repositories

import (
	"context"
	"gorm.io/gorm"
	"github.com/google/uuid"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// WebhookEndpoint Repository
type WebhookEndpointRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.WebhookEndpoint]
}

func NewWebhookEndpointRepository(db *gorm.DB) repositories.WebhookEndpointRepository {
	return &WebhookEndpointRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.WebhookEndpoint](db),
	}
}

func (r *WebhookEndpointRepositoryImpl) GetByAccount(ctx context.Context, accountID uuid.UUID) ([]*entities.WebhookEndpoint, error) {
	var webhookEndpoints []*entities.WebhookEndpoint
	err := r.GetDB().WithContext(ctx).Where("account_id = ?", accountID.String()).Find(&webhookEndpoints).Error
	return webhookEndpoints, err
}

func (r *WebhookEndpointRepositoryImpl) GetByEvent(ctx context.Context, event string) ([]*entities.WebhookEndpoint, error) {
	var webhookEndpoints []*entities.WebhookEndpoint
	// Using GORM's JSON array contains operator for PostgreSQL
	err := r.GetDB().WithContext(ctx).Where("events @> ?", `["`+event+`"]`).Find(&webhookEndpoints).Error
	return webhookEndpoints, err
}

func (r *WebhookEndpointRepositoryImpl) UpdateDeliveryStats(ctx context.Context, endpointID uuid.UUID, success bool) error {
	updates := map[string]interface{}{}
	
	if success {
		updates["successful_deliveries"] = gorm.Expr("successful_deliveries + 1")
		updates["last_success_at"] = "NOW()"
	} else {
		updates["failed_deliveries"] = gorm.Expr("failed_deliveries + 1")
		updates["last_failure_at"] = "NOW()"
	}
	
	updates["last_delivery_at"] = "NOW()"
	
	return r.GetDB().WithContext(ctx).Model(&entities.WebhookEndpoint{}).
		Where("id = ?", endpointID.String()).
		Updates(updates).Error
}