package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// UserRepositoryImpl implements UserRepository interface
type UserRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.User]
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) repositories.UserRepository {
	return &UserRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.User](db),
	}
}

// GetByEmail retrieves a user by email and account ID
func (r *UserRepositoryImpl) GetByEmail(ctx context.Context, email string, accountID uuid.UUID) (*entities.User, error) {
	var user entities.User
	err := r.GetDB().WithContext(ctx).
		Preload("Group").
		Where("email = ? AND account_id = ?", email, accountID).
		First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByGroup retrieves users by group ID
func (r *UserRepositoryImpl) GetByGroup(ctx context.Context, groupID uuid.UUID) ([]*entities.User, error) {
	var users []*entities.User
	err := r.GetDB().WithContext(ctx).
		Where("group_id = ?", groupID).
		Find(&users).Error
	return users, err
}

// UpdateLastLogin updates the user's last login timestamp
func (r *UserRepositoryImpl) UpdateLastLogin(ctx context.Context, userID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"last_sign_in_at": time.Now(),
			"updated_at":      time.Now(),
		}).Error
}