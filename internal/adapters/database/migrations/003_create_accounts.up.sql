-- Create accounts table (references plans)
CREATE TABLE accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_id UUID REFERENCES plans(id) ON DELETE SET NULL,
    
    -- Basic information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, suspended
    protected BOOLEAN DEFAULT FALSE,
    
    -- Cryptographic keys
    public_key TEXT,
    private_key TEXT, -- encrypted
    secret_key TEXT, -- encrypted
    ed25519_private_key TEXT, -- encrypted
    ed25519_public_key TEXT,
    
    -- Domain and hosting
    domain VARCHAR(255) UNIQUE,
    subdomain VARCHAR(255) UNIQUE,
    cname VARCHAR(255) UNIQUE,
    backend VARCHAR(255),
    
    -- API configuration
    api_version VARCHAR(50),
    
    -- SSO configuration
    sso_organization_id VARCHAR(255) UNIQUE,
    sso_organization_domains TEXT[],
    sso_session_duration INTEGER, -- in seconds
    ssojit_provisioning BOOLEAN DEFAULT FALSE,
    sso_external_authn BOOLEAN DEFAULT FALSE,
    
    -- Slack integration
    slack_invited_at TIMESTAMP WITH TIME ZONE,
    slack_accepted_at TIMESTAMP WITH TIME ZONE,
    slack_team_id VARCHAR(255) UNIQUE,
    slack_channel_id VARCHAR(255) UNIQUE,
    
    -- Notification tracking (last sent timestamps)
    last_low_activity_lifeline_sent_at TIMESTAMP WITH TIME ZONE,
    last_trial_will_end_sent_at TIMESTAMP WITH TIME ZONE,
    last_license_limit_exceeded_sent_at TIMESTAMP WITH TIME ZONE,
    last_request_limit_exceeded_sent_at TIMESTAMP WITH TIME ZONE,
    last_prompt_for_review_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Legacy fields
    subscription_tier VARCHAR(50), -- deprecated, use plan_id
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for accounts
CREATE INDEX idx_accounts_plan_id ON accounts(plan_id);
CREATE INDEX idx_accounts_status ON accounts(status);
CREATE INDEX idx_accounts_protected ON accounts(protected);
CREATE UNIQUE INDEX idx_accounts_slug ON accounts(slug) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX idx_accounts_email ON accounts(email) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX idx_accounts_domain ON accounts(domain) WHERE domain IS NOT NULL AND deleted_at IS NULL;
CREATE UNIQUE INDEX idx_accounts_subdomain ON accounts(subdomain) WHERE subdomain IS NOT NULL AND deleted_at IS NULL;
CREATE UNIQUE INDEX idx_accounts_cname ON accounts(cname) WHERE cname IS NOT NULL AND deleted_at IS NULL;
CREATE UNIQUE INDEX idx_accounts_sso_organization_id ON accounts(sso_organization_id) WHERE sso_organization_id IS NOT NULL AND deleted_at IS NULL;
CREATE UNIQUE INDEX idx_accounts_slack_team_id ON accounts(slack_team_id) WHERE slack_team_id IS NOT NULL AND deleted_at IS NULL;
CREATE UNIQUE INDEX idx_accounts_slack_channel_id ON accounts(slack_channel_id) WHERE slack_channel_id IS NOT NULL AND deleted_at IS NULL;
CREATE INDEX idx_accounts_deleted_at ON accounts(deleted_at);
CREATE INDEX idx_accounts_created_at ON accounts(created_at);
CREATE INDEX idx_accounts_settings_gin ON accounts USING gin(settings);
CREATE INDEX idx_accounts_metadata_gin ON accounts USING gin(metadata);