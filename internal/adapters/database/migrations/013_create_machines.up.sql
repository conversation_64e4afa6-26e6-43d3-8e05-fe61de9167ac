-- Create machines table (references accounts, environments, licenses, policies, groups, users)
CREATE TABLE machines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    license_id UUID REFERENCES licenses(id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(id) ON DELETE SET NULL,
    group_id UUID REFERENCES groups(id) ON DELETE SET NULL,
    owner_id UUID REFERENCES users(id) ON DELETE SET NULL, -- machine owner
    
    -- Identity
    fingerprint VARCHAR(255) NOT NULL,
    name VARCHA<PERSON>(255),
    hostname VA<PERSON>HAR(255),
    platform VARCHAR(255),
    ip VARCHAR(45), -- IPv4 or IPv6
    
    -- State
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, suspended
    activated_at TIMESTAMP WITH TIME ZONE,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    
    -- Hardware
    cores INTEGER,
    
    -- Heartbeat tracking
    last_heartbeat_at TIMESTAMP WITH TIME ZONE,
    next_heartbeat_at TIMESTAMP WITH TIME ZONE,
    last_death_event_sent_at TIMESTAMP WITH TIME ZONE,
    heartbeat_jid VARCHAR(255), -- background job ID
    
    -- Check-out tracking
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    
    -- Override values
    max_processes_override INTEGER,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for machines
CREATE INDEX idx_machines_account_id ON machines(account_id);
CREATE INDEX idx_machines_environment_id ON machines(environment_id);
CREATE INDEX idx_machines_license_id ON machines(license_id);
CREATE INDEX idx_machines_policy_id ON machines(policy_id);
CREATE INDEX idx_machines_group_id ON machines(group_id);
CREATE INDEX idx_machines_owner_id ON machines(owner_id);
CREATE UNIQUE INDEX idx_machines_fingerprint_per_license ON machines(license_id, fingerprint) WHERE deleted_at IS NULL;
CREATE INDEX idx_machines_hostname ON machines(hostname);
CREATE INDEX idx_machines_platform ON machines(platform);
CREATE INDEX idx_machines_ip ON machines(ip);
CREATE INDEX idx_machines_status ON machines(status);
CREATE INDEX idx_machines_activated_at ON machines(activated_at);
CREATE INDEX idx_machines_deactivated_at ON machines(deactivated_at);
CREATE INDEX idx_machines_last_seen ON machines(last_seen);
CREATE INDEX idx_machines_last_heartbeat_at ON machines(last_heartbeat_at);
CREATE INDEX idx_machines_next_heartbeat_at ON machines(next_heartbeat_at);
CREATE INDEX idx_machines_last_check_out_at ON machines(last_check_out_at);
CREATE INDEX idx_machines_heartbeat_jid ON machines(heartbeat_jid);
CREATE INDEX idx_machines_deleted_at ON machines(deleted_at);
CREATE INDEX idx_machines_created_at ON machines(created_at);
CREATE INDEX idx_machines_metadata_gin ON machines USING gin(metadata);