-- Create tokens table (references accounts and environments)
CREATE TABLE tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    
    -- Bearer association (polymorphic)
    bearer_type VARCHAR(100), -- e.g., 'User', 'Product', 'License'
    bearer_id UUID,
    
    -- Basic information
    name VARCHAR(255),
    digest VARCHAR(255) UNIQUE NOT NULL, -- hashed token value
    kind VARCHAR(50) DEFAULT 'admin-token', -- admin-token, license-token, user-token, product-token
    
    -- Limits
    max_activations INTEGER,
    max_deactivations INTEGER,
    activations INTEGER DEFAULT 0,
    deactivations INTEGER DEFAULT 0,
    
    -- Custom permissions
    custom_permissions TEXT[], -- array of permission strings
    
    -- Expiration
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE,
    revoked_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for tokens
CREATE INDEX idx_tokens_account_id ON tokens(account_id);
CREATE INDEX idx_tokens_environment_id ON tokens(environment_id);
CREATE INDEX idx_tokens_bearer_type ON tokens(bearer_type);
CREATE INDEX idx_tokens_bearer_id ON tokens(bearer_id);
CREATE INDEX idx_tokens_bearer_polymorphic ON tokens(bearer_type, bearer_id);
CREATE UNIQUE INDEX idx_tokens_digest ON tokens(digest) WHERE deleted_at IS NULL;
CREATE INDEX idx_tokens_kind ON tokens(kind);
CREATE INDEX idx_tokens_expires_at ON tokens(expires_at);
CREATE INDEX idx_tokens_last_used ON tokens(last_used);
CREATE INDEX idx_tokens_revoked_at ON tokens(revoked_at);
CREATE INDEX idx_tokens_deleted_at ON tokens(deleted_at);
CREATE INDEX idx_tokens_created_at ON tokens(created_at);
CREATE INDEX idx_tokens_custom_permissions_gin ON tokens USING gin(custom_permissions);
CREATE INDEX idx_tokens_metadata_gin ON tokens USING gin(metadata);