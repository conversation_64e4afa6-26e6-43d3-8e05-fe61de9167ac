-- Create sessions table (references accounts, environments, tokens)
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    token_id UUID REFERENCES tokens(id) ON DELETE CASCADE,
    
    -- Bearer association (polymorphic)
    bearer_type VARCHAR(100), -- e.g., 'User', 'Token', 'License'
    bearer_id UUID,
    
    -- Session details
    ip VARCHAR(45), -- IPv4 or IPv6
    user_agent TEXT,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for sessions
CREATE INDEX idx_sessions_account_id ON sessions(account_id);
CREATE INDEX idx_sessions_environment_id ON sessions(environment_id);
CREATE INDEX idx_sessions_token_id ON sessions(token_id);
CREATE INDEX idx_sessions_bearer_type ON sessions(bearer_type);
CREATE INDEX idx_sessions_bearer_id ON sessions(bearer_id);
CREATE INDEX idx_sessions_bearer_polymorphic ON sessions(bearer_type, bearer_id);
CREATE INDEX idx_sessions_ip ON sessions(ip);
CREATE INDEX idx_sessions_last_used_at ON sessions(last_used_at);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX idx_sessions_deleted_at ON sessions(deleted_at);
CREATE INDEX idx_sessions_created_at ON sessions(created_at);