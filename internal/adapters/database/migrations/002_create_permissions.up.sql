-- Create permissions table (independent entity)
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action VARCHAR(255) UNIQUE NOT NULL, -- e.g., "license.read", "policy.create"
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for permissions
CREATE UNIQUE INDEX idx_permissions_action ON permissions(action);

-- Insert default permissions
INSERT INTO permissions (action) VALUES
    ('account.read'),
    ('account.update'),
    ('account.delete'),
    ('user.read'),
    ('user.create'),
    ('user.update'),
    ('user.delete'),
    ('product.read'),
    ('product.create'),
    ('product.update'),
    ('product.delete'),
    ('policy.read'),
    ('policy.create'),
    ('policy.update'),
    ('policy.delete'),
    ('license.read'),
    ('license.create'),
    ('license.update'),
    ('license.delete'),
    ('license.validate'),
    ('machine.read'),
    ('machine.create'),
    ('machine.update'),
    ('machine.delete'),
    ('token.read'),
    ('token.create'),
    ('token.update'),
    ('token.delete'),
    ('webhook.read'),
    ('webhook.create'),
    ('webhook.update'),
    ('webhook.delete');