-- Create machine_components table (references accounts, environments, machines)
CREATE TABLE machine_components (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    machine_id UUID NOT NULL REFERENCES machines(id) ON DELETE CASCADE,
    
    -- Identity
    fingerprint VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for machine_components
CREATE INDEX idx_machine_components_account_id ON machine_components(account_id);
CREATE INDEX idx_machine_components_environment_id ON machine_components(environment_id);
CREATE INDEX idx_machine_components_machine_id ON machine_components(machine_id);
CREATE UNIQUE INDEX idx_machine_components_fingerprint_per_machine ON machine_components(machine_id, fingerprint) WHERE deleted_at IS NULL;
CREATE INDEX idx_machine_components_name ON machine_components(name);
CREATE INDEX idx_machine_components_deleted_at ON machine_components(deleted_at);
CREATE INDEX idx_machine_components_created_at ON machine_components(created_at);
CREATE INDEX idx_machine_components_metadata_gin ON machine_components USING gin(metadata);