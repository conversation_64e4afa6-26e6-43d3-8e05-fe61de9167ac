-- Create second_factors table for 2FA (references accounts, environments, users)
CREATE TABLE second_factors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- TOTP configuration
    secret TEXT NOT NULL, -- encrypted TOTP secret
    enabled BOOLEAN DEFAULT FALSE,
    last_verified_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for second_factors
CREATE INDEX idx_second_factors_account_id ON second_factors(account_id);
CREATE INDEX idx_second_factors_environment_id ON second_factors(environment_id);
CREATE UNIQUE INDEX idx_second_factors_user_id ON second_factors(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_second_factors_enabled ON second_factors(enabled);
CREATE INDEX idx_second_factors_last_verified_at ON second_factors(last_verified_at);
CREATE INDEX idx_second_factors_deleted_at ON second_factors(deleted_at);
CREATE INDEX idx_second_factors_created_at ON second_factors(created_at);