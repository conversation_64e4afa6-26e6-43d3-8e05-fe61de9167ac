-- Create licenses table (references accounts, products, policies, environments, users, groups)
CREATE TABLE licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    policy_id UUID NOT NULL REFERENCES policies(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    group_id UUID REFERENCES groups(id) ON DELETE SET NULL,
    
    -- Basic information
    key VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'ACTIVE', -- ACTIVE, INACTIVE, EXPIRED, SUSPENDED, BANNED
    suspended BOOLEAN DEFAULT FALSE,
    protected BOOLEAN DEFAULT FALSE,
    
    -- Usage tracking
    uses INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE,
    
    -- Policy overrides (allow per-license customization)
    max_machines_override INTEGER,
    max_uses_override INTEGER,
    max_cores_override INTEGER,
    max_users_override INTEGER,
    max_processes_override INTEGER,
    max_activations_override INTEGER,
    max_deactivations_override INTEGER,
    
    -- Cached counts for performance
    machines_count INTEGER DEFAULT 0,
    machines_core_count INTEGER DEFAULT 0,
    license_users_count INTEGER DEFAULT 0,
    
    -- Event tracking (last occurrence timestamps)
    last_check_in_at TIMESTAMP WITH TIME ZONE,
    last_validated_at TIMESTAMP WITH TIME ZONE,
    last_validated_checksum VARCHAR(255),
    last_validated_version VARCHAR(255),
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    last_expiration_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_expiring_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for licenses
CREATE INDEX idx_licenses_account_id ON licenses(account_id);
CREATE INDEX idx_licenses_product_id ON licenses(product_id);
CREATE INDEX idx_licenses_policy_id ON licenses(policy_id);
CREATE INDEX idx_licenses_environment_id ON licenses(environment_id);
CREATE INDEX idx_licenses_user_id ON licenses(user_id);
CREATE INDEX idx_licenses_group_id ON licenses(group_id);
CREATE UNIQUE INDEX idx_licenses_key_per_account ON licenses(account_id, key) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_status ON licenses(status);
CREATE INDEX idx_licenses_suspended ON licenses(suspended);
CREATE INDEX idx_licenses_protected ON licenses(protected);
CREATE INDEX idx_licenses_expires_at ON licenses(expires_at);
CREATE INDEX idx_licenses_last_used ON licenses(last_used);
CREATE INDEX idx_licenses_last_validated_at ON licenses(last_validated_at);
CREATE INDEX idx_licenses_deleted_at ON licenses(deleted_at);
CREATE INDEX idx_licenses_created_at ON licenses(created_at);
CREATE INDEX idx_licenses_metadata_gin ON licenses USING gin(metadata);