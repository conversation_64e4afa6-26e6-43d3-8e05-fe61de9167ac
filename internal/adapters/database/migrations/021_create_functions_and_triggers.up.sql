-- Create database functions and triggers for automatic maintenance

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- <PERSON><PERSON> triggers for all tables with updated_at columns
CREATE TRIGGER update_plans_updated_at BEFORE UPDATE ON plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_environments_updated_at BEFORE UPDATE ON environments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_second_factors_updated_at BEFORE UPDATE ON second_factors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_groups_updated_at BEFORE UPDATE ON groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_entitlements_updated_at BEFORE UPDATE ON entitlements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_licenses_updated_at BEFORE UPDATE ON licenses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_machines_updated_at BEFORE UPDATE ON machines FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_machine_components_updated_at BEFORE UPDATE ON machine_components FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_machine_processes_updated_at BEFORE UPDATE ON machine_processes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tokens_updated_at BEFORE UPDATE ON tokens FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_webhook_endpoints_updated_at BEFORE UPDATE ON webhook_endpoints FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_webhook_events_updated_at BEFORE UPDATE ON webhook_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update license cached counts
CREATE OR REPLACE FUNCTION update_license_counts()
RETURNS TRIGGER AS $$
BEGIN
    -- Update machines_count for the affected license
    IF TG_TABLE_NAME = 'machines' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE licenses 
            SET machines_count = machines_count + 1,
                machines_core_count = machines_core_count + COALESCE(NEW.cores, 0)
            WHERE id = NEW.license_id;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE licenses 
            SET machines_count = machines_count - 1,
                machines_core_count = machines_core_count - COALESCE(OLD.cores, 0)
            WHERE id = OLD.license_id;
        ELSIF TG_OP = 'UPDATE' THEN
            -- Handle license_id change
            IF OLD.license_id != NEW.license_id THEN
                UPDATE licenses 
                SET machines_count = machines_count - 1,
                    machines_core_count = machines_core_count - COALESCE(OLD.cores, 0)
                WHERE id = OLD.license_id;
                
                UPDATE licenses 
                SET machines_count = machines_count + 1,
                    machines_core_count = machines_core_count + COALESCE(NEW.cores, 0)
                WHERE id = NEW.license_id;
            -- Handle cores change
            ELSIF COALESCE(OLD.cores, 0) != COALESCE(NEW.cores, 0) THEN
                UPDATE licenses 
                SET machines_core_count = machines_core_count - COALESCE(OLD.cores, 0) + COALESCE(NEW.cores, 0)
                WHERE id = NEW.license_id;
            END IF;
        END IF;
    END IF;
    
    -- Update license_users_count for license_users junction table
    IF TG_TABLE_NAME = 'license_users' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE licenses 
            SET license_users_count = license_users_count + 1
            WHERE id = NEW.license_id;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE licenses 
            SET license_users_count = license_users_count - 1
            WHERE id = OLD.license_id;
        END IF;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Create triggers for automatic count updates
CREATE TRIGGER update_license_machine_counts 
    AFTER INSERT OR UPDATE OR DELETE ON machines 
    FOR EACH ROW EXECUTE FUNCTION update_license_counts();

CREATE TRIGGER update_license_user_counts 
    AFTER INSERT OR DELETE ON license_users 
    FOR EACH ROW EXECUTE FUNCTION update_license_counts();

-- Function to update webhook endpoint statistics
CREATE OR REPLACE FUNCTION update_webhook_endpoint_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE webhook_endpoints 
        SET delivery_count = delivery_count + 1,
            last_delivery_at = NEW.created_at
        WHERE id = NEW.webhook_endpoint_id;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Update success/failure counts based on status change
        IF OLD.status != NEW.status THEN
            IF NEW.status = 'delivered' THEN
                UPDATE webhook_endpoints 
                SET success_count = success_count + 1,
                    last_success_at = NEW.delivered_at
                WHERE id = NEW.webhook_endpoint_id;
            ELSIF NEW.status = 'failed' THEN
                UPDATE webhook_endpoints 
                SET failure_count = failure_count + 1,
                    last_failure_at = NEW.failed_at
                WHERE id = NEW.webhook_endpoint_id;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for webhook statistics
CREATE TRIGGER update_webhook_stats 
    AFTER INSERT OR UPDATE ON webhook_events 
    FOR EACH ROW EXECUTE FUNCTION update_webhook_endpoint_stats();