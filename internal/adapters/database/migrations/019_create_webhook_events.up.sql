-- Create webhook_events table (references accounts, environments, webhook_endpoints)
CREATE TABLE webhook_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    webhook_endpoint_id UUID NOT NULL REFERENCES webhook_endpoints(id) ON DELETE CASCADE,
    
    -- Event information
    event VARCHAR(255) NOT NULL, -- event type name
    data JSONB DEFAULT '{}', -- event data
    payload JSONB DEFAULT '{}', -- webhook payload
    headers JSONB DEFAULT '{}', -- request headers
    
    -- Delivery tracking
    status VARCHAR(50) DEFAULT 'pending', -- pending, delivered, failed, retrying
    attempt_count INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    
    -- Response tracking
    last_response_status INTEGER,
    last_response_body TEXT,
    last_error_message TEXT,
    
    -- Timing
    delivered_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for webhook_events
CREATE INDEX idx_webhook_events_account_id ON webhook_events(account_id);
CREATE INDEX idx_webhook_events_environment_id ON webhook_events(environment_id);
CREATE INDEX idx_webhook_events_webhook_endpoint_id ON webhook_events(webhook_endpoint_id);
CREATE INDEX idx_webhook_events_event ON webhook_events(event);
CREATE INDEX idx_webhook_events_status ON webhook_events(status);
CREATE INDEX idx_webhook_events_next_retry_at ON webhook_events(next_retry_at);
CREATE INDEX idx_webhook_events_last_attempt_at ON webhook_events(last_attempt_at);
CREATE INDEX idx_webhook_events_delivered_at ON webhook_events(delivered_at);
CREATE INDEX idx_webhook_events_failed_at ON webhook_events(failed_at);
CREATE INDEX idx_webhook_events_deleted_at ON webhook_events(deleted_at);
CREATE INDEX idx_webhook_events_created_at ON webhook_events(created_at);
CREATE INDEX idx_webhook_events_data_gin ON webhook_events USING gin(data);
CREATE INDEX idx_webhook_events_payload_gin ON webhook_events USING gin(payload);
CREATE INDEX idx_webhook_events_headers_gin ON webhook_events USING gin(headers);