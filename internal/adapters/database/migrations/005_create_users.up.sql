-- Create users table (references accounts and environments)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    
    -- Basic information
    email VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    password_digest TEXT, -- bcrypt hashed password
    role VARCHAR(50) DEFAULT 'user', -- admin, user, developer, read-only
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, suspended
    
    -- Password reset
    password_reset_token VARCHAR(255),
    password_reset_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Management
    group_id UUID, -- will be set after groups table is created
    banned_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    
    -- SSO integration
    sso_profile_id VARCHAR(255),
    sso_idp_id VARCHAR(255),
    sso_connection_id VARCHAR(255),
    
    -- Notifications
    stdout_unsubscribed_at TIMESTAMP WITH TIME ZONE,
    stdout_last_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for users
CREATE INDEX idx_users_account_id ON users(account_id);
CREATE INDEX idx_users_environment_id ON users(environment_id);
CREATE UNIQUE INDEX idx_users_email_per_account ON users(account_id, email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_group_id ON users(group_id);
CREATE INDEX idx_users_banned_at ON users(banned_at);
CREATE INDEX idx_users_last_login ON users(last_login);
CREATE INDEX idx_users_password_reset_token ON users(password_reset_token);
CREATE INDEX idx_users_sso_profile_id ON users(sso_profile_id);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_metadata_gin ON users USING gin(metadata);