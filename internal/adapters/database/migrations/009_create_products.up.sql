-- Create products table (references accounts and environments)
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    
    -- Basic information
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    key VARCHAR(255), -- legacy field, globally unique
    description TEXT,
    url VARCHAR(500),
    
    -- Platform configuration
    platforms JSONB DEFAULT '[]', -- array of platform names
    distribution_strategy VARCHAR(100) DEFAULT 'licensed', -- licensed, open, closed
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for products
CREATE INDEX idx_products_account_id ON products(account_id);
CREATE INDEX idx_products_environment_id ON products(environment_id);
CREATE UNIQUE INDEX idx_products_code_per_account ON products(account_id, code) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX idx_products_key_global ON products(key) WHERE key IS NOT NULL AND deleted_at IS NULL;
CREATE INDEX idx_products_distribution_strategy ON products(distribution_strategy);
CREATE INDEX idx_products_deleted_at ON products(deleted_at);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_platforms_gin ON products USING gin(platforms);
CREATE INDEX idx_products_metadata_gin ON products USING gin(metadata);