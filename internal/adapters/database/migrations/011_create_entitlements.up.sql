-- Create entitlements table (references accounts and environments)
CREATE TABLE entitlements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    
    -- Basic information
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for entitlements
CREATE INDEX idx_entitlements_account_id ON entitlements(account_id);
CREATE INDEX idx_entitlements_environment_id ON entitlements(environment_id);
CREATE UNIQUE INDEX idx_entitlements_code_per_account ON entitlements(account_id, code) WHERE deleted_at IS NULL;
CREATE INDEX idx_entitlements_deleted_at ON entitlements(deleted_at);
CREATE INDEX idx_entitlements_created_at ON entitlements(created_at);
CREATE INDEX idx_entitlements_metadata_gin ON entitlements USING gin(metadata);