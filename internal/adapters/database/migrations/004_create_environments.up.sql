-- Create environments table (references accounts)
CREATE TABLE environments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    
    -- Basic information
    name VARCHAR(255) NOT NULL,
    code VA<PERSON>HAR(255) NOT NULL,
    
    -- Isolation strategy: global, shared, isolated
    isolation_strategy VARCHAR(50) DEFAULT 'global',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for environments
CREATE INDEX idx_environments_account_id ON environments(account_id);
CREATE UNIQUE INDEX idx_environments_code_per_account ON environments(account_id, code) WHERE deleted_at IS NULL;
CREATE INDEX idx_environments_isolation_strategy ON environments(isolation_strategy);
CREATE INDEX idx_environments_deleted_at ON environments(deleted_at);
CREATE INDEX idx_environments_created_at ON environments(created_at);