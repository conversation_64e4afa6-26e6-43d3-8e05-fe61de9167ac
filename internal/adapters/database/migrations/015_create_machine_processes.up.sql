-- Create machine_processes table (references accounts, environments, machines)
CREATE TABLE machine_processes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    machine_id UUID NOT NULL REFERENCES machines(id) ON DELETE CASCADE,
    
    -- Identity
    pid INTEGER NOT NULL, -- Process ID
    
    -- Heartbeat tracking
    last_heartbeat_at TIMESTAMP WITH TIME ZONE,
    last_death_event_sent_at TIMESTAMP WITH TIME ZONE,
    heartbeat_jid VARCHAR(255), -- background job ID
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for machine_processes
CREATE INDEX idx_machine_processes_account_id ON machine_processes(account_id);
CREATE INDEX idx_machine_processes_environment_id ON machine_processes(environment_id);
CREATE INDEX idx_machine_processes_machine_id ON machine_processes(machine_id);
CREATE UNIQUE INDEX idx_machine_processes_pid_per_machine ON machine_processes(machine_id, pid) WHERE deleted_at IS NULL;
CREATE INDEX idx_machine_processes_last_heartbeat_at ON machine_processes(last_heartbeat_at);
CREATE INDEX idx_machine_processes_heartbeat_jid ON machine_processes(heartbeat_jid);
CREATE INDEX idx_machine_processes_deleted_at ON machine_processes(deleted_at);
CREATE INDEX idx_machine_processes_created_at ON machine_processes(created_at);
CREATE INDEX idx_machine_processes_metadata_gin ON machine_processes USING gin(metadata);