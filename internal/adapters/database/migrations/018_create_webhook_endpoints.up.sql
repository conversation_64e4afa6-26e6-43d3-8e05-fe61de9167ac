-- Create webhook_endpoints table (references accounts and environments)
CREATE TABLE webhook_endpoints (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    
    -- Configuration
    name VA<PERSON>HAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    events TEXT[] DEFAULT '{}', -- array of event names
    secret TEXT, -- webhook signing secret
    headers JSONB DEFAULT '{}', -- custom headers
    
    -- Status and retry configuration
    enabled BOOLEAN DEFAULT TRUE,
    max_retries INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 5, -- in seconds
    
    -- Statistics
    last_delivery_at TIMESTAMP WITH TIME ZONE,
    last_success_at TIMESTAMP WITH TIME ZONE,
    last_failure_at TIMESTAMP WITH TIME ZONE,
    delivery_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for webhook_endpoints
CREATE INDEX idx_webhook_endpoints_account_id ON webhook_endpoints(account_id);
CREATE INDEX idx_webhook_endpoints_environment_id ON webhook_endpoints(environment_id);
CREATE INDEX idx_webhook_endpoints_enabled ON webhook_endpoints(enabled);
CREATE INDEX idx_webhook_endpoints_last_delivery_at ON webhook_endpoints(last_delivery_at);
CREATE INDEX idx_webhook_endpoints_last_success_at ON webhook_endpoints(last_success_at);
CREATE INDEX idx_webhook_endpoints_last_failure_at ON webhook_endpoints(last_failure_at);
CREATE INDEX idx_webhook_endpoints_deleted_at ON webhook_endpoints(deleted_at);
CREATE INDEX idx_webhook_endpoints_created_at ON webhook_endpoints(created_at);
CREATE INDEX idx_webhook_endpoints_events_gin ON webhook_endpoints USING gin(events);
CREATE INDEX idx_webhook_endpoints_headers_gin ON webhook_endpoints USING gin(headers);