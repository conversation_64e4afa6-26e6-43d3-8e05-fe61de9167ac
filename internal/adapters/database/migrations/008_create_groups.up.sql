-- Create groups table (references accounts and environments)
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    
    -- Basic information
    name VARCHAR(255) NOT NULL,
    
    -- Limits
    max_users INTEGER,
    max_licenses INTEGER,
    max_machines INTEGER,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for groups
CREATE INDEX idx_groups_account_id ON groups(account_id);
CREATE INDEX idx_groups_environment_id ON groups(environment_id);
CREATE INDEX idx_groups_name ON groups(name);
CREATE INDEX idx_groups_deleted_at ON groups(deleted_at);
CREATE INDEX idx_groups_created_at ON groups(created_at);
CREATE INDEX idx_groups_metadata_gin ON groups USING gin(metadata);

-- Now add the foreign key constraint to users table
ALTER TABLE users ADD CONSTRAINT fk_users_group_id FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE SET NULL;