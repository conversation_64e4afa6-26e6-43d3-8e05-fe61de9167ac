-- Create roles table (references accounts)
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    
    -- Basic information
    name VARCHAR(255) NOT NULL,
    
    -- Polymorphic resource association
    resource_type VARCHAR(100), -- e.g., 'Account', 'Product', 'License'
    resource_id UUID,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for roles
CREATE INDEX idx_roles_account_id ON roles(account_id);
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_resource_type ON roles(resource_type);
CREATE INDEX idx_roles_resource_id ON roles(resource_id);
CREATE INDEX idx_roles_polymorphic ON roles(resource_type, resource_id);
CREATE INDEX idx_roles_deleted_at ON roles(deleted_at);
CREATE INDEX idx_roles_created_at ON roles(created_at);