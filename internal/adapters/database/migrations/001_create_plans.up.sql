-- Create plans table (independent entity)
CREATE TABLE plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    plan_id VARCHAR(255) UNIQUE, -- External billing system ID
    price INTEGER DEFAULT 0, -- Price in cents
    interval VARCHAR(50), -- billing interval (month, year, etc.)
    private BOOLEAN DEFAULT FALSE,
    
    -- Limits
    max_users INTEGER,
    max_admins INTEGER,
    max_policies INTEGER,
    max_licenses INTEGER,
    max_products INTEGER,
    max_reqs INTEGER,
    
    -- Trial
    trial_duration INTEGER, -- in seconds
    
    -- Retention
    request_log_retention_duration INTEGER, -- in seconds
    event_log_retention_duration INTEGER, -- in seconds
    
    -- Storage
    max_storage BIGINT, -- in bytes
    max_transfer BIGINT, -- in bytes
    max_upload BIGINT, -- in bytes
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for plans
CREATE INDEX idx_plans_plan_id ON plans(plan_id);
CREATE INDEX idx_plans_private ON plans(private);
CREATE INDEX idx_plans_created_at ON plans(created_at);