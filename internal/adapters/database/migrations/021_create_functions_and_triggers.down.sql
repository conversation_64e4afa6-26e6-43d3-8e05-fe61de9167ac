-- Drop all triggers
DROP TRIGGER IF EXISTS update_webhook_stats ON webhook_events;
DROP TRIGGER IF EXISTS update_license_user_counts ON license_users;
DROP TRIGGER IF EXISTS update_license_machine_counts ON machines;
DROP TRIGGER IF EXISTS update_webhook_events_updated_at ON webhook_events;
DROP TRIGGER IF EXISTS update_webhook_endpoints_updated_at ON webhook_endpoints;
DROP TRIGGER IF EXISTS update_sessions_updated_at ON sessions;
DROP TRIGGER IF EXISTS update_tokens_updated_at ON tokens;
DROP TRIGGER IF EXISTS update_machine_processes_updated_at ON machine_processes;
DROP TRIGGER IF EXISTS update_machine_components_updated_at ON machine_components;
DROP TRIGGER IF EXISTS update_machines_updated_at ON machines;
DROP TRIGGER IF EXISTS update_licenses_updated_at ON licenses;
DROP TRIGGER IF EXISTS update_entitlements_updated_at ON entitlements;
DROP TRIGGER IF EXISTS update_policies_updated_at ON policies;
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
DROP TRIGGER IF EXISTS update_groups_updated_at ON groups;
DROP TRIGGER IF EXISTS update_second_factors_updated_at ON second_factors;
DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_environments_updated_at ON environments;
DROP TRIGGER IF EXISTS update_accounts_updated_at ON accounts;
DROP TRIGGER IF EXISTS update_permissions_updated_at ON permissions;
DROP TRIGGER IF EXISTS update_plans_updated_at ON plans;

-- Drop all functions
DROP FUNCTION IF EXISTS update_webhook_endpoint_stats();
DROP FUNCTION IF EXISTS update_license_counts();
DROP FUNCTION IF EXISTS update_updated_at_column();