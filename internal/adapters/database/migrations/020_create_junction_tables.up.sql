-- Create many-to-many junction tables

-- License ↔ User relationships
CREATE TABLE license_users (
    license_id UUID NOT NULL REFERENCES licenses(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (license_id, user_id)
);

-- License ↔ Entitlement relationships
CREATE TABLE license_entitlements (
    license_id UUID NOT NULL REFERENCES licenses(id) ON DELETE CASCADE,
    entitlement_id UUID NOT NULL REFERENCES entitlements(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (license_id, entitlement_id)
);

-- Policy ↔ Entitlement relationships
CREATE TABLE policy_entitlements (
    policy_id UUID NOT NULL REFERENCES policies(id) ON DELETE CASCADE,
    entitlement_id UUID NOT NULL REFERENCES entitlements(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (policy_id, entitlement_id)
);

-- Group ↔ User (Owners) relationships
CREATE TABLE group_owners (
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (group_id, user_id)
);

-- Group ↔ Permission relationships
CREATE TABLE group_permissions (
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (group_id, permission_id)
);

-- Role ↔ Permission relationships
CREATE TABLE role_permissions (
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id)
);

-- Token ↔ Permission relationships
CREATE TABLE token_permissions (
    token_id UUID NOT NULL REFERENCES tokens(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (token_id, permission_id)
);

-- Indexes for junction tables
CREATE INDEX idx_license_users_license_id ON license_users(license_id);
CREATE INDEX idx_license_users_user_id ON license_users(user_id);

CREATE INDEX idx_license_entitlements_license_id ON license_entitlements(license_id);
CREATE INDEX idx_license_entitlements_entitlement_id ON license_entitlements(entitlement_id);

CREATE INDEX idx_policy_entitlements_policy_id ON policy_entitlements(policy_id);
CREATE INDEX idx_policy_entitlements_entitlement_id ON policy_entitlements(entitlement_id);

CREATE INDEX idx_group_owners_group_id ON group_owners(group_id);
CREATE INDEX idx_group_owners_user_id ON group_owners(user_id);

CREATE INDEX idx_group_permissions_group_id ON group_permissions(group_id);
CREATE INDEX idx_group_permissions_permission_id ON group_permissions(permission_id);

CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);

CREATE INDEX idx_token_permissions_token_id ON token_permissions(token_id);
CREATE INDEX idx_token_permissions_permission_id ON token_permissions(permission_id);