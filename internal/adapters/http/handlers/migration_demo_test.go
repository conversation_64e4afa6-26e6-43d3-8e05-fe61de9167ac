package handlers

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/gokeys/gokeys/internal/adapters/database/postgres"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"gorm.io/gorm"
)

// TestMigrationSystemDemo - Simple demo of migration system in tests
func TestMigrationSystemDemo(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping migration demo in short mode")
	}

	ctx := context.Background()

	t.Run("migration_system_basic_demo", func(t *testing.T) {
		// Setup database with real migration system
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Database setup failed: %v", err)
			return
		}

		t.Log("✅ Database setup successful with real migration system")

		// Verify key tables exist
		keyTables := []string{"accounts", "products", "policies", "licenses", "machines"}
		for _, table := range keyTables {
			var exists bool
			query := `SELECT EXISTS (
				SELECT FROM information_schema.tables 
				WHERE table_schema = 'public' AND table_name = ?
			)`
			err := db.Raw(query, table).Scan(&exists).Error
			require.NoError(t, err)
			assert.True(t, exists, "Table %s should exist", table)
		}

		t.Log("✅ All key tables verified to exist")

		// Test basic CRUD with migrated schema
		serviceCoordinator := createTestServiceCoordinatorWithDB(db)

		account := &entities.Account{
			Name:  "Migration Demo Account",
			Slug:  "migration-demo-" + uuid.New().String()[0:8],
			Email: "<EMAIL>",
		}
		err = serviceCoordinator.Repositories.Account().Create(ctx, account)
		require.NoError(t, err, "Should create account successfully")

		t.Logf("✅ Created account with ID: %s", account.ID)

		// Cleanup
		defer cleanupTestData(t, db, "accounts")
	})

	t.Run("isolated_migration_demo", func(t *testing.T) {
		// Demo isolated database with migration control
		withIsolatedMigrationTest(t, func(t *testing.T, db *gorm.DB, migrator *postgres.Migrator) {
			// Check migration status
			version, dirty, err := migrator.GetMigrationStatus()
			require.NoError(t, err)
			assert.False(t, dirty, "Database should not be dirty")

			t.Logf("✅ Migration status: version=%d, dirty=%v", version, dirty)

			// Quick functional test
			serviceCoordinator := createTestServiceCoordinatorWithDB(db)

			account := &entities.Account{
				Name:  "Isolated Demo Account",
				Slug:  "isolated-demo-" + uuid.New().String()[0:8],
				Email: "<EMAIL>",
			}
			err = serviceCoordinator.Repositories.Account().Create(ctx, account)
			require.NoError(t, err)

			t.Logf("✅ Isolated database test successful")
		})
	})

	t.Run("migration_vs_automigrate_comparison", func(t *testing.T) {
		// Show difference between migration and AutoMigrate
		t.Log("🔄 Using REAL MIGRATION SYSTEM (not AutoMigrate)")
		
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Database setup failed: %v", err)
			return
		}

		// Verify extensions are installed (only possible with real migrations)
		var extensionCount int
		query := `SELECT COUNT(*) FROM pg_extension WHERE extname IN ('uuid-ossp', 'pgcrypto', 'btree_gin')`
		err = db.Raw(query).Scan(&extensionCount).Error
		require.NoError(t, err)
		assert.GreaterOrEqual(t, extensionCount, 3, "Should have at least 3 extensions installed")

		// Verify functions exist (only created by real migrations)
		var functionCount int
		query = `SELECT COUNT(*) FROM pg_proc WHERE proname IN ('update_updated_at_column', 'update_license_counts')`
		err = db.Raw(query).Scan(&functionCount).Error
		require.NoError(t, err)
		assert.GreaterOrEqual(t, functionCount, 2, "Should have trigger functions")

		// Verify triggers exist (only created by real migrations)
		var triggerCount int
		query = `SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_name LIKE 'update_%_updated_at'`
		err = db.Raw(query).Scan(&triggerCount).Error
		require.NoError(t, err)
		assert.Greater(t, triggerCount, 10, "Should have many updated_at triggers")

		t.Logf("✅ Migration system verification:")
		t.Logf("   - Extensions installed: %d", extensionCount)
		t.Logf("   - Functions created: %d", functionCount)
		t.Logf("   - Triggers created: %d", triggerCount)
		t.Log("✅ This is only possible with REAL MIGRATION SYSTEM!")
	})
}

// TestMigrationSystemBasicWorkflow - Basic workflow test using migrations
func TestMigrationSystemBasicWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping migration workflow test in short mode")
	}

	ctx := context.Background()

	t.Run("complete_license_workflow_with_migrations", func(t *testing.T) {
		// Use migration system for complete workflow
		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Skipf("Service coordinator setup failed: %v", err)
			return
		}

		// Create complete test data
		testData, err := setupProductionTestDataWithRepositories(ctx, serviceCoordinator)
		require.NoError(t, err, "Should setup test data successfully")

		// Verify all relationships work
		assert.NotEmpty(t, testData.Account.ID)
		assert.NotEmpty(t, testData.Product.ID)
		assert.NotEmpty(t, testData.Policy.ID)
		assert.NotEmpty(t, testData.License.ID)

		// Test license retrieval
		retrievedLicense, err := serviceCoordinator.Repositories.License().GetByKey(ctx, testData.License.Key)
		require.NoError(t, err)
		assert.Equal(t, testData.License.Key, retrievedLicense.Key)

		t.Logf("✅ Complete workflow successful with migration system")
		t.Logf("   Account: %s", testData.Account.Name)
		t.Logf("   Product: %s", testData.Product.Name)
		t.Logf("   Policy: %s", testData.Policy.Name)
		t.Logf("   License: %s", testData.License.Key)

		// Cleanup (database will be cleaned automatically)
	})
} 