package handlers

import (
	"encoding/json"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAccountHandlerBasic tests basic account handler functionality without database
func TestAccountHandlerBasic(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("handler_creation", func(t *testing.T) {
		// Test that we can create the handler without panicking
		handler := NewAccountHandler(nil)
		assert.NotNil(t, handler)
	})

	t.Run("account_create_request_structure", func(t *testing.T) {
		// Test Go-style AccountCreateRequest structure
		requestBody := AccountCreateRequest{
			Name:  "Test Company",
			Slug:  "testcompany123",
			Email: "<EMAIL>",
			Metadata: map[string]interface{}{
				"plan":        "enterprise",
				"created_via": "api_test",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(requestBody)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "Test Company")
		assert.Contains(t, string(jsonData), "testcompany123")
		assert.Contains(t, string(jsonData), "<EMAIL>")

		// Test JSON unmarshaling
		var parsed AccountCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Test Company", parsed.Name)
		assert.Equal(t, "testcompany123", parsed.Slug)
		assert.Equal(t, "<EMAIL>", parsed.Email)
		assert.Equal(t, "enterprise", parsed.Metadata["plan"])
	})

	t.Run("account_update_request_structure", func(t *testing.T) {
		// Test Go-style AccountUpdateRequest structure
		newName := "Updated Company"
		newSlug := "updatedcompany123"

		requestBody := AccountUpdateRequest{
			Name: &newName,
			Slug: &newSlug,
			Metadata: map[string]interface{}{
				"updated_via": "api_test",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(requestBody)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "Updated Company")
		assert.Contains(t, string(jsonData), "updatedcompany123")

		// Test JSON unmarshaling
		var parsed AccountUpdateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.NotNil(t, parsed.Name)
		assert.Equal(t, "Updated Company", *parsed.Name)
		assert.NotNil(t, parsed.Slug)
		assert.Equal(t, "updatedcompany123", *parsed.Slug)
		assert.Equal(t, "api_test", parsed.Metadata["updated_via"])
	})

	t.Run("account_response_structure", func(t *testing.T) {
		// Test Go-style AccountResponse structure
		response := AccountResponse{
			ID:      "550e8400-e29b-41d4-a716-************",
			Name:    "Test Company",
			Slug:    "testcompany123",
			Email:   "<EMAIL>",
			Created: "2023-01-01T00:00:00Z",
			Updated: "2023-01-01T00:00:00Z",
			Metadata: map[string]interface{}{
				"plan": "enterprise",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(response)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "550e8400-e29b-41d4-a716-************")
		assert.Contains(t, string(jsonData), "Test Company")
		assert.Contains(t, string(jsonData), "testcompany123")

		// Test JSON unmarshaling
		var parsed AccountResponse
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "550e8400-e29b-41d4-a716-************", parsed.ID)
		assert.Equal(t, "Test Company", parsed.Name)
		assert.Equal(t, "testcompany123", parsed.Slug)
		assert.Equal(t, "<EMAIL>", parsed.Email)
		assert.Equal(t, "enterprise", parsed.Metadata["plan"])
	})
}

// TestAccountRequestValidation tests account request validation
func TestAccountRequestValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("valid_account_create_request", func(t *testing.T) {
		request := AccountCreateRequest{
			Name:  "Valid Company",
			Slug:  "validcompany123",
			Email: "<EMAIL>",
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed AccountCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Valid Company", parsed.Name)
		assert.Equal(t, "validcompany123", parsed.Slug)
		assert.Equal(t, "<EMAIL>", parsed.Email)
	})

	t.Run("account_create_request_with_metadata", func(t *testing.T) {
		request := AccountCreateRequest{
			Name:  "Company with Metadata",
			Slug:  "companywithmeta123",
			Email: "<EMAIL>",
			Metadata: map[string]interface{}{
				"plan":      "enterprise",
				"region":    "us-east-1",
				"max_users": 100,
				"features":  []string{"api", "webhooks", "analytics"},
			},
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed AccountCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Company with Metadata", parsed.Name)
		assert.Equal(t, "companywithmeta123", parsed.Slug)
		assert.Equal(t, "<EMAIL>", parsed.Email)
		assert.Equal(t, "enterprise", parsed.Metadata["plan"])
		assert.Equal(t, "us-east-1", parsed.Metadata["region"])
		assert.Equal(t, float64(100), parsed.Metadata["max_users"]) // JSON numbers become float64
	})

	t.Run("account_update_request_partial", func(t *testing.T) {
		// Test partial update - only name
		newName := "Updated Name Only"
		request := AccountUpdateRequest{
			Name: &newName,
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed AccountUpdateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.NotNil(t, parsed.Name)
		assert.Equal(t, "Updated Name Only", *parsed.Name)
		assert.Nil(t, parsed.Slug) // Should be nil since not provided
	})
}

// TestAccountListResponse tests account list response structure
func TestAccountListResponse(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("account_list_response_structure", func(t *testing.T) {
		accounts := []AccountResponse{
			{
				ID:      "550e8400-e29b-41d4-a716-************",
				Name:    "Company One",
				Slug:    "companyone123",
				Email:   "<EMAIL>",
				Created: "2023-01-01T00:00:00Z",
				Updated: "2023-01-01T00:00:00Z",
			},
			{
				ID:      "550e8400-e29b-41d4-a716-************",
				Name:    "Company Two",
				Slug:    "companytwo123",
				Email:   "<EMAIL>",
				Created: "2023-01-02T00:00:00Z",
				Updated: "2023-01-02T00:00:00Z",
			},
		}

		response := AccountListResponse{
			Accounts: accounts,
			Pagination: PaginationInfo{
				Page:       1,
				PerPage:    10,
				Total:      2,
				TotalPages: 1,
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(response)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "Company One")
		assert.Contains(t, string(jsonData), "Company Two")

		// Test JSON unmarshaling
		var parsed AccountListResponse
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Len(t, parsed.Accounts, 2)
		assert.Equal(t, int64(2), parsed.Pagination.Total)
		assert.Equal(t, 1, parsed.Pagination.Page)
		assert.Equal(t, 10, parsed.Pagination.PerPage)
		assert.Equal(t, "Company One", parsed.Accounts[0].Name)
		assert.Equal(t, "Company Two", parsed.Accounts[1].Name)
	})

	t.Run("empty_account_list_response", func(t *testing.T) {
		response := AccountListResponse{
			Accounts: []AccountResponse{},
			Pagination: PaginationInfo{
				Page:       1,
				PerPage:    10,
				Total:      0,
				TotalPages: 0,
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(response)
		require.NoError(t, err)

		// Test JSON unmarshaling
		var parsed AccountListResponse
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Len(t, parsed.Accounts, 0)
		assert.Equal(t, int64(0), parsed.Pagination.Total)
		assert.Equal(t, 1, parsed.Pagination.Page)
		assert.Equal(t, 10, parsed.Pagination.PerPage)
	})
}
