package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type PolicyHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewPolicyHandler(serviceCoordinator *services.ServiceCoordinator) *PolicyHandler {
	return &PolicyHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear
type PolicyCreateRequest struct {
	Name             string  `json:"name" binding:"required,min=1,max=255"`
	Description      string  `json:"description,omitempty"`
	ProductID        string  `json:"product_id" binding:"required,uuid"`
	EnvironmentID    *string `json:"environment_id,omitempty" binding:"omitempty,uuid"`
	Duration         *int    `json:"duration,omitempty" binding:"omitempty,min=0"`
	Strict           bool    `json:"strict"`
	Floating         bool    `json:"floating"`
	RequireHeartbeat bool    `json:"require_heartbeat"`

	// Machine limits
	MaxMachines  *int `json:"max_machines,omitempty" binding:"omitempty,min=0"`
	MaxProcesses *int `json:"max_processes,omitempty" binding:"omitempty,min=0"`
	MaxUsers     *int `json:"max_users,omitempty" binding:"omitempty,min=0"`
	MaxCores     *int `json:"max_cores,omitempty" binding:"omitempty,min=0"`
	MaxUses      *int `json:"max_uses,omitempty" binding:"omitempty,min=0"`

	// Advanced settings (simplified)
	Scheme                    string `json:"scheme,omitempty" binding:"omitempty,oneof=ED25519_SIGN RSA_PKCS1_SIGN RSA_PSS_SIGN"`
	HeartbeatDuration         *int   `json:"heartbeat_duration,omitempty" binding:"omitempty,min=0"`
	MachineUniquenessStrategy string `json:"machine_uniqueness_strategy,omitempty" binding:"omitempty,oneof=UNIQUE_PER_ACCOUNT UNIQUE_PER_PRODUCT UNIQUE_PER_POLICY UNIQUE_PER_LICENSE"`
	ExpirationStrategy        string `json:"expiration_strategy,omitempty" binding:"omitempty,oneof=RESTRICT_ACCESS REVOKE_ACCESS MAINTAIN_ACCESS"`
	OverageStrategy           string `json:"overage_strategy,omitempty" binding:"omitempty,oneof=NO_OVERAGE ALWAYS_ALLOW_OVERAGE ALLOW_1_25X_OVERAGE ALLOW_1_5X_OVERAGE ALLOW_2X_OVERAGE"`

	// Check-in settings
	RequireCheckIn       bool    `json:"require_check_in"`
	CheckInInterval      *string `json:"check_in_interval,omitempty"`
	CheckInIntervalCount *int    `json:"check_in_interval_count,omitempty" binding:"omitempty,min=0"`

	// Pool and activation limits
	UsePool          bool `json:"use_pool"`
	MaxActivations   *int `json:"max_activations,omitempty" binding:"omitempty,min=0"`
	MaxDeactivations *int `json:"max_deactivations,omitempty" binding:"omitempty,min=0"`

	Protected bool                   `json:"protected"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type PolicyUpdateRequest struct {
	Name             *string `json:"name,omitempty" binding:"omitempty,min=1,max=255"`
	Description      *string `json:"description,omitempty"`
	Duration         *int    `json:"duration,omitempty" binding:"omitempty,min=0"`
	Strict           *bool   `json:"strict,omitempty"`
	Floating         *bool   `json:"floating,omitempty"`
	RequireHeartbeat *bool   `json:"require_heartbeat,omitempty"`

	// Machine limits
	MaxMachines  *int `json:"max_machines,omitempty" binding:"omitempty,min=0"`
	MaxProcesses *int `json:"max_processes,omitempty" binding:"omitempty,min=0"`
	MaxUsers     *int `json:"max_users,omitempty" binding:"omitempty,min=0"`
	MaxCores     *int `json:"max_cores,omitempty" binding:"omitempty,min=0"`
	MaxUses      *int `json:"max_uses,omitempty" binding:"omitempty,min=0"`

	// Advanced settings
	Scheme                    *string `json:"scheme,omitempty" binding:"omitempty,oneof=ED25519_SIGN RSA_PKCS1_SIGN RSA_PSS_SIGN"`
	HeartbeatDuration         *int    `json:"heartbeat_duration,omitempty" binding:"omitempty,min=0"`
	MachineUniquenessStrategy *string `json:"machine_uniqueness_strategy,omitempty"`
	ExpirationStrategy        *string `json:"expiration_strategy,omitempty"`
	OverageStrategy           *string `json:"overage_strategy,omitempty"`

	// Check-in settings
	RequireCheckIn       *bool   `json:"require_check_in,omitempty"`
	CheckInInterval      *string `json:"check_in_interval,omitempty"`
	CheckInIntervalCount *int    `json:"check_in_interval_count,omitempty" binding:"omitempty,min=0"`

	// Pool and activation limits
	UsePool          *bool `json:"use_pool,omitempty"`
	MaxActivations   *int  `json:"max_activations,omitempty" binding:"omitempty,min=0"`
	MaxDeactivations *int  `json:"max_deactivations,omitempty" binding:"omitempty,min=0"`

	Protected *bool                  `json:"protected,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs
type PolicyResponse struct {
	ID                        string                 `json:"id"`
	Name                      string                 `json:"name"`
	Description               string                 `json:"description,omitempty"`
	ProductID                 string                 `json:"product_id"`
	EnvironmentID             *string                `json:"environment_id,omitempty"`
	Duration                  *int                   `json:"duration,omitempty"`
	Strict                    bool                   `json:"strict"`
	Floating                  bool                   `json:"floating"`
	RequireHeartbeat          bool                   `json:"require_heartbeat"`
	MaxMachines               *int                   `json:"max_machines,omitempty"`
	MaxProcesses              *int                   `json:"max_processes,omitempty"`
	MaxUsers                  *int                   `json:"max_users,omitempty"`
	MaxCores                  *int                   `json:"max_cores,omitempty"`
	MaxUses                   *int                   `json:"max_uses,omitempty"`
	Scheme                    string                 `json:"scheme,omitempty"`
	HeartbeatDuration         *int                   `json:"heartbeat_duration,omitempty"`
	MachineUniquenessStrategy string                 `json:"machine_uniqueness_strategy,omitempty"`
	ExpirationStrategy        string                 `json:"expiration_strategy,omitempty"`
	OverageStrategy           string                 `json:"overage_strategy,omitempty"`
	RequireCheckIn            bool                   `json:"require_check_in"`
	CheckInInterval           *string                `json:"check_in_interval,omitempty"`
	CheckInIntervalCount      *int                   `json:"check_in_interval_count,omitempty"`
	UsePool                   bool                   `json:"use_pool"`
	MaxActivations            *int                   `json:"max_activations,omitempty"`
	MaxDeactivations          *int                   `json:"max_deactivations,omitempty"`
	Protected                 bool                   `json:"protected"`
	Metadata                  map[string]interface{} `json:"metadata,omitempty"`
	Created                   string                 `json:"created_at"`
	Updated                   string                 `json:"updated_at"`
}

type PolicyListResponse struct {
	Policies   []PolicyResponse `json:"policies"`
	Pagination PaginationInfo   `json:"pagination"`
}

// Helper function to convert entity to response
func (h *PolicyHandler) toPolicyResponse(policy *entities.Policy) PolicyResponse {
	response := PolicyResponse{
		ID:                   policy.ID,
		Name:                 policy.Name,
		Description:          policy.Description,
		ProductID:            policy.ProductID,
		EnvironmentID:        policy.EnvironmentID,
		Duration:             policy.Duration,
		Strict:               policy.Strict,
		Floating:             policy.Floating,
		RequireHeartbeat:     policy.RequireHeartbeat,
		MaxMachines:          policy.MaxMachines,
		MaxProcesses:         policy.MaxProcesses,
		MaxUsers:             policy.MaxUsers,
		MaxCores:             policy.MaxCores,
		MaxUses:              policy.MaxUses,
		HeartbeatDuration:    policy.HeartbeatDuration,
		RequireCheckIn:       policy.RequireCheckIn,
		CheckInInterval:      policy.CheckInInterval,
		CheckInIntervalCount: policy.CheckInIntervalCount,
		UsePool:              policy.UsePool,
		MaxActivations:       policy.MaxActivations,
		MaxDeactivations:     policy.MaxDeactivations,
		Metadata:             policy.Metadata,
		Created:              policy.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:              policy.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Handle pointer fields
	if policy.Protected != nil {
		response.Protected = *policy.Protected
	}

	// Handle enum fields
	if policy.Scheme != "" {
		response.Scheme = string(policy.Scheme)
	}
	if policy.MachineUniquenessStrategy != nil && *policy.MachineUniquenessStrategy != "" {
		response.MachineUniquenessStrategy = *policy.MachineUniquenessStrategy
	}
	if policy.ExpirationStrategy != nil && *policy.ExpirationStrategy != "" {
		response.ExpirationStrategy = *policy.ExpirationStrategy
	}
	if policy.OverageStrategy != nil && *policy.OverageStrategy != "" {
		response.OverageStrategy = *policy.OverageStrategy
	}

	return response
}

// ListPolicies handles GET /api/v1/policies - Go-style approach
func (h *PolicyHandler) ListPolicies(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	search := c.Query("search")
	productID := c.Query("product_id")
	environmentID := c.Query("environment_id")

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    search,
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if productID != "" {
		if pid, err := uuid.Parse(productID); err == nil {
			filter.Filters["product_id"] = pid
		}
	}
	if environmentID != "" {
		if eid, err := uuid.Parse(environmentID); err == nil {
			filter.Filters["environment_id"] = eid
		}
	}

	// Get policies from repository with filters
	policies, total, err := h.serviceCoordinator.Repositories.Policy().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve policies",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	policyResponses := make([]PolicyResponse, len(policies))
	for i, policy := range policies {
		policyResponses[i] = h.toPolicyResponse(policy)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := PolicyListResponse{
		Policies: policyResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListPoliciesHandler - Legacy handler for backward compatibility
// Use ListPolicies instead
func (h *PolicyHandler) ListPoliciesHandler(c *gin.Context) {
	h.ListPolicies(c)
}

// GetPolicy handles GET /api/v1/policies/:id - Go-style approach
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid policy ID format",
		})
		return
	}

	// Get policy from repository by ID
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Return policy in Go-style
	response := h.toPolicyResponse(policy)
	c.JSON(http.StatusOK, response)
}

// GetPolicyHandler - Legacy handler for backward compatibility
// Use GetPolicy instead
func (h *PolicyHandler) GetPolicyHandler(c *gin.Context) {
	h.GetPolicy(c)
}

// CreatePolicy handles POST /api/v1/policies - Go-style approach
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req PolicyCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create policy entity
	policy := &entities.Policy{
		AccountID:            accountID.String(),
		ProductID:            req.ProductID,
		Name:                 req.Name,
		Description:          req.Description,
		Duration:             req.Duration,
		Strict:               req.Strict,
		Floating:             req.Floating,
		RequireHeartbeat:     req.RequireHeartbeat,
		MaxMachines:          req.MaxMachines,
		MaxProcesses:         req.MaxProcesses,
		MaxUsers:             req.MaxUsers,
		MaxCores:             req.MaxCores,
		MaxUses:              req.MaxUses,
		HeartbeatDuration:    req.HeartbeatDuration,
		RequireCheckIn:       req.RequireCheckIn,
		CheckInInterval:      req.CheckInInterval,
		CheckInIntervalCount: req.CheckInIntervalCount,
		UsePool:              req.UsePool,
		MaxActivations:       req.MaxActivations,
		MaxDeactivations:     req.MaxDeactivations,
		Protected:            &req.Protected,
		Metadata:             req.Metadata,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	// Set optional fields
	if req.EnvironmentID != nil {
		policy.EnvironmentID = req.EnvironmentID
	}

	// Set enum fields
	if req.Scheme != "" {
		policy.Scheme = entities.LicenseScheme(req.Scheme)
	} else {
		policy.Scheme = entities.Ed25519Sign // Default
	}

	if req.MachineUniquenessStrategy != "" {
		policy.MachineUniquenessStrategy = &req.MachineUniquenessStrategy
	}
	if req.ExpirationStrategy != "" {
		policy.ExpirationStrategy = &req.ExpirationStrategy
	}
	if req.OverageStrategy != "" {
		policy.OverageStrategy = &req.OverageStrategy
	}

	// Save policy to repository
	if err := h.serviceCoordinator.Repositories.Policy().Create(c.Request.Context(), policy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create policy",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyCreated,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	// Return created policy in Go-style
	response := h.toPolicyResponse(policy)
	c.Header("Location", "/api/v1/policies/"+policy.ID)
	c.JSON(http.StatusCreated, response)
}

// CreatePolicyHandler - Legacy handler for backward compatibility
// Use CreatePolicy instead
func (h *PolicyHandler) CreatePolicyHandler(c *gin.Context) {
	h.CreatePolicy(c)
}

// UpdatePolicy handles PUT /api/v1/policies/:id - Go-style approach
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid policy ID format",
		})
		return
	}

	var req PolicyUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing policy
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Policy not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		policy.Name = *req.Name
	}
	if req.Description != nil {
		policy.Description = *req.Description
	}
	if req.Duration != nil {
		policy.Duration = req.Duration
	}
	if req.Strict != nil {
		policy.Strict = *req.Strict
	}
	if req.Floating != nil {
		policy.Floating = *req.Floating
	}
	if req.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *req.RequireHeartbeat
	}
	if req.HeartbeatDuration != nil {
		policy.HeartbeatDuration = req.HeartbeatDuration
	}
	if req.RequireCheckIn != nil {
		policy.RequireCheckIn = *req.RequireCheckIn
	}
	if req.CheckInInterval != nil {
		policy.CheckInInterval = req.CheckInInterval
	}
	if req.CheckInIntervalCount != nil {
		policy.CheckInIntervalCount = req.CheckInIntervalCount
	}
	if req.UsePool != nil {
		policy.UsePool = *req.UsePool
	}
	if req.MaxMachines != nil {
		policy.MaxMachines = req.MaxMachines
	}
	if req.MaxProcesses != nil {
		policy.MaxProcesses = req.MaxProcesses
	}
	if req.MaxUsers != nil {
		policy.MaxUsers = req.MaxUsers
	}
	if req.MaxCores != nil {
		policy.MaxCores = req.MaxCores
	}
	if req.MaxUses != nil {
		policy.MaxUses = req.MaxUses
	}
	if req.MaxActivations != nil {
		policy.MaxActivations = req.MaxActivations
	}
	if req.MaxDeactivations != nil {
		policy.MaxDeactivations = req.MaxDeactivations
	}
	if req.Protected != nil {
		policy.Protected = req.Protected
	}
	if req.Metadata != nil {
		policy.Metadata = req.Metadata
	}

	// Update enum fields
	if req.Scheme != nil && *req.Scheme != "" {
		policy.Scheme = entities.LicenseScheme(*req.Scheme)
	}
	if req.MachineUniquenessStrategy != nil && *req.MachineUniquenessStrategy != "" {
		policy.MachineUniquenessStrategy = req.MachineUniquenessStrategy
	}
	if req.ExpirationStrategy != nil && *req.ExpirationStrategy != "" {
		policy.ExpirationStrategy = req.ExpirationStrategy
	}
	if req.OverageStrategy != nil && *req.OverageStrategy != "" {
		policy.OverageStrategy = req.OverageStrategy
	}
	policy.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Policy().Update(c.Request.Context(), policy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update policy",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyUpdated,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	// Return updated policy in Go-style
	response := h.toPolicyResponse(policy)
	c.JSON(http.StatusOK, response)
}

// UpdatePolicyHandler - Legacy handler for backward compatibility
// Use UpdatePolicy instead
func (h *PolicyHandler) UpdatePolicyHandler(c *gin.Context) {
	h.UpdatePolicy(c)
}

func (h *PolicyHandler) DeletePolicyHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid policy ID format")
		return
	}

	// Get policy from repository
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Delete policy
	if err := h.serviceCoordinator.Repositories.Policy().Delete(c.Request.Context(), policyID); err != nil {
		responses.RenderInternalError(c, "Failed to delete policy: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyDeleted,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
