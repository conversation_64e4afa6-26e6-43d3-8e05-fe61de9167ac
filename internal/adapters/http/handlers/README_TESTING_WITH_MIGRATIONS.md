# Testing with Migration System

Hệ thống test đã được cập nhật để sử dụng migration system thay vì tạo table thủ công. Đi<PERSON><PERSON> này đảm bảo tests sử dụng chính xác cùng database schema như production.

## ✅ **Những gì đã được cập nhật**

### **1. Migration-Based Testing Infrastructure**

**File mới: `testing_utils.go`**
- `setupTestDatabaseWithMigrations()` - Chạy migration một lần và setup database test
- `createTestServiceCoordinator()` - Tạo service coordinator thực với database đã migrate
- `cleanupTestData()` - Cleanup dữ liệu test sau khi chạy
- `withTestTransaction()` - Chạy test trong transaction tự động rollback

### **2. Updated Test Files**

**`license_production_test.go`**
- ✅ Sử dụng `setupTestDatabaseWithMigrations()` thay vì tạo table thủ công
- ✅ Sử dụng real ServiceCoordinator và handlers
- ✅ Test với database schema thực từ migrations

**`license_integration_test.go`**  
- ✅ Cập nhật để sử dụng migration system
- ✅ Sử dụng repositories thực thay vì mock data

**`migration_demo_test.go`** (mới)
- ✅ Demo cách sử dụng migration system trong tests
- ✅ Verify tất cả tables được tạo đúng từ migrations
- ✅ Test transaction-based testing pattern

### **3. Migration Tool Integration**

**`cmd/migrate/main.go`**
- ✅ Sử dụng config system chung của app
- ✅ Support YAML config và environment variables  
- ✅ Help command hiển thị config hiện tại

## 🚀 **Cách sử dụng**

### **Setup Database cho Testing**

```bash
# 1. Start PostgreSQL container
docker run -d --name gokeys-postgres \
  -p 5432:5432 \
  -e POSTGRES_USER=gokeys \
  -e POSTGRES_PASSWORD=gokeys_dev_password \
  -e POSTGRES_DB=gokeys_test \
  postgres:15

# 2. Run migrations
go run cmd/migrate/main.go -extensions
go run cmd/migrate/main.go -up

# 3. Run tests
go test ./internal/adapters/http/handlers -run TestMigrationSystemDemo -v
```

### **Automated Script**

```bash
# Chạy migrations và tests cùng lúc
./scripts/run-migrations-and-tests.sh
```

### **Writing Tests với Migration System**

```go
func TestMyHandler(t *testing.T) {
    // Setup database với migrations
    db, err := setupTestDatabaseWithMigrations(t)
    if err != nil {
        t.Skipf("Skipping test: %v", err)
        return
    }
    
    // Tạo service coordinator thực
    serviceCoordinator, err := createTestServiceCoordinator(t)
    require.NoError(t, err)
    
    // Cleanup sau test
    defer cleanupTestData(t, db)
    
    // Tạo handler với service coordinator thực
    handler := NewMyHandler(serviceCoordinator)
    
    // Test với database và services thực
    // ...
}
```

### **Transaction-Based Testing**

```go
func TestWithTransaction(t *testing.T) {
    withTestTransaction(t, func(t *testing.T, tx *gorm.DB) {
        // Tất cả database operations sẽ tự động rollback
        // Insert test data
        tx.Exec("INSERT INTO accounts ...")
        
        // Run tests
        // ...
        
        // Không cần cleanup - tự động rollback
    })
}
```

## 📋 **Benefits của Migration-Based Testing**

### **✅ Production Parity**
- Tests sử dụng chính xác same schema như production
- Constraints, indexes, triggers được test đúng
- Không có schema drift giữa test và production

### **✅ Real Integration Testing**
- Sử dụng ServiceCoordinator thực với repositories thực
- Test full workflow từ HTTP → Handler → Service → Repository → Database
- Catch integration issues sớm

### **✅ Maintainability**
- Một migration system cho cả development và testing
- Không cần maintain manual table creation code
- Schema changes tự động áp dụng cho tests

### **✅ Reliability**
- Tests fail nếu migration system có vấn đề
- Đảm bảo migration system luôn working
- Catch database-related issues trong CI/CD

## 🔧 **Configuration**

Tests sử dụng same config system như main app:

**YAML config (configs/config.yaml):**
```yaml
database:
  host: "localhost"
  port: 5432
  user: "gokeys"
  password: "gokeys_dev_password"
  dbname: "gokeys_test"  # Override cho tests
  sslmode: "disable"
```

**Environment variables:**
```bash
export DATABASE_HOST=localhost
export DATABASE_DBNAME=gokeys_test
```

## 🎯 **Test Patterns**

### **1. Skip Pattern (Recommended)**
```go
func TestMyHandler(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test in short mode")
    }
    
    db, err := setupTestDatabaseWithMigrations(t)
    if err != nil {
        t.Skipf("Skipping test: %v", err)
        return
    }
    // ...
}
```

### **2. Table Verification**
```go
// Verify migration tạo đúng tables
tables := []string{"accounts", "licenses", "machines"}
for _, table := range tables {
    var exists bool
    db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", table).Scan(&exists)
    assert.True(t, exists, "Table %s should exist", table)
}
```

### **3. Real Service Testing**
```go
// Test với services thực
serviceCoordinator, err := createTestServiceCoordinator(t)
require.NoError(t, err)

// Test repositories
account := &entities.Account{Name: "Test"}
err = serviceCoordinator.Repositories.Account().Create(ctx, account)
require.NoError(t, err)
```

## 🚨 **Important Notes**

1. **Database Required**: Tests skip gracefully nếu không connect được database
2. **Migration Path**: Migration files phải có đúng relative path `./../../../../internal/adapters/database/migrations`
3. **Test Database**: Sử dụng `gokeys_test` database riêng cho testing
4. **Cleanup**: Always cleanup test data để tránh conflicts giữa tests
5. **Transactions**: Sử dụng `withTestTransaction()` cho tests cần isolation

## 🎉 **Result**

Giờ đây tests:
- ✅ Sử dụng chính xác same database schema như production
- ✅ Test real workflows với ServiceCoordinator thực
- ✅ Catch integration issues sớm
- ✅ Maintainable và reliable
- ✅ Production-ready testing approach

**"phải integration full chứ như production thực tes"** ✅ **ACHIEVED!**