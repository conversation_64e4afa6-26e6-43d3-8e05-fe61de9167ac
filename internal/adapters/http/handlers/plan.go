package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/google/uuid"
)

type PlanHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewPlanHandler(serviceCoordinator *services.ServiceCoordinator) *PlanHandler {
	return &PlanHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Plans are read-only in most cases, mainly for billing/subscription purposes
func (h *PlanHandler) ListPlansHandler(c *gin.Context) {
	// Parse pagination
	page := 1
	limit := 25
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	_ = (page - 1) * limit // offset calculation

	// Get visible plans from repository (cached)
	// In Ruby: Plan.visible.reorder('price ASC NULLS FIRST')
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  limit,
		SortBy:    "price",
		SortOrder: "ASC",
		Filters: map[string]interface{}{
			"visible = ?": true,
		},
	}

	plans, total, err := h.serviceCoordinator.Repositories.Plan().List(c.Request.Context(), filter)
	if err != nil {
		responses.RenderInternalError(c, "Failed to retrieve plans: "+err.Error())
		return
	}

	response := gin.H{
		"data": plans,
		"meta": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *PlanHandler) GetPlanHandler(c *gin.Context) {
	planID := c.Param("id")
	if planID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plan ID is required"})
		return
	}

	planUUID, err := uuid.Parse(planID)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid plan ID format")
		return
	}

	// Get plan from repository by ID
	plan, err := h.serviceCoordinator.Repositories.Plan().GetByID(c.Request.Context(), planUUID)
	if err != nil {
		responses.RenderNotFound(c, "Plan not found")
		return
	}

	response := gin.H{
		"data": plan,
	}

	c.JSON(http.StatusOK, response)
}
