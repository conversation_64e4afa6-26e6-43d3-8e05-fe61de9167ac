package handlers

import (
	"encoding/json"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPolicyHandlerBasic tests basic policy handler functionality without database
func TestPolicyHandlerBasic(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("handler_creation", func(t *testing.T) {
		// Test that we can create the handler without panicking
		handler := NewPolicyHandler(nil)
		assert.NotNil(t, handler)
	})

	t.Run("policy_create_request_structure", func(t *testing.T) {
		// Test Go-style PolicyCreateRequest structure
		envID := "550e8400-e29b-41d4-a716-446655440000"
		duration := 86400 // 24 hours
		maxMachines := 5
		maxUsers := 10
		maxCores := 32
		heartbeatDuration := 300 // 5 minutes
		
		requestBody := PolicyCreateRequest{
			Name:                      "Test Policy",
			Description:               "A test policy for validation",
			ProductID:                 "550e8400-e29b-41d4-a716-************",
			EnvironmentID:             &envID,
			Duration:                  &duration,
			Strict:                    true,
			Floating:                  false,
			RequireHeartbeat:          true,
			MaxMachines:               &maxMachines,
			MaxUsers:                  &maxUsers,
			MaxCores:                  &maxCores,
			Scheme:                    "ED25519_SIGN",
			HeartbeatDuration:         &heartbeatDuration,
			MachineUniquenessStrategy: "UNIQUE_PER_LICENSE",
			ExpirationStrategy:        "RESTRICT_ACCESS",
			Metadata: map[string]interface{}{
				"tier":        "enterprise",
				"created_via": "api_test",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(requestBody)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "Test Policy")
		assert.Contains(t, string(jsonData), "550e8400-e29b-41d4-a716-************")
		assert.Contains(t, string(jsonData), "ED25519_SIGN")

		// Test JSON unmarshaling
		var parsed PolicyCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Test Policy", parsed.Name)
		assert.Equal(t, "A test policy for validation", parsed.Description)
		assert.Equal(t, "550e8400-e29b-41d4-a716-************", parsed.ProductID)
		assert.NotNil(t, parsed.EnvironmentID)
		assert.Equal(t, envID, *parsed.EnvironmentID)
		assert.NotNil(t, parsed.Duration)
		assert.Equal(t, 86400, *parsed.Duration)
		assert.True(t, parsed.Strict)
		assert.False(t, parsed.Floating)
		assert.True(t, parsed.RequireHeartbeat)
		assert.NotNil(t, parsed.MaxMachines)
		assert.Equal(t, 5, *parsed.MaxMachines)
		assert.NotNil(t, parsed.MaxUsers)
		assert.Equal(t, 10, *parsed.MaxUsers)
		assert.NotNil(t, parsed.MaxCores)
		assert.Equal(t, 32, *parsed.MaxCores)
		assert.Equal(t, "ED25519_SIGN", parsed.Scheme)
		assert.NotNil(t, parsed.HeartbeatDuration)
		assert.Equal(t, 300, *parsed.HeartbeatDuration)
		assert.Equal(t, "UNIQUE_PER_LICENSE", parsed.MachineUniquenessStrategy)
		assert.Equal(t, "RESTRICT_ACCESS", parsed.ExpirationStrategy)
		assert.Equal(t, "enterprise", parsed.Metadata["tier"])
	})

	t.Run("policy_update_request_structure", func(t *testing.T) {
		// Test Go-style PolicyUpdateRequest structure
		newName := "Updated Policy"
		newDescription := "Updated description"
		newDuration := 172800 // 48 hours
		newMaxMachines := 10
		newStrict := false
		
		requestBody := PolicyUpdateRequest{
			Name:        &newName,
			Description: &newDescription,
			Duration:    &newDuration,
			MaxMachines: &newMaxMachines,
			Strict:      &newStrict,
			Metadata: map[string]interface{}{
				"updated_via": "api_test",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(requestBody)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "Updated Policy")
		assert.Contains(t, string(jsonData), "Updated description")

		// Test JSON unmarshaling
		var parsed PolicyUpdateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.NotNil(t, parsed.Name)
		assert.Equal(t, "Updated Policy", *parsed.Name)
		assert.NotNil(t, parsed.Description)
		assert.Equal(t, "Updated description", *parsed.Description)
		assert.NotNil(t, parsed.Duration)
		assert.Equal(t, 172800, *parsed.Duration)
		assert.NotNil(t, parsed.MaxMachines)
		assert.Equal(t, 10, *parsed.MaxMachines)
		assert.NotNil(t, parsed.Strict)
		assert.False(t, *parsed.Strict)
		assert.Equal(t, "api_test", parsed.Metadata["updated_via"])
	})

	t.Run("policy_response_structure", func(t *testing.T) {
		// Test Go-style PolicyResponse structure
		response := PolicyResponse{
			ID:                        "550e8400-e29b-41d4-a716-446655440000",
			Name:                      "Test Policy",
			Description:               "A test policy",
			ProductID:                 "550e8400-e29b-41d4-a716-************",
			Duration:                  intPtr(86400),
			Strict:                    true,
			Floating:                  false,
			RequireHeartbeat:          true,
			MaxMachines:               intPtr(5),
			MaxUsers:                  intPtr(10),
			MaxCores:                  intPtr(32),
			Scheme:                    "ED25519_SIGN",
			HeartbeatDuration:         intPtr(300),
			MachineUniquenessStrategy: "UNIQUE_PER_LICENSE",
			ExpirationStrategy:        "RESTRICT_ACCESS",
			Created:                   "2023-01-01T00:00:00Z",
			Updated:                   "2023-01-01T00:00:00Z",
			Metadata: map[string]interface{}{
				"tier": "enterprise",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(response)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "550e8400-e29b-41d4-a716-446655440000")
		assert.Contains(t, string(jsonData), "Test Policy")
		assert.Contains(t, string(jsonData), "ED25519_SIGN")

		// Test JSON unmarshaling
		var parsed PolicyResponse
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", parsed.ID)
		assert.Equal(t, "Test Policy", parsed.Name)
		assert.Equal(t, "A test policy", parsed.Description)
		assert.Equal(t, "550e8400-e29b-41d4-a716-************", parsed.ProductID)
		assert.NotNil(t, parsed.Duration)
		assert.Equal(t, 86400, *parsed.Duration)
		assert.True(t, parsed.Strict)
		assert.False(t, parsed.Floating)
		assert.True(t, parsed.RequireHeartbeat)
		assert.Equal(t, "ED25519_SIGN", parsed.Scheme)
		assert.Equal(t, "UNIQUE_PER_LICENSE", parsed.MachineUniquenessStrategy)
		assert.Equal(t, "RESTRICT_ACCESS", parsed.ExpirationStrategy)
		assert.Equal(t, "enterprise", parsed.Metadata["tier"])
	})
}

// TestPolicyRequestValidation tests policy request validation
func TestPolicyRequestValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("valid_minimal_policy_create_request", func(t *testing.T) {
		request := PolicyCreateRequest{
			Name:      "Minimal Policy",
			ProductID: "550e8400-e29b-41d4-a716-************",
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed PolicyCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Minimal Policy", parsed.Name)
		assert.Equal(t, "550e8400-e29b-41d4-a716-************", parsed.ProductID)
		assert.False(t, parsed.Strict) // Default value
		assert.False(t, parsed.Floating) // Default value
		assert.False(t, parsed.RequireHeartbeat) // Default value
	})

	t.Run("policy_schemes", func(t *testing.T) {
		// Test valid schemes
		schemes := []string{"ED25519_SIGN", "RSA_PKCS1_SIGN", "RSA_PSS_SIGN"}
		
		for _, scheme := range schemes {
			request := PolicyCreateRequest{
				Name:      "Test Policy",
				ProductID: "550e8400-e29b-41d4-a716-************",
				Scheme:    scheme,
			}

			jsonData, err := json.Marshal(request)
			require.NoError(t, err)

			var parsed PolicyCreateRequest
			err = json.Unmarshal(jsonData, &parsed)
			require.NoError(t, err)
			assert.Equal(t, scheme, parsed.Scheme)
		}
	})

	t.Run("policy_machine_uniqueness_strategies", func(t *testing.T) {
		// Test valid machine uniqueness strategies
		strategies := []string{
			"UNIQUE_PER_ACCOUNT",
			"UNIQUE_PER_PRODUCT", 
			"UNIQUE_PER_POLICY",
			"UNIQUE_PER_LICENSE",
		}
		
		for _, strategy := range strategies {
			request := PolicyCreateRequest{
				Name:                      "Test Policy",
				ProductID:                 "550e8400-e29b-41d4-a716-************",
				MachineUniquenessStrategy: strategy,
			}

			jsonData, err := json.Marshal(request)
			require.NoError(t, err)

			var parsed PolicyCreateRequest
			err = json.Unmarshal(jsonData, &parsed)
			require.NoError(t, err)
			assert.Equal(t, strategy, parsed.MachineUniquenessStrategy)
		}
	})

	t.Run("policy_expiration_strategies", func(t *testing.T) {
		// Test valid expiration strategies
		strategies := []string{"RESTRICT_ACCESS", "REVOKE_ACCESS", "MAINTAIN_ACCESS"}
		
		for _, strategy := range strategies {
			request := PolicyCreateRequest{
				Name:               "Test Policy",
				ProductID:          "550e8400-e29b-41d4-a716-************",
				ExpirationStrategy: strategy,
			}

			jsonData, err := json.Marshal(request)
			require.NoError(t, err)

			var parsed PolicyCreateRequest
			err = json.Unmarshal(jsonData, &parsed)
			require.NoError(t, err)
			assert.Equal(t, strategy, parsed.ExpirationStrategy)
		}
	})

	t.Run("policy_limits", func(t *testing.T) {
		// Test various limit combinations
		maxMachines := 100
		maxProcesses := 500
		maxUsers := 50
		maxCores := 128
		maxUses := 1000
		
		request := PolicyCreateRequest{
			Name:         "Limits Policy",
			ProductID:    "550e8400-e29b-41d4-a716-************",
			MaxMachines:  &maxMachines,
			MaxProcesses: &maxProcesses,
			MaxUsers:     &maxUsers,
			MaxCores:     &maxCores,
			MaxUses:      &maxUses,
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed PolicyCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.NotNil(t, parsed.MaxMachines)
		assert.Equal(t, 100, *parsed.MaxMachines)
		assert.NotNil(t, parsed.MaxProcesses)
		assert.Equal(t, 500, *parsed.MaxProcesses)
		assert.NotNil(t, parsed.MaxUsers)
		assert.Equal(t, 50, *parsed.MaxUsers)
		assert.NotNil(t, parsed.MaxCores)
		assert.Equal(t, 128, *parsed.MaxCores)
		assert.NotNil(t, parsed.MaxUses)
		assert.Equal(t, 1000, *parsed.MaxUses)
	})

	t.Run("policy_update_request_partial", func(t *testing.T) {
		// Test partial update - only name and strict flag
		newName := "Updated Policy Name"
		newStrict := true
		request := PolicyUpdateRequest{
			Name:   &newName,
			Strict: &newStrict,
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed PolicyUpdateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.NotNil(t, parsed.Name)
		assert.Equal(t, "Updated Policy Name", *parsed.Name)
		assert.NotNil(t, parsed.Strict)
		assert.True(t, *parsed.Strict)
		assert.Nil(t, parsed.Description) // Should be nil since not provided
		assert.Nil(t, parsed.Duration)
		assert.Nil(t, parsed.MaxMachines)
	})
}
