package handlers

import (
	"encoding/json"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestProductHandlerBasic tests basic product handler functionality without database
func TestProductHandlerBasic(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("handler_creation", func(t *testing.T) {
		// Test that we can create the handler without panicking
		handler := NewProductHandler(nil)
		assert.NotNil(t, handler)
	})

	t.Run("product_create_request_structure", func(t *testing.T) {
		// Test Go-style ProductCreateRequest structure
		envID := "550e8400-e29b-41d4-a716-446655440000"
		requestBody := ProductCreateRequest{
			Name:                 "Test Software",
			Code:                 "testsoftware123",
			Description:          "A test software product",
			DistributionStrategy: "licensed",
			URL:                  "https://example.com/product",
			Platforms:            []string{"windows", "linux", "macos"},
			EnvironmentID:        &envID,
			Metadata: map[string]interface{}{
				"version":     "1.0.0",
				"created_via": "api_test",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(requestBody)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "Test Software")
		assert.Contains(t, string(jsonData), "testsoftware123")
		assert.Contains(t, string(jsonData), "licensed")

		// Test JSON unmarshaling
		var parsed ProductCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Test Software", parsed.Name)
		assert.Equal(t, "testsoftware123", parsed.Code)
		assert.Equal(t, "A test software product", parsed.Description)
		assert.Equal(t, "licensed", parsed.DistributionStrategy)
		assert.Equal(t, "https://example.com/product", parsed.URL)
		assert.Len(t, parsed.Platforms, 3)
		assert.Contains(t, parsed.Platforms, "windows")
		assert.Contains(t, parsed.Platforms, "linux")
		assert.Contains(t, parsed.Platforms, "macos")
		assert.NotNil(t, parsed.EnvironmentID)
		assert.Equal(t, envID, *parsed.EnvironmentID)
		assert.Equal(t, "1.0.0", parsed.Metadata["version"])
	})

	t.Run("product_update_request_structure", func(t *testing.T) {
		// Test Go-style ProductUpdateRequest structure
		newName := "Updated Software"
		newCode := "updatedsoftware123"
		newDescription := "Updated description"
		newStrategy := "open"
		newURL := "https://example.com/updated"
		
		requestBody := ProductUpdateRequest{
			Name:                 &newName,
			Code:                 &newCode,
			Description:          &newDescription,
			DistributionStrategy: &newStrategy,
			URL:                  &newURL,
			Platforms:            []string{"web", "mobile"},
			Metadata: map[string]interface{}{
				"updated_via": "api_test",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(requestBody)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "Updated Software")
		assert.Contains(t, string(jsonData), "updatedsoftware123")
		assert.Contains(t, string(jsonData), "open")

		// Test JSON unmarshaling
		var parsed ProductUpdateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.NotNil(t, parsed.Name)
		assert.Equal(t, "Updated Software", *parsed.Name)
		assert.NotNil(t, parsed.Code)
		assert.Equal(t, "updatedsoftware123", *parsed.Code)
		assert.NotNil(t, parsed.Description)
		assert.Equal(t, "Updated description", *parsed.Description)
		assert.NotNil(t, parsed.DistributionStrategy)
		assert.Equal(t, "open", *parsed.DistributionStrategy)
		assert.NotNil(t, parsed.URL)
		assert.Equal(t, "https://example.com/updated", *parsed.URL)
		assert.Len(t, parsed.Platforms, 2)
		assert.Contains(t, parsed.Platforms, "web")
		assert.Contains(t, parsed.Platforms, "mobile")
		assert.Equal(t, "api_test", parsed.Metadata["updated_via"])
	})

	t.Run("product_response_structure", func(t *testing.T) {
		// Test Go-style ProductResponse structure
		response := ProductResponse{
			ID:                   "550e8400-e29b-41d4-a716-446655440000",
			Name:                 "Test Software",
			Code:                 "testsoftware123",
			Description:          "A test software product",
			DistributionStrategy: "licensed",
			URL:                  "https://example.com/product",
			Platforms:            []string{"windows", "linux"},
			Created:              "2023-01-01T00:00:00Z",
			Updated:              "2023-01-01T00:00:00Z",
			Metadata: map[string]interface{}{
				"version": "1.0.0",
			},
		}

		// Test JSON marshaling
		jsonData, err := json.Marshal(response)
		require.NoError(t, err)
		assert.Contains(t, string(jsonData), "550e8400-e29b-41d4-a716-446655440000")
		assert.Contains(t, string(jsonData), "Test Software")
		assert.Contains(t, string(jsonData), "testsoftware123")

		// Test JSON unmarshaling
		var parsed ProductResponse
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", parsed.ID)
		assert.Equal(t, "Test Software", parsed.Name)
		assert.Equal(t, "testsoftware123", parsed.Code)
		assert.Equal(t, "A test software product", parsed.Description)
		assert.Equal(t, "licensed", parsed.DistributionStrategy)
		assert.Equal(t, "https://example.com/product", parsed.URL)
		assert.Len(t, parsed.Platforms, 2)
		assert.Equal(t, "1.0.0", parsed.Metadata["version"])
	})
}

// TestProductRequestValidation tests product request validation
func TestProductRequestValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("valid_product_create_request", func(t *testing.T) {
		request := ProductCreateRequest{
			Name: "Valid Product",
			Code: "validproduct123",
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed ProductCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Valid Product", parsed.Name)
		assert.Equal(t, "validproduct123", parsed.Code)
	})

	t.Run("product_create_request_with_all_fields", func(t *testing.T) {
		envID := "550e8400-e29b-41d4-a716-446655440000"
		request := ProductCreateRequest{
			Name:                 "Complete Product",
			Code:                 "completeproduct123",
			Description:          "A complete product with all fields",
			DistributionStrategy: "licensed",
			URL:                  "https://example.com/complete",
			Platforms:            []string{"windows", "linux", "macos", "web"},
			EnvironmentID:        &envID,
			Metadata: map[string]interface{}{
				"version":     "2.0.0",
				"category":    "enterprise",
				"max_users":   1000,
				"features":    []string{"api", "webhooks", "analytics", "reporting"},
			},
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed ProductCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Equal(t, "Complete Product", parsed.Name)
		assert.Equal(t, "completeproduct123", parsed.Code)
		assert.Equal(t, "A complete product with all fields", parsed.Description)
		assert.Equal(t, "licensed", parsed.DistributionStrategy)
		assert.Equal(t, "https://example.com/complete", parsed.URL)
		assert.Len(t, parsed.Platforms, 4)
		assert.NotNil(t, parsed.EnvironmentID)
		assert.Equal(t, envID, *parsed.EnvironmentID)
		assert.Equal(t, "2.0.0", parsed.Metadata["version"])
		assert.Equal(t, "enterprise", parsed.Metadata["category"])
		assert.Equal(t, float64(1000), parsed.Metadata["max_users"])
	})

	t.Run("product_update_request_partial", func(t *testing.T) {
		// Test partial update - only name and description
		newName := "Updated Product Name"
		newDescription := "Updated description only"
		request := ProductUpdateRequest{
			Name:        &newName,
			Description: &newDescription,
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed ProductUpdateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.NotNil(t, parsed.Name)
		assert.Equal(t, "Updated Product Name", *parsed.Name)
		assert.NotNil(t, parsed.Description)
		assert.Equal(t, "Updated description only", *parsed.Description)
		assert.Nil(t, parsed.Code) // Should be nil since not provided
		assert.Nil(t, parsed.DistributionStrategy)
		assert.Nil(t, parsed.URL)
	})

	t.Run("product_distribution_strategies", func(t *testing.T) {
		// Test valid distribution strategies
		strategies := []string{"licensed", "open"}
		
		for _, strategy := range strategies {
			request := ProductCreateRequest{
				Name:                 "Test Product",
				Code:                 "testproduct123",
				DistributionStrategy: strategy,
			}

			jsonData, err := json.Marshal(request)
			require.NoError(t, err)

			var parsed ProductCreateRequest
			err = json.Unmarshal(jsonData, &parsed)
			require.NoError(t, err)
			assert.Equal(t, strategy, parsed.DistributionStrategy)
		}
	})

	t.Run("product_platforms", func(t *testing.T) {
		// Test various platform combinations
		platforms := []string{"windows", "linux", "macos", "web", "mobile", "api"}
		
		request := ProductCreateRequest{
			Name:      "Multi-Platform Product",
			Code:      "multiplatform123",
			Platforms: platforms,
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		var parsed ProductCreateRequest
		err = json.Unmarshal(jsonData, &parsed)
		require.NoError(t, err)
		assert.Len(t, parsed.Platforms, 6)
		for _, platform := range platforms {
			assert.Contains(t, parsed.Platforms, platform)
		}
	})
}
