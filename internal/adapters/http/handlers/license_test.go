package handlers

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestLicenseHandlerBasic tests basic license handler functionality without database
func TestLicenseHandlerBasic(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("handler_creation", func(t *testing.T) {
		// Test that we can create the handler without panicking
		handler := NewLicenseHandler(nil)
		if handler == nil {
			t.Error("Expected handler to be created")
		}
	})

	t.Run("validate_request_structure", func(t *testing.T) {
		// Test ValidateLicenseRequest structure
		requestBody := ValidateLicenseRequest{
			LicenseKey:         "LIC-12345-ABCDE-67890-FGHIJ",
			MachineFingerprint: stringPtr("fp-mac-12345678"),
			Environment:        stringPtr("production"),
			MachineInfo: map[string]interface{}{
				"hostname": "server-01",
				"os":       "linux",
			},
		}

		body, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")

		assert.Equal(t, "POST", req.Method)
		assert.Equal(t, "/api/v1/licenses/validate", req.URL.Path)
		assert.Equal(t, "application/json", req.Header.Get("Content-Type"))
	})

	t.Run("json_parsing", func(t *testing.T) {
		// Test that our request structure can be parsed
		jsonStr := `{
			"license_key": "LIC-12345-ABCDE-67890-FGHIJ",
			"machine_fingerprint": "fp-mac-12345678",
			"environment": "production",
			"machine_info": {
				"hostname": "server-01",
				"os": "linux",
				"cpu": "x86_64"
			}
		}`

		var requestBody ValidateLicenseRequest
		err := json.Unmarshal([]byte(jsonStr), &requestBody)
		require.NoError(t, err)

		assert.Equal(t, "LIC-12345-ABCDE-67890-FGHIJ", requestBody.LicenseKey)
		assert.Equal(t, "fp-mac-12345678", *requestBody.MachineFingerprint)
		assert.Equal(t, "production", *requestBody.Environment)
		assert.Equal(t, "server-01", requestBody.MachineInfo["hostname"])
	})

	t.Run("response_structure", func(t *testing.T) {
		// Test ValidateLicenseResponse structure
		response := ValidateLicenseResponse{
			Valid:           true,
			ValidationTime:  mustParseTime("2025-07-12T16:15:30Z"),
			MachinesUsed:    2,
			MachinesAllowed: 5,
			Claims: map[string]interface{}{
				"feature_a": true,
				"feature_b": false,
			},
		}

		body, err := json.Marshal(response)
		require.NoError(t, err)

		var parsed ValidateLicenseResponse
		err = json.Unmarshal(body, &parsed)
		require.NoError(t, err)

		assert.Equal(t, true, parsed.Valid)
		assert.Equal(t, 2, parsed.MachinesUsed)
		assert.Equal(t, 5, parsed.MachinesAllowed)
		assert.NotNil(t, parsed.Claims)
	})
}

// TestLicenseRequestValidation tests license request validation
func TestLicenseRequestValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("valid_license_request", func(t *testing.T) {
		jsonStr := `{
			"license_key": "LIC-12345-ABCDE-67890-FGHIJ",
			"machine_fingerprint": "fp-mac-12345678",
			"environment": "production"
		}`

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		req := httptest.NewRequest("POST", "/", bytes.NewBufferString(jsonStr))
		req.Header.Set("Content-Type", "application/json")
		c.Request = req

		var request ValidateLicenseRequest
		err := c.ShouldBindJSON(&request)

		require.NoError(t, err)
		assert.Equal(t, "LIC-12345-ABCDE-67890-FGHIJ", request.LicenseKey)
		assert.NotNil(t, request.MachineFingerprint)
		assert.Equal(t, "fp-mac-12345678", *request.MachineFingerprint)
	})

	t.Run("missing_required_fields", func(t *testing.T) {
		testCases := []struct {
			name        string
			jsonStr     string
			shouldError bool
		}{
			{
				name: "missing_license_key",
				jsonStr: `{
					"machine_fingerprint": "fp-mac-12345678",
					"environment": "production"
				}`,
				shouldError: true,
			},
			{
				name: "empty_license_key",
				jsonStr: `{
					"license_key": "",
					"machine_fingerprint": "fp-mac-12345678"
				}`,
				shouldError: true,
			},
			{
				name: "valid_minimal",
				jsonStr: `{
					"license_key": "LIC-12345-ABCDE-67890-FGHIJ"
				}`,
				shouldError: false,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				req := httptest.NewRequest("POST", "/", bytes.NewBufferString(tc.jsonStr))
				req.Header.Set("Content-Type", "application/json")
				c.Request = req

				var request ValidateLicenseRequest
				err := c.ShouldBindJSON(&request)

				if tc.shouldError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			})
		}
	})
}

// TestLicenseHandlerStructures tests license handler request/response structures
func TestLicenseHandlerStructures(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("validate_license_request_binding", func(t *testing.T) {
		// Test complete license validation request structure
		requestBody := ValidateLicenseRequest{
			LicenseKey:         "LIC-12345-ABCDE-67890-FGHIJ",
			MachineFingerprint: stringPtr("fp-mac-12345678"),
			Environment:        stringPtr("production"),
			MachineInfo: map[string]interface{}{
				"hostname": "server-01",
				"os":       "linux",
				"memory":   "8GB",
				"cpu":      "x86_64",
			},
		}

		body, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request: %v", err)
		}

		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create basic gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Test request parsing with Gin binding
		var parsedRequest ValidateLicenseRequest
		err = c.ShouldBindJSON(&parsedRequest)
		if err != nil {
			t.Fatalf("Failed to parse request: %v", err)
		}

		if parsedRequest.LicenseKey != "LIC-12345-ABCDE-67890-FGHIJ" {
			t.Errorf("Expected license key 'LIC-12345-ABCDE-67890-FGHIJ', got %s", parsedRequest.LicenseKey)
		}
		if *parsedRequest.MachineFingerprint != "fp-mac-12345678" {
			t.Errorf("Expected machine fingerprint 'fp-mac-12345678', got %s", *parsedRequest.MachineFingerprint)
		}
		if *parsedRequest.Environment != "production" {
			t.Errorf("Expected environment 'production', got %s", *parsedRequest.Environment)
		}
		if parsedRequest.MachineInfo["hostname"] != "server-01" {
			t.Errorf("Expected hostname 'server-01', got %v", parsedRequest.MachineInfo["hostname"])
		}
	})

	t.Run("validate_license_response_structure", func(t *testing.T) {
		// Test license validation response structure
		expiresAt := mustParseTime("2025-12-31T23:59:59Z")

		response := ValidateLicenseResponse{
			Valid:           true,
			ValidationTime:  mustParseTime("2025-07-12T16:15:30Z"),
			ExpiresAt:       &expiresAt,
			MachinesUsed:    3,
			MachinesAllowed: 10,
			Claims: map[string]interface{}{
				"feature_premium": true,
				"feature_basic":   true,
				"max_users":       100,
			},
			Errors:   []string{},
			Warnings: []string{"License expires in 30 days"},
		}

		// Test JSON marshaling
		body, err := json.Marshal(response)
		if err != nil {
			t.Fatalf("Failed to marshal response: %v", err)
		}

		// Test JSON unmarshaling
		var parsed ValidateLicenseResponse
		err = json.Unmarshal(body, &parsed)
		if err != nil {
			t.Fatalf("Failed to unmarshal response: %v", err)
		}

		if !parsed.Valid {
			t.Error("Expected valid license")
		}
		if parsed.MachinesUsed != 3 {
			t.Errorf("Expected 3 machines used, got %d", parsed.MachinesUsed)
		}
		if parsed.MachinesAllowed != 10 {
			t.Errorf("Expected 10 machines allowed, got %d", parsed.MachinesAllowed)
		}
		if parsed.ExpiresAt == nil {
			t.Error("Expected expires at time")
		}
		if parsed.Claims["feature_premium"] != true {
			t.Error("Expected premium feature claim")
		}
		if len(parsed.Warnings) != 1 {
			t.Errorf("Expected 1 warning, got %d", len(parsed.Warnings))
		}
	})

	t.Run("handler_creation_without_service", func(t *testing.T) {
		// Test that we can create the handler (even with nil service coordinator for structure testing)
		handler := NewLicenseHandler(nil)
		if handler == nil {
			t.Error("Expected handler to be created")
		}
	})
}

// Helper functions are now in test_helpers.go

func mustParseTime(timeStr string) time.Time {
	t, err := time.Parse("2006-01-02T15:04:05Z", timeStr)
	if err != nil {
		panic(err)
	}
	return t
}

// TestLicenseValidationResponse tests license validation response structure
func TestLicenseValidationResponse(t *testing.T) {
	t.Run("complete_validation_response", func(t *testing.T) {
		expiresAt := mustParseTime("2025-12-31T23:59:59Z")

		response := ValidateLicenseResponse{
			Valid:           true,
			ValidationTime:  mustParseTime("2025-07-12T16:15:30Z"),
			ExpiresAt:       &expiresAt,
			MachinesUsed:    3,
			MachinesAllowed: 10,
			Claims: map[string]interface{}{
				"feature_premium": true,
				"feature_basic":   true,
				"max_users":       100,
			},
			Errors:   []string{},
			Warnings: []string{"License expires in 30 days"},
		}

		// Test JSON marshaling
		body, err := json.Marshal(response)
		require.NoError(t, err)

		// Test JSON unmarshaling
		var parsed ValidateLicenseResponse
		err = json.Unmarshal(body, &parsed)
		require.NoError(t, err)

		assert.Equal(t, true, parsed.Valid)
		assert.Equal(t, 3, parsed.MachinesUsed)
		assert.Equal(t, 10, parsed.MachinesAllowed)
		assert.NotNil(t, parsed.ExpiresAt)
		assert.Equal(t, true, parsed.Claims["feature_premium"])
		assert.Len(t, parsed.Warnings, 1)
	})

	t.Run("failed_validation_response", func(t *testing.T) {
		response := ValidateLicenseResponse{
			Valid:          false,
			ValidationTime: mustParseTime("2025-07-12T16:15:30Z"),
			Errors: []string{
				"License key not found",
				"Invalid license format",
			},
		}

		body, err := json.Marshal(response)
		require.NoError(t, err)

		var parsed ValidateLicenseResponse
		err = json.Unmarshal(body, &parsed)
		require.NoError(t, err)

		assert.Equal(t, false, parsed.Valid)
		assert.Len(t, parsed.Errors, 2)
		assert.Contains(t, parsed.Errors, "License key not found")
	})
}

// NOTE: For complete workflow testing, see license_integration_test.go
// That file demonstrates a much simpler approach using existing structures
