package handlers

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/gokeys/gokeys/internal/adapters/database/postgres"
	"github.com/gokeys/gokeys/internal/config"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"gorm.io/gorm"
)

var (
	testDB   *gorm.DB
	dbOnce   sync.Once
	dbErr    error
	migrated bool
)

// findMigrationsDir finds the migrations directory relative to the current execution context
func findMigrationsDir() (string, error) {
	// Get current working directory
	wd, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// Try to find the migrations directory by walking up from current directory
	dir := wd
	for {
		migrationsPath := filepath.Join(dir, "internal", "adapters", "database", "migrations")
		if _, err := os.Stat(migrationsPath); err == nil {
			return migrationsPath, nil
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			break // reached root directory
		}
		dir = parent
	}

	// Fallback: try to get the source file location and work from there
	_, filename, _, ok := runtime.Caller(1)
	if ok {
		sourceDir := filepath.Dir(filename)
		// Navigate up from handlers directory to project root
		projectRoot := filepath.Join(sourceDir, "..", "..", "..", "..")
		migrationsPath := filepath.Join(projectRoot, "internal", "adapters", "database", "migrations")
		if _, err := os.Stat(migrationsPath); err == nil {
			return migrationsPath, nil
		}
	}

	return "", fmt.Errorf("could not find migrations directory")
}

// setupTestDatabaseWithMigrations sets up a test database using real migration system
func setupTestDatabaseWithMigrations(t *testing.T) (*gorm.DB, error) {
	t.Helper()

	dbOnce.Do(func() {
		// Load test configuration
		cfg, err := config.Load()
		if err != nil {
			dbErr = err
			return
		}

		// Override database name for tests
		cfg.Database.DBName = "gokeys_test"

		// Create database connection
		dbConfig := postgres.Config{
			Host:            cfg.Database.Host,
			Port:            cfg.Database.Port,
			User:            cfg.Database.User,
			Password:        cfg.Database.Password,
			DBName:          cfg.Database.DBName,
			SSLMode:         cfg.Database.SSLMode,
			MaxIdleConns:    cfg.Database.MaxIdleConns,
			MaxOpenConns:    cfg.Database.MaxOpenConns,
			ConnMaxLifetime: cfg.Database.ConnMaxLifetime,
		}

		testDB, err = postgres.NewConnection(dbConfig)
		if err != nil {
			dbErr = err
			return
		}

		// Run migrations if not already done
		if !migrated {
			// Use real migration system - find migrations directory dynamically
			migrationsDir, err := findMigrationsDir()
			if err != nil {
				dbErr = fmt.Errorf("failed to find migrations directory: %w", err)
				return
			}
			
			migrator := postgres.NewMigrator(testDB, migrationsDir)

			// Create required PostgreSQL extensions
			if err := migrator.CreateExtensions(); err != nil {
				dbErr = fmt.Errorf("failed to create extensions: %w", err)
				return
			}

			// Run all migrations
			if err := migrator.RunMigrations(); err != nil {
				dbErr = fmt.Errorf("failed to run migrations: %w", err)
				return
			}

			migrated = true
			t.Log("✅ Database migrations completed successfully using real migration system")
		}
	})

	if dbErr != nil {
		t.Skipf("Skipping test due to database setup error: %v", dbErr)
		return nil, dbErr
	}

	return testDB, nil
}

// setupTestDatabaseWithMigrationRollback sets up test database and provides rollback capability
func setupTestDatabaseWithMigrationRollback(t *testing.T) (*gorm.DB, *postgres.Migrator, error) {
	t.Helper()

	// Load test configuration
	cfg, err := config.Load()
	if err != nil {
		t.Skipf("Failed to load config: %v", err)
		return nil, nil, err
	}

	// Override database name for isolated test
	testDBName := fmt.Sprintf("gokeys_test_%s", uuid.New().String()[0:8])
	cfg.Database.DBName = testDBName

	// Create database connection
	dbConfig := postgres.Config{
		Host:            cfg.Database.Host,
		Port:            cfg.Database.Port,
		User:            cfg.Database.User,
		Password:        cfg.Database.Password,
		DBName:          cfg.Database.DBName,
		SSLMode:         cfg.Database.SSLMode,
		MaxIdleConns:    cfg.Database.MaxIdleConns,
		MaxOpenConns:    cfg.Database.MaxOpenConns,
		ConnMaxLifetime: cfg.Database.ConnMaxLifetime,
	}

	// First, connect to default database to create test database
	defaultConfig := dbConfig
	defaultConfig.DBName = "postgres"
	defaultDB, err := postgres.NewConnection(defaultConfig)
	if err != nil {
		t.Skipf("Failed to connect to default database: %v", err)
		return nil, nil, err
	}

	// Create test database
	createDBSQL := fmt.Sprintf("CREATE DATABASE %s", testDBName)
	if err := defaultDB.Exec(createDBSQL).Error; err != nil {
		postgres.Close(defaultDB)
		t.Skipf("Failed to create test database: %v", err)
		return nil, nil, err
	}
	postgres.Close(defaultDB)

	// Connect to test database
	testDB, err := postgres.NewConnection(dbConfig)
	if err != nil {
		t.Skipf("Failed to connect to test database: %v", err)
		return nil, nil, err
	}

	// Create migrator
	migrationsDir, err := findMigrationsDir()
	if err != nil {
		postgres.Close(testDB)
		t.Skipf("Failed to find migrations directory: %v", err)
		return nil, nil, err
	}
	migrator := postgres.NewMigrator(testDB, migrationsDir)

	// Create extensions and run migrations
	if err := migrator.CreateExtensions(); err != nil {
		postgres.Close(testDB)
		t.Skipf("Failed to create extensions: %v", err)
		return nil, nil, err
	}

	if err := migrator.RunMigrations(); err != nil {
		postgres.Close(testDB)
		t.Skipf("Failed to run migrations: %v", err)
		return nil, nil, err
	}

	t.Logf("✅ Test database %s created and migrated successfully", testDBName)

	// Cleanup function
	t.Cleanup(func() {
		postgres.Close(testDB)
		
		// Connect to default database to drop test database
		defaultDB, err := postgres.NewConnection(defaultConfig)
		if err != nil {
			t.Logf("Warning: failed to connect for cleanup: %v", err)
			return
		}
		defer postgres.Close(defaultDB)

		dropDBSQL := fmt.Sprintf("DROP DATABASE IF EXISTS %s", testDBName)
		if err := defaultDB.Exec(dropDBSQL).Error; err != nil {
			t.Logf("Warning: failed to drop test database: %v", err)
		} else {
			t.Logf("✅ Test database %s cleaned up successfully", testDBName)
		}
	})

	return testDB, migrator, nil
}

// createTestServiceCoordinator creates a real service coordinator with migrated database
func createTestServiceCoordinator(t *testing.T) (*services.ServiceCoordinator, error) {
	t.Helper()

	// Ensure migrations are run first
	_, err := setupTestDatabaseWithMigrations(t)
	if err != nil {
		return nil, err
	}

	// Create a fresh database connection (since migrations close the previous one)
	cfg, err := config.Load()
	if err != nil {
		return nil, err
	}
	cfg.Database.DBName = "gokeys_test"

	dbConfig := postgres.Config{
		Host:            cfg.Database.Host,
		Port:            cfg.Database.Port,
		User:            cfg.Database.User,
		Password:        cfg.Database.Password,
		DBName:          cfg.Database.DBName,
		SSLMode:         cfg.Database.SSLMode,
		MaxIdleConns:    cfg.Database.MaxIdleConns,
		MaxOpenConns:    cfg.Database.MaxOpenConns,
		ConnMaxLifetime: cfg.Database.ConnMaxLifetime,
	}

	db, err := postgres.NewConnection(dbConfig)
	if err != nil {
		return nil, err
	}

	// Create real service coordinator with no-op logger for tests
	logger := zerolog.Nop()
	serviceCoordinator := services.NewServiceCoordinator(db, logger)

	return serviceCoordinator, nil
}

// createTestServiceCoordinatorWithDB creates a service coordinator with existing database connection
func createTestServiceCoordinatorWithDB(db *gorm.DB) *services.ServiceCoordinator {
	// Create real service coordinator with no-op logger for tests
	logger := zerolog.Nop()
	serviceCoordinator := services.NewServiceCoordinator(db, logger)

	return serviceCoordinator
}

// createTestServiceCoordinatorIsolated creates an isolated service coordinator with dedicated database
func createTestServiceCoordinatorIsolated(t *testing.T) (*services.ServiceCoordinator, *postgres.Migrator, error) {
	t.Helper()

	db, migrator, err := setupTestDatabaseWithMigrationRollback(t)
	if err != nil {
		return nil, nil, err
	}

	// Create service coordinator
	logger := zerolog.Nop()
	serviceCoordinator := services.NewServiceCoordinator(db, logger)

	return serviceCoordinator, migrator, nil
}

// cleanupTestData provides helper to cleanup test data after tests
func cleanupTestData(t *testing.T, db *gorm.DB, tables ...string) {
	t.Helper()

	// Clean up tables in reverse dependency order to avoid foreign key issues
	defaultTables := []string{
		"webhook_events",
		"webhook_endpoints",
		"sessions",
		"tokens",
		"machine_processes",
		"machine_components",
		"machines",
		"license_users",
		"license_entitlements",
		"policy_entitlements",
		"group_owners",
		"group_permissions",
		"role_permissions",
		"token_permissions",
		"licenses",
		"entitlements",
		"policies",
		"products",
		"groups",
		"second_factors",
		"roles",
		"users",
		"environments",
	}

	tablesToClean := tables
	if len(tablesToClean) == 0 {
		tablesToClean = defaultTables
	}

	for _, table := range tablesToClean {
		if err := db.Exec("DELETE FROM " + table + " WHERE created_at > NOW() - INTERVAL '1 hour'").Error; err != nil {
			t.Logf("Warning: failed to cleanup table %s: %v", table, err)
		}
	}
}

// withTestTransaction runs a test within a database transaction that gets rolled back
func withTestTransaction(t *testing.T, fn func(*testing.T, *gorm.DB)) {
	t.Helper()

	db, err := setupTestDatabaseWithMigrations(t)
	if err != nil {
		t.Skipf("Skipping test due to database setup: %v", err)
		return
	}

	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		} else {
			tx.Rollback() // Always rollback for tests
		}
	}()

	fn(t, tx)
}

// withIsolatedMigrationTest runs test with isolated database and migration control
func withIsolatedMigrationTest(t *testing.T, fn func(*testing.T, *gorm.DB, *postgres.Migrator)) {
	t.Helper()

	db, migrator, err := setupTestDatabaseWithMigrationRollback(t)
	if err != nil {
		t.Skipf("Skipping test due to database setup: %v", err)
		return
	}

	fn(t, db, migrator)
}

// setupProductionTestDataWithRepositories creates test data using repositories
func setupProductionTestDataWithRepositories(ctx context.Context, sc *services.ServiceCoordinator) (*ProductionTestData, error) {
	// Create test data using repositories (real production workflow)

	// 1. Create Account (required for License)
	uniqueID := uuid.New().String()[0:8]
	account := &entities.Account{
		Name:  "Test Account",
		Slug:  "test-account-" + uniqueID,
		Email: "test-" + uniqueID + "@example.com",
		Metadata: entities.Metadata{
			"test_mode":  true,
			"created_by": "integration_test",
		},
	}
	if err := sc.Repositories.Account().Create(ctx, account); err != nil {
		return nil, err
	}

	// 2. Create Environment (optional but useful)
	environment := &entities.Environment{
		AccountID: account.ID,
		Name:      "Test Environment",
		Code:      "test-" + uniqueID,
	}
	if err := sc.Repositories.Environment().Create(ctx, environment); err != nil {
		return nil, err
	}

	// 3. Create Product (required for License)
	product := &entities.Product{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Name:          "Test Product",
		Code:          "test-product-" + uuid.New().String()[0:8],
	}
	if err := sc.Repositories.Product().Create(ctx, product); err != nil {
		return nil, err
	}

	// 4. Create Policy (required for License)
	policy := &entities.Policy{
		AccountID:        account.ID,
		ProductID:        product.ID,
		EnvironmentID:    &environment.ID,
		Name:             "Test Policy",
		Duration:         utilsIntPtr(3600), // 1 hour
		Strict:           false,
		RequireHeartbeat: false,
		MaxMachines:      utilsIntPtr(10),
		MaxUsers:         utilsIntPtr(5),
		Scheme:           entities.Ed25519Sign,
	}
	if err := sc.Repositories.Policy().Create(ctx, policy); err != nil {
		return nil, err
	}

	// 5. Create License (requires Account, Product, Policy)
	license := &entities.License{
		AccountID:     account.ID,
		ProductID:     product.ID,
		PolicyID:      policy.ID,
		EnvironmentID: &environment.ID,
		Key:           "TEST-LIC-" + uuid.New().String()[0:8] + "-" + uuid.New().String()[0:8],
		Name:          "Test License",
		Status:        entities.LicenseStatusActive,
		ExpiresAt:     utilsTimePtr(time.Now().Add(24 * time.Hour)), // Expires in 24 hours
		Metadata: entities.Metadata{
			"test_mode": true,
		},
	}
	if err := sc.Repositories.License().Create(ctx, license); err != nil {
		return nil, err
	}

	return &ProductionTestData{
		Account:     account,
		Product:     product,
		Policy:      policy,
		License:     license,
		Environment: environment,
	}, nil
}

// Helper functions for pointer creation (utils version)
func utilsIntPtr(i int) *int {
	return &i
}

func utilsTimePtr(t time.Time) *time.Time {
	return &t
}

// ProductionTestData holds test data entities for testing
type ProductionTestData struct {
	Account     *entities.Account
	Product     *entities.Product
	Policy      *entities.Policy
	License     *entities.License
	Environment *entities.Environment
}
