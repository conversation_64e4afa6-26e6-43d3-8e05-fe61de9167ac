package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type EntitlementHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewEntitlementHandler(serviceCoordinator *services.ServiceCoordinator) *EntitlementHandler {
	return &EntitlementHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear
type EntitlementCreateRequest struct {
	Name          string                 `json:"name" binding:"required,min=1,max=255"`
	Code          string                 `json:"code" binding:"required,min=1,max=255,alphanum"`
	EnvironmentID *string                `json:"environment_id,omitempty" binding:"omitempty,uuid"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

type EntitlementUpdateRequest struct {
	Name     *string                `json:"name,omitempty" binding:"omitempty,min=1,max=255"`
	Code     *string                `json:"code,omitempty" binding:"omitempty,min=1,max=255,alphanum"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs
type EntitlementResponse struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	Code          string                 `json:"code"`
	EnvironmentID *string                `json:"environment_id,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	Created       string                 `json:"created_at"`
	Updated       string                 `json:"updated_at"`
}

type EntitlementListResponse struct {
	Entitlements []EntitlementResponse `json:"entitlements"`
	Pagination   PaginationInfo        `json:"pagination"`
}

// Helper function to convert entity to response
func (h *EntitlementHandler) toEntitlementResponse(entitlement *entities.Entitlement) EntitlementResponse {
	return EntitlementResponse{
		ID:            entitlement.ID,
		Name:          entitlement.Name,
		Code:          entitlement.Code,
		EnvironmentID: entitlement.EnvironmentID,
		Metadata:      entitlement.Metadata,
		Created:       entitlement.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:       entitlement.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}
}

// ListEntitlements handles GET /api/v1/entitlements - Go-style approach
func (h *EntitlementHandler) ListEntitlements(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	search := c.Query("search")
	environmentID := c.Query("environment_id")

	// Build filter for repository
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    search,
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if environmentID != "" {
		if eid, err := uuid.Parse(environmentID); err == nil {
			filter.Filters["environment_id"] = eid
		}
	}

	entitlements, total, err := h.serviceCoordinator.Repositories.Entitlement().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve entitlements",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	entitlementResponses := make([]EntitlementResponse, len(entitlements))
	for i, entitlement := range entitlements {
		entitlementResponses[i] = h.toEntitlementResponse(entitlement)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := EntitlementListResponse{
		Entitlements: entitlementResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListEntitlementsHandler - Legacy handler for backward compatibility
// Use ListEntitlements instead
func (h *EntitlementHandler) ListEntitlementsHandler(c *gin.Context) {
	h.ListEntitlements(c)
}

// GetEntitlement handles GET /api/v1/entitlements/:id - Go-style approach
func (h *EntitlementHandler) GetEntitlement(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	entitlementIDStr := c.Param("id")
	entitlementID, err := uuid.Parse(entitlementIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid entitlement ID format",
		})
		return
	}

	// Get entitlement from repository by ID
	entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Verify entitlement belongs to the account
	if entitlement.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Return entitlement in Go-style
	response := h.toEntitlementResponse(entitlement)
	c.JSON(http.StatusOK, response)
}

// GetEntitlementHandler - Legacy handler for backward compatibility
// Use GetEntitlement instead
func (h *EntitlementHandler) GetEntitlementHandler(c *gin.Context) {
	h.GetEntitlement(c)
}

// CreateEntitlement handles POST /api/v1/entitlements - Go-style approach
func (h *EntitlementHandler) CreateEntitlement(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req EntitlementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Create entitlement entity
	entitlement := &entities.Entitlement{
		AccountID:     accountID.String(),
		Name:          req.Name,
		Code:          req.Code,
		EnvironmentID: req.EnvironmentID,
		Metadata:      req.Metadata,
	}

	// Save entitlement to repository
	if err := h.serviceCoordinator.Repositories.Entitlement().Create(c.Request.Context(), entitlement); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create entitlement",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventEntitlementCreated,
		account,
		events.MakeEventResource(entitlement),
		events.EventMeta{},
	)

	// Return created entitlement in Go-style
	response := h.toEntitlementResponse(entitlement)
	c.Header("Location", "/api/v1/entitlements/"+entitlement.ID)
	c.JSON(http.StatusCreated, response)
}

// CreateEntitlementHandler - Legacy handler for backward compatibility
// Use CreateEntitlement instead
func (h *EntitlementHandler) CreateEntitlementHandler(c *gin.Context) {
	h.CreateEntitlement(c)
}

// UpdateEntitlement handles PUT /api/v1/entitlements/:id - Go-style approach
func (h *EntitlementHandler) UpdateEntitlement(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	entitlementIDStr := c.Param("id")
	entitlementID, err := uuid.Parse(entitlementIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid entitlement ID format",
		})
		return
	}

	var req EntitlementUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing entitlement
	entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Verify entitlement belongs to the account
	if entitlement.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Entitlement not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		entitlement.Name = *req.Name
	}
	if req.Code != nil {
		entitlement.Code = *req.Code
	}
	if req.Metadata != nil {
		entitlement.Metadata = req.Metadata
	}

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Entitlement().Update(c.Request.Context(), entitlement); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update entitlement",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventEntitlementUpdated,
		account,
		events.MakeEventResource(entitlement),
		events.EventMeta{},
	)

	// Return updated entitlement in Go-style
	response := h.toEntitlementResponse(entitlement)
	c.JSON(http.StatusOK, response)
}

// UpdateEntitlementHandler - Legacy handler for backward compatibility
// Use UpdateEntitlement instead
func (h *EntitlementHandler) UpdateEntitlementHandler(c *gin.Context) {
	h.UpdateEntitlement(c)
}

func (h *EntitlementHandler) DeleteEntitlementHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Account not found"})
		return
	}

	entitlementID := c.Param("id")
	if entitlementID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Entitlement ID is required"})
		return
	}

	entitlementUUID, err := uuid.Parse(entitlementID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entitlement ID format"})
		return
	}

	entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Entitlement not found"})
		return
	}

	if err := h.serviceCoordinator.Repositories.Entitlement().Delete(c.Request.Context(), entitlementUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete entitlement"})
		return
	}

	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventEntitlementDeleted,
		account,
		events.MakeEventResource(entitlement),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
