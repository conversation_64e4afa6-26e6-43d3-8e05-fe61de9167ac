package middleware

import (
	"bytes"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gokeys/gokeys/internal/adapters/logger"
)

// LoggingConfig defines configuration for logging middleware
type LoggingConfig struct {
	Logger         logger.Logger
	SkipPaths      []string          // Paths to skip logging
	LogRequestBody bool              // Whether to log request body
	LogResponseBody bool             // Whether to log response body
	MaxBodySize    int64             // Maximum body size to log
	SensitiveHeaders []string        // Headers to redact
}

// LoggingMiddleware provides request/response logging
type LoggingMiddleware struct {
	config LoggingConfig
}

// responseWriter wraps gin.ResponseWriter to capture response body
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// NewLoggingMiddleware creates a new logging middleware
func NewLoggingMiddleware(config LoggingConfig) *LoggingMiddleware {
	// Set defaults
	if config.MaxBodySize == 0 {
		config.MaxBodySize = 1024 * 1024 // 1MB
	}
	
	if config.SensitiveHeaders == nil {
		config.SensitiveHeaders = []string{
			"authorization",
			"x-api-key",
			"x-license-key",
			"cookie",
			"set-cookie",
		}
	}
	
	return &LoggingMiddleware{
		config: config,
	}
}

// RequestID middleware adds a unique request ID to each request
func (lm *LoggingMiddleware) RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		
		// Add to context
		c.Set("request_id", requestID)
		
		// Add to response headers
		c.Header("X-Request-ID", requestID)
		
		c.Next()
	}
}

// Logger middleware logs HTTP requests and responses
func (lm *LoggingMiddleware) Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip logging for specified paths
		for _, path := range lm.config.SkipPaths {
			if c.Request.URL.Path == path {
				c.Next()
				return
			}
		}
		
		start := time.Now()
		
		// Create request logger
		reqLogger := lm.config.Logger.WithContext(c.Request.Context())
		
		// Log request start
		lm.logRequestStart(reqLogger, c)
		
		// Wrap response writer if we need to capture response body
		var responseBody *bytes.Buffer
		if lm.config.LogResponseBody {
			responseBody = &bytes.Buffer{}
			c.Writer = &responseWriter{
				ResponseWriter: c.Writer,
				body:           responseBody,
			}
		}
		
		// Process request
		c.Next()
		
		// Calculate duration
		duration := time.Since(start)
		
		// Log request end
		lm.logRequestEnd(reqLogger, c, duration, responseBody)
	}
}

// StructuredLogger returns a Gin logger that uses our structured logger
func (lm *LoggingMiddleware) StructuredLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// Create log entry
		logEntry := lm.config.Logger.WithFields(map[string]interface{}{
			"timestamp":    param.TimeStamp.Format(time.RFC3339),
			"status":       param.StatusCode,
			"latency":      param.Latency.String(),
			"client_ip":    param.ClientIP,
			"method":       param.Method,
			"path":         param.Path,
			"user_agent":   param.Request.UserAgent(),
			"error":        param.ErrorMessage,
		})
		
		// Log based on status code
		if param.StatusCode >= 500 {
			logEntry.Error("Server error")
		} else if param.StatusCode >= 400 {
			logEntry.Warn("Client error")
		} else {
			logEntry.Info("Request processed")
		}
		
		return ""
	})
}

// logRequestStart logs the beginning of a request
func (lm *LoggingMiddleware) logRequestStart(reqLogger logger.Logger, c *gin.Context) {
	fields := map[string]interface{}{
		"event":       "request_start",
		"method":      c.Request.Method,
		"path":        c.Request.URL.Path,
		"query":       c.Request.URL.RawQuery,
		"remote_ip":   c.ClientIP(),
		"user_agent":  c.Request.UserAgent(),
		"content_length": c.Request.ContentLength,
	}
	
	// Add headers (redact sensitive ones)
	headers := make(map[string]string)
	for name, values := range c.Request.Header {
		if lm.isSensitiveHeader(name) {
			headers[name] = "[REDACTED]"
		} else {
			if len(values) > 0 {
				headers[name] = values[0]
			}
		}
	}
	fields["headers"] = headers
	
	// Log request body if configured
	if lm.config.LogRequestBody && c.Request.ContentLength > 0 && c.Request.ContentLength <= lm.config.MaxBodySize {
		if body, err := io.ReadAll(c.Request.Body); err == nil {
			// Reset body for further processing
			c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
			
			// Only log non-binary content
			if lm.isTextContent(c.GetHeader("Content-Type")) {
				fields["request_body"] = string(body)
			} else {
				fields["request_body"] = "[BINARY_CONTENT]"
			}
		}
	}
	
	reqLogger.WithFields(fields).Info("Request started")
}

// logRequestEnd logs the completion of a request
func (lm *LoggingMiddleware) logRequestEnd(reqLogger logger.Logger, c *gin.Context, duration time.Duration, responseBody *bytes.Buffer) {
	fields := map[string]interface{}{
		"event":         "request_end",
		"status":        c.Writer.Status(),
		"duration_ms":   duration.Milliseconds(),
		"duration":      duration.String(),
		"response_size": c.Writer.Size(),
	}
	
	// Add response headers
	responseHeaders := make(map[string]string)
	for name, values := range c.Writer.Header() {
		if len(values) > 0 {
			responseHeaders[name] = values[0]
		}
	}
	fields["response_headers"] = responseHeaders
	
	// Log response body if configured and captured
	if lm.config.LogResponseBody && responseBody != nil && responseBody.Len() > 0 {
		if responseBody.Len() <= int(lm.config.MaxBodySize) {
			if lm.isTextContent(c.GetHeader("Content-Type")) {
				fields["response_body"] = responseBody.String()
			} else {
				fields["response_body"] = "[BINARY_CONTENT]"
			}
		} else {
			fields["response_body"] = "[CONTENT_TOO_LARGE]"
		}
	}
	
	// Add any errors from the context
	if errors := c.Errors; len(errors) > 0 {
		errorMessages := make([]string, len(errors))
		for i, err := range errors {
			errorMessages[i] = err.Error()
		}
		fields["errors"] = errorMessages
	}
	
	// Log based on status code
	if c.Writer.Status() >= 500 {
		reqLogger.WithFields(fields).Error("Request completed with server error")
	} else if c.Writer.Status() >= 400 {
		reqLogger.WithFields(fields).Warn("Request completed with client error")
	} else {
		reqLogger.WithFields(fields).Info("Request completed successfully")
	}
}

// isSensitiveHeader checks if a header should be redacted
func (lm *LoggingMiddleware) isSensitiveHeader(header string) bool {
	headerLower := strings.ToLower(header)
	for _, sensitive := range lm.config.SensitiveHeaders {
		if headerLower == strings.ToLower(sensitive) {
			return true
		}
	}
	return false
}

// isTextContent checks if content type is text-based
func (lm *LoggingMiddleware) isTextContent(contentType string) bool {
	textTypes := []string{
		"application/json",
		"application/xml",
		"text/",
		"application/x-www-form-urlencoded",
	}
	
	contentTypeLower := strings.ToLower(contentType)
	for _, textType := range textTypes {
		if strings.Contains(contentTypeLower, textType) {
			return true
		}
	}
	return false
}

// SecurityLogger logs security-related events
func (lm *LoggingMiddleware) SecurityLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		secLogger := lm.config.Logger.WithContext(c.Request.Context())
		
		// Log suspicious activity
		if lm.isSuspiciousRequest(c) {
			secLogger.WithFields(map[string]interface{}{
				"event":      "suspicious_request",
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"ip":         c.ClientIP(),
				"user_agent": c.Request.UserAgent(),
				"reason":     lm.getSuspiciousReason(c),
			}).Warn("Suspicious request detected")
		}
		
		c.Next()
		
		// Log failed authentication attempts
		if c.Writer.Status() == 401 {
			secLogger.WithFields(map[string]interface{}{
				"event":  "authentication_failed",
				"method": c.Request.Method,
				"path":   c.Request.URL.Path,
				"ip":     c.ClientIP(),
			}).Warn("Authentication failed")
		}
		
		// Log access denied
		if c.Writer.Status() == 403 {
			secLogger.WithFields(map[string]interface{}{
				"event":  "access_denied",
				"method": c.Request.Method,
				"path":   c.Request.URL.Path,
				"ip":     c.ClientIP(),
			}).Warn("Access denied")
		}
	}
}

// isSuspiciousRequest checks for suspicious request patterns
func (lm *LoggingMiddleware) isSuspiciousRequest(c *gin.Context) bool {
	// Check for common attack patterns
	suspiciousPatterns := []string{
		"../",
		"<script",
		"javascript:",
		"php://",
		"file://",
		"data://",
		"admin",
		".env",
		"wp-admin",
		"xmlrpc",
	}
	
	path := strings.ToLower(c.Request.URL.Path)
	query := strings.ToLower(c.Request.URL.RawQuery)
	userAgent := strings.ToLower(c.Request.UserAgent())
	
	for _, pattern := range suspiciousPatterns {
		if strings.Contains(path, pattern) || 
		   strings.Contains(query, pattern) || 
		   strings.Contains(userAgent, pattern) {
			return true
		}
	}
	
	return false
}

// getSuspiciousReason returns the reason why a request is suspicious
func (lm *LoggingMiddleware) getSuspiciousReason(c *gin.Context) string {
	path := strings.ToLower(c.Request.URL.Path)
	query := strings.ToLower(c.Request.URL.RawQuery)
	userAgent := strings.ToLower(c.Request.UserAgent())
	
	if strings.Contains(path, "../") || strings.Contains(query, "../") {
		return "path_traversal_attempt"
	}
	if strings.Contains(path, "<script") || strings.Contains(query, "<script") {
		return "xss_attempt"
	}
	if strings.Contains(userAgent, "bot") || strings.Contains(userAgent, "crawler") {
		return "bot_access"
	}
	if strings.Contains(path, "admin") || strings.Contains(path, "wp-admin") {
		return "admin_probe"
	}
	
	return "unknown_suspicious_pattern"
}

// AuditLogger logs audit events for compliance
func (lm *LoggingMiddleware) AuditLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		auditLogger := lm.config.Logger.WithContext(c.Request.Context())
		
		// Log audit events for specific paths
		auditPaths := []string{
			"/api/v1/licenses",
			"/api/v1/accounts",
			"/api/v1/users",
			"/api/v1/products",
			"/api/v1/policies",
		}
		
		shouldAudit := false
		for _, path := range auditPaths {
			if strings.HasPrefix(c.Request.URL.Path, path) {
				shouldAudit = true
				break
			}
		}
		
		if shouldAudit && (c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "DELETE") {
			auditLogger.WithFields(map[string]interface{}{
				"event":   "api_modification",
				"method":  c.Request.Method,
				"path":    c.Request.URL.Path,
				"ip":      c.ClientIP(),
				"user_id": c.GetString("user_id"),
			}).Info("API modification event")
		}
		
		c.Next()
	}
}

// Helper function to create a default logging middleware
func NewDefaultLoggingMiddleware(logger logger.Logger) *LoggingMiddleware {
	return NewLoggingMiddleware(LoggingConfig{
		Logger:           logger,
		SkipPaths:        []string{"/health", "/metrics"},
		LogRequestBody:   false,
		LogResponseBody:  false,
		MaxBodySize:      1024 * 1024, // 1MB
		SensitiveHeaders: []string{
			"authorization",
			"x-api-key",
			"x-license-key",
			"cookie",
			"set-cookie",
		},
	})
}