package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
)

// ErrorHandlerMiddleware handles panics and errors in a standardized way
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Log the panic with stack trace
				stack := debug.Stack()
				fmt.Printf("PANIC: %v\nStack Trace:\n%s\n", err, stack)

				// Return standardized error response
				if !c.Writer.Written() {
					responses.RenderInternalError(c, "Internal server error occurred")
				}
				c.Abort()
			}
		}()

		c.Next()

		// Handle any errors that were set but not responded to
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			
			// If response hasn't been written yet, write error response
			if !c.Writer.Written() {
				switch err.Type {
				case gin.ErrorTypeBind:
					responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
				case gin.ErrorTypePublic:
					responses.RenderBadRequest(c, err.Error())
				default:
					responses.RenderInternalError(c, "Internal server error")
				}
			}
		}
	}
}

// ValidationErrorMiddleware converts validation errors to JSON-API format
func ValidationErrorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Check for validation errors
		if c.Writer.Status() == http.StatusBadRequest {
			if len(c.Errors) > 0 {
				err := c.Errors.Last()
				if err.Type == gin.ErrorTypeBind {
					// This is a binding/validation error
					source := map[string]interface{}{
						"pointer": "/data/attributes",
					}
					responses.RenderUnprocessableEntity(c, err.Error(), source)
					return
				}
			}
		}
	}
}

// NotFoundHandler handles 404 errors in JSON-API format
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		responses.RenderNotFound(c, "Endpoint")
	}
}

// MethodNotAllowedHandler handles 405 errors in JSON-API format
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		err := responses.SingleError(
			responses.ErrorCodeInvalidRequest,
			"Method Not Allowed",
			fmt.Sprintf("Method %s is not allowed for this endpoint", c.Request.Method),
		)
		responses.RenderError(c, http.StatusMethodNotAllowed, err)
	}
}