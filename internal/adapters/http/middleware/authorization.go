package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gokeys/gokeys/internal/domain/services"
)

// Permission constants for the license management system
const (
	// Account permissions
	PermissionAccountRead   = "account:read"
	PermissionAccountWrite  = "account:write"
	PermissionAccountAdmin  = "account:admin"

	// User permissions
	PermissionUserRead   = "user:read"
	PermissionUserWrite  = "user:write"
	PermissionUserAdmin  = "user:admin"

	// Product permissions
	PermissionProductRead   = "product:read"
	PermissionProductWrite  = "product:write"
	PermissionProductAdmin  = "product:admin"

	// Policy permissions
	PermissionPolicyRead   = "policy:read"
	PermissionPolicyWrite  = "policy:write"
	PermissionPolicyAdmin  = "policy:admin"

	// License permissions
	PermissionLicenseRead      = "license:read"
	PermissionLicenseWrite     = "license:write"
	PermissionLicenseValidate  = "license:validate"
	PermissionLicenseAdmin     = "license:admin"

	// Machine permissions
	PermissionMachineRead      = "machine:read"
	PermissionMachineWrite     = "machine:write"
	PermissionMachineRegister  = "machine:register"
	PermissionMachineHeartbeat = "machine:heartbeat"
	PermissionMachineAdmin     = "machine:admin"

	// Entitlement permissions
	PermissionEntitlementRead  = "entitlement:read"
	PermissionEntitlementWrite = "entitlement:write"
	PermissionEntitlementAdmin = "entitlement:admin"

	// Group permissions
	PermissionGroupRead  = "group:read"
	PermissionGroupWrite = "group:write"
	PermissionGroupAdmin = "group:admin"

	// Webhook permissions
	PermissionWebhookRead  = "webhook:read"
	PermissionWebhookWrite = "webhook:write"
	PermissionWebhookAdmin = "webhook:admin"

	// Analytics permissions
	PermissionAnalyticsRead = "analytics:read"
	PermissionReportsRead   = "reports:read"

	// Admin permissions
	PermissionSystemAdmin = "system:admin"
	PermissionSuperAdmin  = "*"
)

// Resource types for resource-based permissions
const (
	ResourceTypeAccount         = "account"
	ResourceTypeProduct         = "product"
	ResourceTypePolicy          = "policy"
	ResourceTypeLicense         = "license"
	ResourceTypeMachine         = "machine"
	ResourceTypeUser            = "user"
	ResourceTypeEntitlement     = "entitlement"
	ResourceTypeGroup           = "group"
	ResourceTypeWebhookEndpoint = "webhook_endpoint"
)

// AuthorizationMiddleware provides RBAC-based authorization
type AuthorizationMiddleware struct {
	serviceCoordinator *services.ServiceCoordinator
}

// NewAuthorizationMiddleware creates a new authorization middleware
func NewAuthorizationMiddleware(serviceCoordinator *services.ServiceCoordinator) *AuthorizationMiddleware {
	return &AuthorizationMiddleware{
		serviceCoordinator: serviceCoordinator,
	}
}

// RequireAccountAccess ensures user has access to the specified account
func (am *AuthorizationMiddleware) RequireAccountAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		userAccountID, err := GetAccountID(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "User account ID not found in token",
			})
			c.Abort()
			return
		}

		// Get account ID from URL parameter or header
		requestedAccountID := c.Param("account_id")
		if requestedAccountID == "" {
			requestedAccountID = c.GetHeader("X-Account-ID")
		}

		if requestedAccountID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "missing_account_id",
				"message": "Account ID required in URL path or X-Account-ID header",
			})
			c.Abort()
			return
		}

		requestedUUID, err := uuid.Parse(requestedAccountID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "invalid_account_id",
				"message": "Invalid account ID format",
			})
			c.Abort()
			return
		}

		// Check if user belongs to this account or has super admin permission
		if userAccountID != requestedUUID {
			permissions, _ := GetPermissions(c)
			if !hasPermission(permissions, PermissionSuperAdmin) && !hasPermission(permissions, PermissionSystemAdmin) {
				c.JSON(http.StatusForbidden, gin.H{
					"error":   "account_access_denied",
					"message": "Access denied to this account",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// RequireResourceOwnership ensures user owns or has access to a specific resource
func (am *AuthorizationMiddleware) RequireResourceOwnership(resourceType string, resourceIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := GetUserID(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "User ID not found in token",
			})
			c.Abort()
			return
		}

		accountID, err := GetAccountID(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Account ID not found in token",
			})
			c.Abort()
			return
		}

		resourceIDStr := c.Param(resourceIDParam)
		if resourceIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "missing_resource_id",
				"message": fmt.Sprintf("Resource ID parameter '%s' is required", resourceIDParam),
			})
			c.Abort()
			return
		}

		resourceID, err := uuid.Parse(resourceIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "invalid_resource_id",
				"message": "Invalid resource ID format",
			})
			c.Abort()
			return
		}

		// Check permissions for this resource type
		permissions, _ := GetPermissions(c)
		if hasPermission(permissions, PermissionSuperAdmin) {
			c.Next()
			return
		}

		// Check resource-specific ownership
		ctx := context.Background()
		hasAccess, err := am.checkResourceAccess(ctx, userID, accountID, resourceType, resourceID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "authorization_error",
				"message": "Failed to check resource access",
			})
			c.Abort()
			return
		}

		if !hasAccess {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "resource_access_denied",
				"message": fmt.Sprintf("Access denied to %s resource", resourceType),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireEnvironmentAccess ensures user has access to the specified environment
func (am *AuthorizationMiddleware) RequireEnvironmentAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		accountID, err := GetAccountID(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Account ID not found in token",
			})
			c.Abort()
			return
		}

		// Get environment from parameter or header
		environmentCode := c.Param("environment")
		if environmentCode == "" {
			environmentCode = c.GetHeader("X-Environment")
		}

		if environmentCode == "" {
			// No specific environment required
			c.Next()
			return
		}

		// Check if environment exists and user has access
		ctx := context.Background()
		environment, err := am.serviceCoordinator.Repositories.Environment().GetByCode(ctx, environmentCode, accountID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "environment_not_found",
				"message": "Environment not found or access denied",
			})
			c.Abort()
			return
		}

		// Store environment in context for later use
		c.Set("environment_id", environment.ID)
		c.Set("environment_code", environment.Code)
		c.Set("isolation_strategy", environment.IsolationStrategy)

		c.Next()
	}
}

// RequireGroupMembership ensures user is a member of a specific group
func (am *AuthorizationMiddleware) RequireGroupMembership(groupIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := GetUserID(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "User ID not found in token",
			})
			c.Abort()
			return
		}

		groupIDStr := c.Param(groupIDParam)
		if groupIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "missing_group_id",
				"message": fmt.Sprintf("Group ID parameter '%s' is required", groupIDParam),
			})
			c.Abort()
			return
		}

		groupID, err := uuid.Parse(groupIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "invalid_group_id",
				"message": "Invalid group ID format",
			})
			c.Abort()
			return
		}

		// Check if user is member of the group
		ctx := context.Background()
		user, err := am.serviceCoordinator.Repositories.User().GetByID(ctx, userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "user_lookup_failed",
				"message": "Failed to lookup user information",
			})
			c.Abort()
			return
		}

		if user.GroupID == nil || *user.GroupID != groupID.String() {
			permissions, _ := GetPermissions(c)
			if !hasPermission(permissions, PermissionSuperAdmin) && !hasPermission(permissions, PermissionUserAdmin) {
				c.JSON(http.StatusForbidden, gin.H{
					"error":   "group_membership_required",
					"message": "User must be a member of this group",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// RequireValidLicense ensures the request includes a valid license
func (am *AuthorizationMiddleware) RequireValidLicense() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get license key from header or query parameter
		licenseKey := c.GetHeader("X-License-Key")
		if licenseKey == "" {
			licenseKey = c.Query("license_key")
		}

		if licenseKey == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "license_key_required",
				"message": "License key required in X-License-Key header or license_key parameter",
			})
			c.Abort()
			return
		}

		// Validate license
		ctx := context.Background()
		result, err := am.serviceCoordinator.LicenseValidation.ValidateLicense(ctx, licenseKey, nil, nil)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "license_validation_failed",
				"message": "Failed to validate license",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		if !result.Valid {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "invalid_license",
				"message": "License is not valid",
				"errors":  result.Errors,
			})
			c.Abort()
			return
		}

		// Store license information in context
		c.Set("license", result.License)
		c.Set("license_policy", result.Policy)
		c.Set("license_account", result.Account)

		c.Next()
	}
}

// checkResourceAccess checks if user has access to a specific resource
func (am *AuthorizationMiddleware) checkResourceAccess(ctx context.Context, userID, accountID uuid.UUID, resourceType string, resourceID uuid.UUID) (bool, error) {
	switch resourceType {
	case ResourceTypeProduct:
		product, err := am.serviceCoordinator.Repositories.Product().GetByID(ctx, resourceID)
		if err != nil {
			return false, err
		}
		return product.AccountID == accountID.String(), nil

	case ResourceTypeLicense:
		license, err := am.serviceCoordinator.Repositories.License().GetByID(ctx, resourceID)
		if err != nil {
			return false, err
		}
		// User can access license if it belongs to their account
		// or if they are the license owner
		return license.AccountID == accountID.String() || (license.UserID != nil && *license.UserID == userID.String()), nil

	case ResourceTypeMachine:
		machine, err := am.serviceCoordinator.Repositories.Machine().GetByID(ctx, resourceID)
		if err != nil {
			return false, err
		}
		return machine.AccountID == accountID.String() || (machine.OwnerID != nil && *machine.OwnerID == userID.String()), nil

	case ResourceTypeUser:
		user, err := am.serviceCoordinator.Repositories.User().GetByID(ctx, resourceID)
		if err != nil {
			return false, err
		}
		return user.AccountID == accountID.String() || user.ID == userID.String(), nil

	default:
		// For other resource types, just check account ownership
		return true, nil
	}
}

// hasPermission checks if user has a specific permission
func hasPermission(permissions []string, required string) bool {
	for _, perm := range permissions {
		if perm == required || perm == PermissionSuperAdmin {
			return true
		}
		
		// Check wildcard permissions (e.g., "user:*" matches "user:read")
		if strings.HasSuffix(perm, ":*") {
			prefix := strings.TrimSuffix(perm, "*")
			if strings.HasPrefix(required, prefix) {
				return true
			}
		}
	}
	return false
}

// CreatePermissionChecker creates a middleware that checks for specific permissions
func (am *AuthorizationMiddleware) CreatePermissionChecker(requiredPermissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		permissions, err := GetPermissions(c)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "permission_error",
				"message": "Unable to check permissions",
			})
			c.Abort()
			return
		}

		// Check if user has any of the required permissions
		hasAnyPermission := false
		for _, required := range requiredPermissions {
			if hasPermission(permissions, required) {
				hasAnyPermission = true
				break
			}
		}

		if !hasAnyPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "insufficient_permissions",
				"message": fmt.Sprintf("One of the following permissions required: %v", requiredPermissions),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireOwnershipOrPermission combines resource ownership and permission checks
func (am *AuthorizationMiddleware) RequireOwnershipOrPermission(resourceType, resourceIDParam string, permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userPermissions, _ := GetPermissions(c)
		
		// If user has required permissions, allow access
		for _, perm := range permissions {
			if hasPermission(userPermissions, perm) {
				c.Next()
				return
			}
		}

		// Otherwise, check resource ownership
		am.RequireResourceOwnership(resourceType, resourceIDParam)(c)
	}
}

// Helper functions for context access

// GetEnvironmentID extracts environment ID from context
func GetEnvironmentID(c *gin.Context) (uuid.UUID, error) {
	envID, exists := c.Get("environment_id")
	if !exists {
		return uuid.Nil, fmt.Errorf("environment ID not found in context")
	}

	envUUID, ok := envID.(uuid.UUID)
	if !ok {
		return uuid.Nil, fmt.Errorf("environment ID has invalid type")
	}

	return envUUID, nil
}

// GetEnvironmentCode extracts environment code from context
func GetEnvironmentCode(c *gin.Context) (string, error) {
	code, exists := c.Get("environment_code")
	if !exists {
		return "", fmt.Errorf("environment code not found in context")
	}

	codeStr, ok := code.(string)
	if !ok {
		return "", fmt.Errorf("environment code has invalid type")
	}

	return codeStr, nil
}