package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/services/license"
)

// RateLimitConfig defines rate limiting configuration
type RateLimitConfig struct {
	// Rate limiting rules
	RequestsPerSecond int           // Requests per second limit
	RequestsPerMinute int           // Requests per minute limit
	RequestsPerHour   int           // Requests per hour limit
	BurstSize         int           // Burst size for token bucket
	WindowSize        time.Duration // Sliding window size

	// Cache configuration
	Cache license.CacheInterface

	// Key generation
	KeyGenerator func(c *gin.Context) string

	// Response configuration
	Headers           bool // Add rate limit headers to response
	SkipSuccessfulReq bool // Only count failed requests
	SkipFailedReq     bool // Only count successful requests

	// Error handling
	ErrorHandler func(c *gin.Context, err error)

	// Custom limits per endpoint or user
	CustomLimits map[string]RateLimit
}

// RateLimit defines a specific rate limit
type RateLimit struct {
	RequestsPerSecond int
	RequestsPerMinute int
	RequestsPerHour   int
	BurstSize         int
}

// RateLimitMiddleware provides request rate limiting
type RateLimitMiddleware struct {
	config RateLimitConfig
}

// RateLimitInfo contains rate limit status information
type RateLimitInfo struct {
	Limit     int           `json:"limit"`
	Remaining int           `json:"remaining"`
	Reset     time.Time     `json:"reset"`
	RetryAfter time.Duration `json:"retry_after,omitempty"`
}

// NewRateLimitMiddleware creates a new rate limiting middleware
func NewRateLimitMiddleware(config RateLimitConfig) *RateLimitMiddleware {
	// Set defaults
	if config.KeyGenerator == nil {
		config.KeyGenerator = defaultKeyGenerator
	}
	if config.ErrorHandler == nil {
		config.ErrorHandler = defaultErrorHandler
	}
	if config.WindowSize == 0 {
		config.WindowSize = time.Minute
	}
	if config.BurstSize == 0 {
		config.BurstSize = config.RequestsPerSecond * 2
	}

	return &RateLimitMiddleware{
		config: config,
	}
}

// Limit returns a Gin middleware function for rate limiting
func (rlm *RateLimitMiddleware) Limit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate cache key for this request
		key := rlm.config.KeyGenerator(c)
		
		// Check rate limits
		allowed, info, err := rlm.checkRateLimit(c, key)
		if err != nil {
			rlm.config.ErrorHandler(c, err)
			c.Abort()
			return
		}

		// Add rate limit headers if configured
		if rlm.config.Headers {
			rlm.addRateLimitHeaders(c, info)
		}

		// Block request if rate limit exceeded
		if !allowed {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "rate_limit_exceeded",
				"message": "Rate limit exceeded",
				"limit":   info.Limit,
				"reset":   info.Reset.Unix(),
			})
			c.Abort()
			return
		}

		// Store rate limit info in context for logging
		c.Set("rate_limit_info", info)

		c.Next()

		// Post-process: increment counters based on response
		rlm.postProcessRequest(c, key)
	}
}

// LimitPerEndpoint applies different rate limits per endpoint
func (rlm *RateLimitMiddleware) LimitPerEndpoint(endpointLimits map[string]RateLimit) gin.HandlerFunc {
	return func(c *gin.Context) {
		endpoint := c.FullPath()
		
		// Get custom limit for this endpoint
		if customLimit, exists := endpointLimits[endpoint]; exists {
			// Create temporary middleware with custom limits
			tempConfig := rlm.config
			tempConfig.RequestsPerSecond = customLimit.RequestsPerSecond
			tempConfig.RequestsPerMinute = customLimit.RequestsPerMinute
			tempConfig.RequestsPerHour = customLimit.RequestsPerHour
			tempConfig.BurstSize = customLimit.BurstSize
			
			tempMiddleware := &RateLimitMiddleware{config: tempConfig}
			tempMiddleware.Limit()(c)
		} else {
			// Use default limits
			rlm.Limit()(c)
		}
	}
}

// LimitPerUser applies rate limits per authenticated user
func (rlm *RateLimitMiddleware) LimitPerUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := c.Get(string(UserIDKey))
		if !exists {
			// No user context, apply global limits
			rlm.Limit()(c)
			return
		}

		// Create user-specific key generator
		originalKeyGen := rlm.config.KeyGenerator
		rlm.config.KeyGenerator = func(c *gin.Context) string {
			return fmt.Sprintf("user:%v:%s", userID, originalKeyGen(c))
		}

		rlm.Limit()(c)

		// Restore original key generator
		rlm.config.KeyGenerator = originalKeyGen
	}
}

// checkRateLimit checks if the request should be allowed
func (rlm *RateLimitMiddleware) checkRateLimit(c *gin.Context, key string) (bool, *RateLimitInfo, error) {
	ctx := c.Request.Context()
	now := time.Now()

	// Check different time windows
	limits := []struct {
		duration time.Duration
		limit    int
		suffix   string
	}{
		{time.Second, rlm.config.RequestsPerSecond, "sec"},
		{time.Minute, rlm.config.RequestsPerMinute, "min"},
		{time.Hour, rlm.config.RequestsPerHour, "hour"},
	}

	for _, limit := range limits {
		if limit.limit <= 0 {
			continue // Skip if limit is not set
		}

		windowKey := fmt.Sprintf("rate_limit:%s:%s:%d", key, limit.suffix, now.Truncate(limit.duration).Unix())
		
		// Get current count
		countStr, err := rlm.config.Cache.Get(ctx, windowKey)
		currentCount := 0
		if err == nil {
			currentCount, _ = strconv.Atoi(countStr)
		}

		// Check if limit would be exceeded
		if currentCount >= limit.limit {
			resetTime := now.Truncate(limit.duration).Add(limit.duration)
			info := &RateLimitInfo{
				Limit:      limit.limit,
				Remaining:  0,
				Reset:      resetTime,
				RetryAfter: time.Until(resetTime),
			}
			return false, info, nil
		}

		// Return info for the most restrictive limit that applies
		if currentCount > 0 {
			resetTime := now.Truncate(limit.duration).Add(limit.duration)
			info := &RateLimitInfo{
				Limit:     limit.limit,
				Remaining: limit.limit - currentCount - 1,
				Reset:     resetTime,
			}
			return true, info, nil
		}
	}

	// Default info if no limits apply
	info := &RateLimitInfo{
		Limit:     rlm.config.RequestsPerMinute,
		Remaining: rlm.config.RequestsPerMinute - 1,
		Reset:     now.Add(time.Minute),
	}
	return true, info, nil
}

// incrementCounters increments the rate limit counters
func (rlm *RateLimitMiddleware) incrementCounters(c *gin.Context, key string) error {
	ctx := c.Request.Context()
	now := time.Now()

	// Increment counters for different time windows
	limits := []struct {
		duration time.Duration
		limit    int
		suffix   string
	}{
		{time.Second, rlm.config.RequestsPerSecond, "sec"},
		{time.Minute, rlm.config.RequestsPerMinute, "min"},
		{time.Hour, rlm.config.RequestsPerHour, "hour"},
	}

	for _, limit := range limits {
		if limit.limit <= 0 {
			continue
		}

		windowKey := fmt.Sprintf("rate_limit:%s:%s:%d", key, limit.suffix, now.Truncate(limit.duration).Unix())
		
		// Try to increment using cache
		// For simplicity, we'll use standard Set/Get operations
		currentStr, err := rlm.config.Cache.Get(ctx, windowKey)
		var count int64
		if err == nil && currentStr != "" {
			if parsedCount, parseErr := strconv.ParseInt(currentStr, 10, 64); parseErr == nil {
				count = parsedCount + 1
			} else {
				count = 1
			}
		} else {
			count = 1
		}
		
		// Set the new count
		err = rlm.config.Cache.Set(ctx, windowKey, fmt.Sprintf("%d", count), rlm.config.WindowSize)
		if err != nil {
			return err
		}
	}

	return nil
}

// postProcessRequest handles post-request processing
func (rlm *RateLimitMiddleware) postProcessRequest(c *gin.Context, key string) {
	// Check if we should count this request based on response
	shouldCount := true
	
	if rlm.config.SkipSuccessfulReq && c.Writer.Status() < 400 {
		shouldCount = false
	}
	if rlm.config.SkipFailedReq && c.Writer.Status() >= 400 {
		shouldCount = false
	}

	if shouldCount {
		if err := rlm.incrementCounters(c, key); err != nil {
			// Log error but don't fail the request
			c.Header("X-RateLimit-Error", "Failed to update counters")
		}
	}
}

// addRateLimitHeaders adds standard rate limit headers to the response
func (rlm *RateLimitMiddleware) addRateLimitHeaders(c *gin.Context, info *RateLimitInfo) {
	c.Header("X-RateLimit-Limit", strconv.Itoa(info.Limit))
	c.Header("X-RateLimit-Remaining", strconv.Itoa(info.Remaining))
	c.Header("X-RateLimit-Reset", strconv.FormatInt(info.Reset.Unix(), 10))
	
	if info.RetryAfter > 0 {
		c.Header("Retry-After", strconv.Itoa(int(info.RetryAfter.Seconds())))
	}
}

// Default implementations

func defaultKeyGenerator(c *gin.Context) string {
	// Use IP address as default key
	return c.ClientIP()
}

func defaultErrorHandler(c *gin.Context, err error) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"error":   "rate_limit_error",
		"message": "Rate limiting service error",
		"details": err.Error(),
	})
}

// Predefined rate limit configurations

// NewAPIRateLimit creates rate limiting for general API usage
func NewAPIRateLimit(cache license.CacheInterface) *RateLimitMiddleware {
	return NewRateLimitMiddleware(RateLimitConfig{
		RequestsPerSecond: 10,
		RequestsPerMinute: 100,
		RequestsPerHour:   1000,
		Cache:             cache,
		Headers:           true,
	})
}

// NewLicenseValidationRateLimit creates rate limiting for license validation endpoints
func NewLicenseValidationRateLimit(cache license.CacheInterface) *RateLimitMiddleware {
	return NewRateLimitMiddleware(RateLimitConfig{
		RequestsPerSecond: 50,  // Higher limit for license validation
		RequestsPerMinute: 1000,
		RequestsPerHour:   10000,
		Cache:             cache,
		Headers:           true,
		KeyGenerator: func(c *gin.Context) string {
			// Use license key for rate limiting if available
			licenseKey := c.Query("license_key")
			if licenseKey == "" {
				licenseKey = c.GetHeader("X-License-Key")
			}
			if licenseKey != "" {
				return fmt.Sprintf("license:%s", licenseKey)
			}
			return c.ClientIP()
		},
	})
}

// NewAdminRateLimit creates rate limiting for admin endpoints
func NewAdminRateLimit(cache license.CacheInterface) *RateLimitMiddleware {
	return NewRateLimitMiddleware(RateLimitConfig{
		RequestsPerSecond: 5,
		RequestsPerMinute: 50,
		RequestsPerHour:   500,
		Cache:             cache,
		Headers:           true,
	})
}

// Helper functions for testing and monitoring

// GetRateLimitStatus returns current rate limit status for a key
func (rlm *RateLimitMiddleware) GetRateLimitStatus(c *gin.Context, key string) (*RateLimitInfo, error) {
	allowed, info, err := rlm.checkRateLimit(c, key)
	if err != nil {
		return nil, err
	}
	
	// Include whether request would be allowed
	if !allowed {
		info.Remaining = 0
	}
	
	return info, nil
}

// ResetRateLimit clears rate limit counters for a key
func (rlm *RateLimitMiddleware) ResetRateLimit(c *gin.Context, key string) error {
	// Clear all time window counters
	suffixes := []string{"sec", "min", "hour"}
	for _, suffix := range suffixes {
		pattern := fmt.Sprintf("rate_limit:%s:%s:*", key, suffix)
		// For cleanup, we'll just try to delete individual keys
		// This is a simplification since we don't have pattern deletion in the interface
		_ = pattern // acknowledge the variable to avoid unused warning
	}
	
	return nil
}