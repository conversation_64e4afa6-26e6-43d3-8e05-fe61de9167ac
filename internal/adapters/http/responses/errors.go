package responses

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ErrorCode represents standardized error codes
type ErrorCode string

const (
	// Authentication errors
	ErrorCodeUnauthorized        ErrorCode = "UNAUTHORIZED"
	ErrorCodeInvalidToken        ErrorCode = "INVALID_TOKEN"
	ErrorCodeTokenExpired        ErrorCode = "TOKEN_EXPIRED"
	ErrorCodeInsufficientScope   ErrorCode = "INSUFFICIENT_SCOPE"

	// Authorization errors
	ErrorCodeForbidden           ErrorCode = "FORBIDDEN"
	ErrorCodeInsufficientPermissions ErrorCode = "INSUFFICIENT_PERMISSIONS"

	// Validation errors
	ErrorCodeValidationFailed    ErrorCode = "VALIDATION_FAILED"
	ErrorCodeInvalidRequest      ErrorCode = "INVALID_REQUEST"
	ErrorCodeMissingParameter    ErrorCode = "MISSING_PARAMETER"
	ErrorCodeInvalidParameter    ErrorCode = "INVALID_PARAMETER"

	// Resource errors
	ErrorCodeNotFound            ErrorCode = "NOT_FOUND"
	ErrorCodeResourceNotFound    ErrorCode = "RESOURCE_NOT_FOUND"
	ErrorCodeConflict            ErrorCode = "CONFLICT"
	ErrorCodeResourceExists      ErrorCode = "RESOURCE_EXISTS"

	// License-specific errors
	ErrorCodeLicenseExpired      ErrorCode = "LICENSE_EXPIRED"
	ErrorCodeLicenseSuspended    ErrorCode = "LICENSE_SUSPENDED"
	ErrorCodeLicenseInvalid      ErrorCode = "LICENSE_INVALID"
	ErrorCodeLicenseNotFound     ErrorCode = "LICENSE_NOT_FOUND"
	ErrorCodeMachineHeartbeatDead ErrorCode = "MACHINE_HEARTBEAT_DEAD"
	ErrorCodeTooManyMachines     ErrorCode = "TOO_MANY_MACHINES"
	ErrorCodeNoMachine           ErrorCode = "NO_MACHINE"

	// Checkout errors
	ErrorCodeCheckoutAlgorithmInvalid ErrorCode = "CHECKOUT_ALGORITHM_INVALID"
	ErrorCodeCheckoutIncludeInvalid   ErrorCode = "CHECKOUT_INCLUDE_INVALID"
	ErrorCodeCheckoutTTLInvalid       ErrorCode = "CHECKOUT_TTL_INVALID"

	// System errors
	ErrorCodeInternalError       ErrorCode = "INTERNAL_ERROR"
	ErrorCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrorCodeRateLimitExceeded   ErrorCode = "RATE_LIMIT_EXCEEDED"
)

// ErrorDetail represents detailed error information
type ErrorDetail struct {
	Code     ErrorCode              `json:"code"`
	Title    string                 `json:"title"`
	Detail   string                 `json:"detail"`
	Source   map[string]interface{} `json:"source,omitempty"`
	Meta     map[string]interface{} `json:"meta,omitempty"`
}

// ErrorResponse represents a JSON-API error response
type ErrorResponse struct {
	Errors []ErrorDetail `json:"errors"`
}

// SingleError creates an error response with a single error
func SingleError(code ErrorCode, title, detail string) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   code,
				Title:  title,
				Detail: detail,
			},
		},
	}
}

// ValidationError creates a validation error with source information
func ValidationError(detail string, source map[string]interface{}) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeValidationFailed,
				Title:  "Validation Failed",
				Detail: detail,
				Source: source,
			},
		},
	}
}

// ParameterError creates a parameter-related error
func ParameterError(parameter, detail string) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeInvalidParameter,
				Title:  "Invalid Parameter",
				Detail: detail,
				Source: map[string]interface{}{
					"parameter": parameter,
				},
			},
		},
	}
}

// NotFoundError creates a resource not found error
func NotFoundError(resource string) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeResourceNotFound,
				Title:  "Resource Not Found",
				Detail: resource + " not found",
			},
		},
	}
}

// UnauthorizedError creates an unauthorized error
func UnauthorizedError(detail string) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeUnauthorized,
				Title:  "Unauthorized",
				Detail: detail,
			},
		},
	}
}

// ForbiddenError creates a forbidden error
func ForbiddenError(detail string) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeForbidden,
				Title:  "Forbidden",
				Detail: detail,
			},
		},
	}
}

// InternalError creates an internal server error
func InternalError(detail string) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeInternalError,
				Title:  "Internal Server Error",
				Detail: detail,
			},
		},
	}
}

// ConflictError creates a conflict error
func ConflictError(detail string) *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeConflict,
				Title:  "Conflict",
				Detail: detail,
			},
		},
	}
}

// License-specific error constructors

// LicenseExpiredError creates a license expired error
func LicenseExpiredError() *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeLicenseExpired,
				Title:  "License Expired",
				Detail: "License has expired",
			},
		},
	}
}

// LicenseSuspendedError creates a license suspended error
func LicenseSuspendedError() *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeLicenseSuspended,
				Title:  "License Suspended",
				Detail: "License has been suspended",
			},
		},
	}
}

// TooManyMachinesError creates a too many machines error
func TooManyMachinesError() *ErrorResponse {
	return &ErrorResponse{
		Errors: []ErrorDetail{
			{
				Code:   ErrorCodeTooManyMachines,
				Title:  "Too Many Machines",
				Detail: "License has reached machine limit",
			},
		},
	}
}

// Response helper functions

// RenderError renders an error response with appropriate HTTP status
func RenderError(c *gin.Context, status int, err *ErrorResponse) {
	c.JSON(status, err)
}

// RenderBadRequest renders a 400 Bad Request error
func RenderBadRequest(c *gin.Context, detail string) {
	RenderError(c, http.StatusBadRequest, SingleError(ErrorCodeInvalidRequest, "Bad Request", detail))
}

// RenderUnauthorized renders a 401 Unauthorized error
func RenderUnauthorized(c *gin.Context, detail string) {
	RenderError(c, http.StatusUnauthorized, UnauthorizedError(detail))
}

// RenderForbidden renders a 403 Forbidden error
func RenderForbidden(c *gin.Context, detail string) {
	RenderError(c, http.StatusForbidden, ForbiddenError(detail))
}

// RenderNotFound renders a 404 Not Found error
func RenderNotFound(c *gin.Context, resource string) {
	RenderError(c, http.StatusNotFound, NotFoundError(resource))
}

// RenderConflict renders a 409 Conflict error
func RenderConflict(c *gin.Context, detail string) {
	RenderError(c, http.StatusConflict, ConflictError(detail))
}

// RenderUnprocessableEntity renders a 422 Unprocessable Entity error
func RenderUnprocessableEntity(c *gin.Context, detail string, source map[string]interface{}) {
	RenderError(c, http.StatusUnprocessableEntity, ValidationError(detail, source))
}

// RenderInternalError renders a 500 Internal Server Error
func RenderInternalError(c *gin.Context, detail string) {
	RenderError(c, http.StatusInternalServerError, InternalError(detail))
}

// RenderServiceUnavailable renders a 503 Service Unavailable error
func RenderServiceUnavailable(c *gin.Context, detail string) {
	err := SingleError(ErrorCodeServiceUnavailable, "Service Unavailable", detail)
	RenderError(c, http.StatusServiceUnavailable, err)
}