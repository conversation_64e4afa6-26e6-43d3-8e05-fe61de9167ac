package responses

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
)

// CRUDResponse provides standardized CRUD response helpers
type CRUDResponse struct{}

// NewCRUDResponse creates a new CRUD response helper
func NewCRUDResponse() *CRUDResponse {
	return &CRUDResponse{}
}

// RenderCreated renders a 201 Created response for newly created resources
func (cr *CRUDResponse) RenderCreated(c *gin.Context, resource *ResourceObject, location string) {
	if location != "" {
		c.Header("Location", location)
	}
	RenderSingle(c, http.StatusCreated, resource, nil)
}

// RenderUpdated renders a 200 OK response for updated resources
func (cr *CRUDResponse) RenderUpdated(c *gin.Context, resource *ResourceObject) {
	RenderSingle(c, http.StatusOK, resource, nil)
}

// RenderDeleted renders a 204 No Content response for deleted resources
func (cr *CRUDResponse) RenderDeleted(c *gin.Context) {
	c.Status(http.StatusNoContent)
}

// RenderList renders a paginated list of resources
func (cr *CRUDResponse) RenderList(c *gin.Context, resources []interface{}, pagination *PaginationMeta) {
	RenderCollection(c, http.StatusOK, resources, pagination, nil)
}

// Entity conversion helpers

// ConvertAccount converts an Account entity to a resource object
func ConvertAccount(account *entities.Account) *ResourceObject {
	if account == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"name":       account.Name,
		"slug":       account.Slug,
		"created_at": account.CreatedAt,
		"updated_at": account.UpdatedAt,
	}

	if account.Metadata != nil {
		attributes["metadata"] = account.Metadata
	}

	return ToResourceObject(ResourceTypeAccount, account.ID, attributes)
}

// ConvertProduct converts a Product entity to a resource object
func ConvertProduct(product *entities.Product) *ResourceObject {
	if product == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"name":       product.Name,
		"created_at": product.CreatedAt,
		"updated_at": product.UpdatedAt,
	}

	if product.Code != "" {
		attributes["code"] = product.Code
	}
	if product.DistributionStrategy != nil {
		attributes["distribution_strategy"] = *product.DistributionStrategy
	}
	if product.URL != "" {
		attributes["url"] = product.URL
	}
	// Platforms and Metadata are structs, not pointers, so they're always present
	attributes["platforms"] = product.Platforms
	attributes["metadata"] = product.Metadata

	return ToResourceObject(ResourceTypeProduct, product.ID, attributes)
}

// ConvertPolicy converts a Policy entity to a resource object
func ConvertPolicy(policy *entities.Policy) *ResourceObject {
	if policy == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"name":       policy.Name,
		"created_at": policy.CreatedAt,
		"updated_at": policy.UpdatedAt,
	}

	// Add all policy fields
	if policy.Duration != nil {
		attributes["duration"] = *policy.Duration
	}
	// These are not pointers, always include them
	attributes["strict"] = policy.Strict
	attributes["floating"] = policy.Floating
	attributes["scheme"] = policy.Scheme
	attributes["require_heartbeat"] = policy.RequireHeartbeat
	if policy.HeartbeatDuration != nil {
		attributes["heartbeat_duration"] = *policy.HeartbeatDuration
	}
	if policy.HeartbeatCullStrategy != nil {
		attributes["heartbeat_cull_strategy"] = *policy.HeartbeatCullStrategy
	}
	if policy.HeartbeatResurrectionStrategy != nil {
		attributes["heartbeat_resurrection_strategy"] = *policy.HeartbeatResurrectionStrategy
	}
	if policy.HeartbeatBasis != nil {
		attributes["heartbeat_basis"] = *policy.HeartbeatBasis
	}
	if policy.MachineUniquenessStrategy != nil {
		attributes["machine_uniqueness_strategy"] = *policy.MachineUniquenessStrategy
	}
	if policy.MachineMatchingStrategy != nil {
		attributes["machine_matching_strategy"] = *policy.MachineMatchingStrategy
	}
	if policy.ComponentUniquenessStrategy != nil {
		attributes["component_uniqueness_strategy"] = *policy.ComponentUniquenessStrategy
	}
	if policy.ComponentMatchingStrategy != nil {
		attributes["component_matching_strategy"] = *policy.ComponentMatchingStrategy
	}
	if policy.ExpirationStrategy != nil {
		attributes["expiration_strategy"] = *policy.ExpirationStrategy
	}
	if policy.ExpirationBasis != nil {
		attributes["expiration_basis"] = *policy.ExpirationBasis
	}
	if policy.RenewalBasis != nil {
		attributes["renewal_basis"] = *policy.RenewalBasis
	}
	if policy.TransferStrategy != nil {
		attributes["transfer_strategy"] = *policy.TransferStrategy
	}
	if policy.AuthenticationStrategy != nil {
		attributes["authentication_strategy"] = *policy.AuthenticationStrategy
	}
	if policy.MachineLeasingStrategy != nil {
		attributes["machine_leasing_strategy"] = *policy.MachineLeasingStrategy
	}
	if policy.ProcessLeasingStrategy != nil {
		attributes["process_leasing_strategy"] = *policy.ProcessLeasingStrategy
	}
	if policy.OverageStrategy != nil {
		attributes["overage_strategy"] = *policy.OverageStrategy
	}
	if policy.MaxMachines != nil {
		attributes["max_machines"] = *policy.MaxMachines
	}
	if policy.MaxProcesses != nil {
		attributes["max_processes"] = *policy.MaxProcesses
	}
	if policy.MaxUsers != nil {
		attributes["max_users"] = *policy.MaxUsers
	}
	if policy.MaxCores != nil {
		attributes["max_cores"] = *policy.MaxCores
	}
	if policy.MaxUses != nil {
		attributes["max_uses"] = *policy.MaxUses
	}
	if policy.Protected != nil {
		attributes["protected"] = *policy.Protected
	}
	attributes["require_check_in"] = policy.RequireCheckIn
	if policy.CheckInInterval != nil {
		attributes["check_in_interval"] = *policy.CheckInInterval
	}
	if policy.CheckInIntervalCount != nil {
		attributes["check_in_interval_count"] = *policy.CheckInIntervalCount
	}
	// These are boolean fields, not pointers
	attributes["use_pool"] = policy.UsePool
	attributes["encrypted"] = policy.Encrypted
	attributes["concurrent"] = policy.Concurrent

	// Add fingerprint strategies
	if policy.FingerprintUniquenessStrategy != nil {
		attributes["fingerprint_uniqueness_strategy"] = *policy.FingerprintUniquenessStrategy
	}
	if policy.FingerprintMatchingStrategy != nil {
		attributes["fingerprint_matching_strategy"] = *policy.FingerprintMatchingStrategy
	}

	// Add leasing strategy
	if policy.LeasingStrategy != nil {
		attributes["leasing_strategy"] = *policy.LeasingStrategy
	}

	// Add scope requirements (all boolean fields)
	attributes["require_product_scope"] = policy.RequireProductScope
	attributes["require_policy_scope"] = policy.RequirePolicyScope
	attributes["require_machine_scope"] = policy.RequireMachineScope
	attributes["require_fingerprint_scope"] = policy.RequireFingerprintScope
	attributes["require_user_scope"] = policy.RequireUserScope
	attributes["require_environment_scope"] = policy.RequireEnvironmentScope
	attributes["require_checksum_scope"] = policy.RequireChecksumScope
	attributes["require_version_scope"] = policy.RequireVersionScope
	attributes["require_components_scope"] = policy.RequireComponentsScope

	// Metadata is a struct, not a pointer
	attributes["metadata"] = policy.Metadata

	return ToResourceObject(ResourceTypePolicy, policy.ID, attributes)
}

// ConvertMachine converts a Machine entity to a resource object
func ConvertMachine(machine *entities.Machine) *ResourceObject {
	if machine == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"fingerprint": machine.Fingerprint,
		"created_at":  machine.CreatedAt,
		"updated_at":  machine.UpdatedAt,
	}

	if machine.Name != "" {
		attributes["name"] = machine.Name
	}
	if machine.Platform != "" {
		attributes["platform"] = machine.Platform
	}
	if machine.Hostname != "" {
		attributes["hostname"] = machine.Hostname
	}
	if machine.IP != "" {
		attributes["ip"] = machine.IP
	}
	if machine.Cores > 0 {
		attributes["cores"] = machine.Cores
	}
	if machine.LastHeartbeatAt != nil {
		attributes["last_heartbeat_at"] = *machine.LastHeartbeatAt
	}
	// NextHeartbeatAt field doesn't exist in Machine entity
	// Remove this or calculate from LastHeartbeatAt + interval
	if machine.Metadata != nil {
		attributes["metadata"] = machine.Metadata
	}

	return ToResourceObject(ResourceTypeMachine, machine.ID, attributes)
}

// ConvertLicense converts a License entity to a resource object
func ConvertLicense(license *entities.License) *ResourceObject {
	if license == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"key":        license.Key,
		"created_at": license.CreatedAt,
		"updated_at": license.UpdatedAt,
	}

	if license.Name != "" {
		attributes["name"] = license.Name
	}
	if license.ExpiresAt != nil {
		attributes["expires_at"] = *license.ExpiresAt
	}
	if license.LastValidatedAt != nil {
		attributes["last_validated_at"] = *license.LastValidatedAt
	}
	if license.LastCheckOutAt != nil {
		attributes["last_check_out_at"] = *license.LastCheckOutAt
	}
	// UsesCount field doesn't exist in License entity
	// This would need to be calculated from usage tracking
	if license.Protected != nil {
		attributes["protected"] = *license.Protected
	}
	attributes["suspended"] = license.Suspended
	if license.Metadata != nil {
		attributes["metadata"] = license.Metadata
	}

	return ToResourceObject(ResourceTypeLicense, license.ID, attributes)
}

// ConvertEntitlement converts an Entitlement entity to a resource object
func ConvertEntitlement(entitlement *entities.Entitlement) *ResourceObject {
	if entitlement == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"name":       entitlement.Name,
		"code":       entitlement.Code,
		"created_at": entitlement.CreatedAt,
		"updated_at": entitlement.UpdatedAt,
	}

	if entitlement.Metadata != nil {
		attributes["metadata"] = entitlement.Metadata
	}

	return ToResourceObject(ResourceTypeEntitlement, entitlement.ID, attributes)
}

// ConvertGroup converts a Group entity to a resource object
func ConvertGroup(group *entities.Group) *ResourceObject {
	if group == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"name":       group.Name,
		"created_at": group.CreatedAt,
		"updated_at": group.UpdatedAt,
	}

	if group.MaxUsers != nil {
		attributes["max_users"] = *group.MaxUsers
	}
	if group.MaxLicenses != nil {
		attributes["max_licenses"] = *group.MaxLicenses
	}
	if group.MaxMachines != nil {
		attributes["max_machines"] = *group.MaxMachines
	}
	if group.Metadata != nil {
		attributes["metadata"] = group.Metadata
	}

	return ToResourceObject(ResourceTypeGroup, group.ID, attributes)
}

// ConvertPlan converts a Plan entity to a resource object
func ConvertPlan(plan *entities.Plan) *ResourceObject {
	if plan == nil {
		return nil
	}

	attributes := map[string]interface{}{
		"name":       plan.Name,
		"created_at": plan.CreatedAt,
		"updated_at": plan.UpdatedAt,
	}

	if plan.Price > 0 {
		attributes["price"] = plan.Price
	}
	// Plan entity doesn't have Metadata field

	return ToResourceObject(ResourceTypePlan, plan.ID, attributes)
}

// TODO: Add ConvertWebhookEndpoint when WebhookEndpoint entity is implemented
