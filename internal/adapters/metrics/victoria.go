package metrics

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/common/expfmt"
	"github.com/gokeys/gokeys/internal/config"
)

// VictoriaMetricsExporter exports metrics to VictoriaMetrics
type VictoriaMetricsExporter struct {
	config      *config.Config
	httpClient  *http.Client
	endpoint    string
	
	// Export state
	ticker      *time.Ticker
	stopCh      chan struct{}
	isRunning   bool
	mu          sync.RWMutex
	
	// Metrics for monitoring the exporter itself
	exportCounter   prometheus.Counter
	exportErrors    prometheus.Counter
	exportDuration  prometheus.Histogram
	lastExportTime  prometheus.Gauge
}

// NewVictoriaMetricsExporter creates a new VictoriaMetrics exporter
func NewVictoriaMetricsExporter(cfg *config.Config) *VictoriaMetricsExporter {
	exporter := &VictoriaMetricsExporter{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		endpoint: cfg.Metrics.VictoriaMetrics.Endpoint,
		stopCh:   make(chan struct{}),
		
		// Initialize exporter metrics
		exportCounter: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "gokeys_vm_exports_total",
			Help: "Total number of VictoriaMetrics exports",
		}),
		exportErrors: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "gokeys_vm_export_errors_total", 
			Help: "Total number of VictoriaMetrics export errors",
		}),
		exportDuration: prometheus.NewHistogram(prometheus.HistogramOpts{
			Name:    "gokeys_vm_export_duration_seconds",
			Help:    "Duration of VictoriaMetrics exports",
			Buckets: prometheus.DefBuckets,
		}),
		lastExportTime: prometheus.NewGauge(prometheus.GaugeOpts{
			Name: "gokeys_vm_last_export_timestamp",
			Help: "Timestamp of last successful VictoriaMetrics export",
		}),
	}
	
	// Register exporter metrics
	prometheus.MustRegister(
		exporter.exportCounter,
		exporter.exportErrors,
		exporter.exportDuration,
		exporter.lastExportTime,
	)
	
	return exporter
}

// Export exports metrics to VictoriaMetrics with retry logic
func (vme *VictoriaMetricsExporter) Export() error {
	return vme.ExportWithContext(context.Background())
}

// ExportWithContext exports metrics to VictoriaMetrics with context and retry logic
func (vme *VictoriaMetricsExporter) ExportWithContext(ctx context.Context) error {
	if !vme.config.Metrics.VictoriaMetrics.Enabled {
		return nil
	}

	start := time.Now()
	vme.exportCounter.Inc()
	
	defer func() {
		vme.exportDuration.Observe(time.Since(start).Seconds())
	}()

	// Retry logic
	maxRetries := 3
	baseDelay := time.Second
	
	for attempt := 0; attempt < maxRetries; attempt++ {
		if err := vme.doExport(ctx); err != nil {
			if attempt == maxRetries-1 {
				vme.exportErrors.Inc()
				return fmt.Errorf("failed to export metrics after %d attempts: %w", maxRetries, err)
			}
			
			// Exponential backoff
			delay := baseDelay * time.Duration(1<<attempt)
			log.Printf("VictoriaMetrics export attempt %d failed, retrying in %v: %v", attempt+1, delay, err)
			
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(delay):
				continue
			}
		} else {
			// Success
			vme.lastExportTime.SetToCurrentTime()
			return nil
		}
	}
	
	return nil
}

// doExport performs the actual export operation
func (vme *VictoriaMetricsExporter) doExport(ctx context.Context) error {
	// Gather metrics from Prometheus registry
	metricFamilies, err := prometheus.DefaultGatherer.Gather()
	if err != nil {
		return fmt.Errorf("failed to gather metrics: %w", err)
	}

	// Convert metrics to Prometheus exposition format
	var buf bytes.Buffer
	for _, mf := range metricFamilies {
		if _, err := expfmt.MetricFamilyToText(&buf, mf); err != nil {
			return fmt.Errorf("failed to convert metrics to text: %w", err)
		}
	}

	// Create request with context
	req, err := http.NewRequestWithContext(ctx, "POST", vme.endpoint+"/api/v1/import/prometheus", &buf)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set authentication if configured
	if vme.config.Metrics.VictoriaMetrics.Username != "" {
		req.SetBasicAuth(
			vme.config.Metrics.VictoriaMetrics.Username,
			vme.config.Metrics.VictoriaMetrics.Password,
		)
	}

	req.Header.Set("Content-Type", "text/plain")
	req.Header.Set("User-Agent", "gokeys-metrics-exporter/1.0")

	resp, err := vme.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send metrics: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("VictoriaMetrics returned status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

// StartPeriodicExport starts periodic export of metrics to VictoriaMetrics
func (vme *VictoriaMetricsExporter) StartPeriodicExport(interval time.Duration) {
	if !vme.config.Metrics.VictoriaMetrics.Enabled {
		return
	}

	vme.mu.Lock()
	defer vme.mu.Unlock()
	
	if vme.isRunning {
		log.Println("VictoriaMetrics exporter is already running")
		return
	}

	vme.ticker = time.NewTicker(interval)
	vme.isRunning = true
	
	go func() {
		defer func() {
			vme.mu.Lock()
			vme.isRunning = false
			vme.mu.Unlock()
		}()
		
		// Initial export
		if err := vme.Export(); err != nil {
			log.Printf("Initial VictoriaMetrics export failed: %v", err)
		}
		
		for {
			select {
			case <-vme.ticker.C:
				if err := vme.Export(); err != nil {
					log.Printf("Failed to export metrics to VictoriaMetrics: %v", err)
				}
			case <-vme.stopCh:
				log.Println("VictoriaMetrics exporter stopped")
				return
			}
		}
	}()

	log.Printf("Started periodic metrics export to VictoriaMetrics (interval: %v)", interval)
}

// Stop stops the periodic export
func (vme *VictoriaMetricsExporter) Stop() {
	vme.mu.Lock()
	defer vme.mu.Unlock()
	
	if !vme.isRunning {
		return
	}
	
	if vme.ticker != nil {
		vme.ticker.Stop()
	}
	
	close(vme.stopCh)
	vme.isRunning = false
	log.Println("VictoriaMetrics exporter stopped")
}

// IsRunning returns whether the exporter is currently running
func (vme *VictoriaMetricsExporter) IsRunning() bool {
	vme.mu.RLock()
	defer vme.mu.RUnlock()
	return vme.isRunning
}

// CustomMetrics provides business-specific metrics for GoKeys
type CustomMetrics struct {
	collector *MetricsCollector
}

// NewCustomMetrics creates custom metrics instance
func NewCustomMetrics(collector *MetricsCollector) *CustomMetrics {
	return &CustomMetrics{
		collector: collector,
	}
}

// RecordLicenseValidation records a license validation event
func (cm *CustomMetrics) RecordLicenseValidation(accountID, productID, result string) {
	cm.collector.LicenseValidations.WithLabelValues(accountID, productID, result).Inc()
}

// RecordLicenseActivation records a license activation event
func (cm *CustomMetrics) RecordLicenseActivation(accountID, productID string) {
	cm.collector.LicenseActivations.WithLabelValues(accountID, productID).Inc()
}

// RecordMachineHeartbeat records a machine heartbeat
func (cm *CustomMetrics) RecordMachineHeartbeat(accountID, machineID string) {
	cm.collector.MachineHeartbeats.WithLabelValues(accountID, machineID).Inc()
}

// RecordMachineRegistration records a machine registration
func (cm *CustomMetrics) RecordMachineRegistration(accountID, licenseID string) {
	cm.collector.MachineRegistrations.WithLabelValues(accountID, licenseID).Inc()
}

// RecordDatabaseQuery records a database query
func (cm *CustomMetrics) RecordDatabaseQuery(operation, table string, duration time.Duration) {
	cm.collector.DatabaseQueries.WithLabelValues(operation, table).Inc()
	cm.collector.DatabaseQueryDuration.WithLabelValues(operation, table).Observe(duration.Seconds())
}

// RecordCacheOperation records a cache operation
func (cm *CustomMetrics) RecordCacheOperation(operation, result string) {
	cm.collector.CacheOperations.WithLabelValues(operation, result).Inc()
}

// UpdateLicenseCount updates the total license count
func (cm *CustomMetrics) UpdateLicenseCount(accountID, productID, status string, count float64) {
	cm.collector.LicensesTotal.WithLabelValues(accountID, productID, status).Set(count)
}

// UpdateActiveMachineCount updates the active machine count
func (cm *CustomMetrics) UpdateActiveMachineCount(accountID, licenseID string, count float64) {
	cm.collector.MachinesActive.WithLabelValues(accountID, licenseID).Set(count)
}

// UpdateBusinessCounts updates business entity counts
func (cm *CustomMetrics) UpdateBusinessCounts(accounts, products, policies, users float64) {
	cm.collector.AccountsTotal.Set(accounts)
	cm.collector.ProductsTotal.Set(products)
	cm.collector.PoliciesTotal.Set(policies)
	cm.collector.UsersTotal.Set(users)
}

// UpdateDatabaseConnections updates database connection metrics
func (cm *CustomMetrics) UpdateDatabaseConnections(idle, active, total float64) {
	cm.collector.DatabaseConnections.WithLabelValues("idle").Set(idle)
	cm.collector.DatabaseConnections.WithLabelValues("active").Set(active)
	cm.collector.DatabaseConnections.WithLabelValues("total").Set(total)
}

// UpdateCacheHitRatio updates cache hit ratio
func (cm *CustomMetrics) UpdateCacheHitRatio(cacheType string, ratio float64) {
	cm.collector.CacheHitRatio.WithLabelValues(cacheType).Set(ratio)
}

// Enterprise License Management Metrics

// RecordLicenseExpiration records a license expiration event
func (cm *CustomMetrics) RecordLicenseExpiration(accountID, productID string) {
	cm.collector.LicenseExpirations.WithLabelValues(accountID, productID).Inc()
}

// RecordLicenseValidationLatency records license validation latency
func (cm *CustomMetrics) RecordLicenseValidationLatency(duration time.Duration) {
	cm.collector.HTTPRequestDuration.WithLabelValues("POST", "/api/v1/licenses/validate").Observe(duration.Seconds())
}

// RecordEnvironmentIsolation tracks environment isolation metrics
func (cm *CustomMetrics) RecordEnvironmentIsolation(accountID, environmentID, isolationStrategy string) {
	// This could be tracked via custom metrics in the future
	log.Printf("Environment isolation: account=%s, env=%s, strategy=%s", accountID, environmentID, isolationStrategy)
}

// RecordPolicyOverride tracks when policy overrides are used
func (cm *CustomMetrics) RecordPolicyOverride(accountID, licenseID, overrideType string) {
	// Track policy override usage for compliance and monitoring
	log.Printf("Policy override used: account=%s, license=%s, type=%s", accountID, licenseID, overrideType)
}

// RecordSecurityEvent tracks security-related events
func (cm *CustomMetrics) RecordSecurityEvent(eventType, accountID string) {
	// Could be expanded to track various security events
	log.Printf("Security event: type=%s, account=%s", eventType, accountID)
}

// RecordAPIRateLimit tracks API rate limiting events
func (cm *CustomMetrics) RecordAPIRateLimit(accountID, endpoint string, limited bool) {
	status := "allowed"
	if limited {
		status = "limited"
	}
	// This would need a new metric in MetricsCollector for proper tracking
	log.Printf("API rate limit: account=%s, endpoint=%s, status=%s", accountID, endpoint, status)
}

// RecordWebhookDelivery tracks webhook delivery success/failure
func (cm *CustomMetrics) RecordWebhookDelivery(accountID, eventType string, success bool) {
	result := "success"
	if !success {
		result = "failure"
	}
	// This would need a new metric in MetricsCollector for proper tracking
	log.Printf("Webhook delivery: account=%s, event=%s, result=%s", accountID, eventType, result)
}

// RecordMultiTenancy tracks multi-tenancy metrics
func (cm *CustomMetrics) RecordMultiTenancy(accountID, environmentID string, resourceCount int) {
	// Track resource usage per environment for multi-tenancy monitoring
	log.Printf("Multi-tenancy: account=%s, environment=%s, resources=%d", accountID, environmentID, resourceCount)
}

// Batch metrics update for periodic collection
func (cm *CustomMetrics) UpdatePeriodicMetrics(stats map[string]float64) {
	for metric, value := range stats {
		switch metric {
		case "total_accounts":
			cm.collector.AccountsTotal.Set(value)
		case "total_products":
			cm.collector.ProductsTotal.Set(value)
		case "total_policies":
			cm.collector.PoliciesTotal.Set(value)
		case "total_users":
			cm.collector.UsersTotal.Set(value)
		default:
			log.Printf("Unknown periodic metric: %s=%f", metric, value)
		}
	}
}