package metrics

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/gokeys/gokeys/internal/config"
)

// MetricsCollector holds all Prometheus metrics
type MetricsCollector struct {
	// HTTP metrics
	HTTPRequestsTotal     *prometheus.CounterVec
	HTTPRequestDuration   *prometheus.HistogramVec
	HTTPRequestsInFlight  prometheus.Gauge

	// License metrics
	LicensesTotal         *prometheus.GaugeVec
	LicenseValidations    *prometheus.CounterVec
	LicenseActivations    *prometheus.CounterVec
	LicenseExpirations    *prometheus.CounterVec

	// Machine metrics
	MachinesActive        *prometheus.GaugeVec
	MachineHeartbeats     *prometheus.CounterVec
	MachineRegistrations  *prometheus.CounterVec

	// Database metrics
	DatabaseConnections   *prometheus.GaugeVec
	DatabaseQueries       *prometheus.CounterVec
	DatabaseQueryDuration *prometheus.HistogramVec

	// Cache metrics
	CacheOperations       *prometheus.CounterVec
	CacheHitRatio         *prometheus.GaugeVec

	// Business metrics
	AccountsTotal         prometheus.Gauge
	ProductsTotal         prometheus.Gauge
	PoliciesTotal         prometheus.Gauge
	UsersTotal            prometheus.Gauge
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector() *MetricsCollector {
	mc := &MetricsCollector{
		// HTTP metrics
		HTTPRequestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "path", "status"},
		),
		HTTPRequestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "gokeys_http_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "path"},
		),
		HTTPRequestsInFlight: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "gokeys_http_requests_in_flight",
				Help: "Number of HTTP requests currently being served",
			},
		),

		// License metrics
		LicensesTotal: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "gokeys_licenses_total",
				Help: "Total number of licenses by status",
			},
			[]string{"account", "product", "status"},
		),
		LicenseValidations: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_license_validations_total",
				Help: "Total number of license validation requests",
			},
			[]string{"account", "product", "result"},
		),
		LicenseActivations: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_license_activations_total",
				Help: "Total number of license activations",
			},
			[]string{"account", "product"},
		),
		LicenseExpirations: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_license_expirations_total",
				Help: "Total number of license expirations",
			},
			[]string{"account", "product"},
		),

		// Machine metrics
		MachinesActive: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "gokeys_machines_active",
				Help: "Number of active machines",
			},
			[]string{"account", "license"},
		),
		MachineHeartbeats: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_machine_heartbeats_total",
				Help: "Total number of machine heartbeats",
			},
			[]string{"account", "machine"},
		),
		MachineRegistrations: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_machine_registrations_total",
				Help: "Total number of machine registrations",
			},
			[]string{"account", "license"},
		),

		// Database metrics
		DatabaseConnections: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "gokeys_database_connections",
				Help: "Number of database connections",
			},
			[]string{"state"},
		),
		DatabaseQueries: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_database_queries_total",
				Help: "Total number of database queries",
			},
			[]string{"operation", "table"},
		),
		DatabaseQueryDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "gokeys_database_query_duration_seconds",
				Help:    "Database query duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation", "table"},
		),

		// Cache metrics
		CacheOperations: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gokeys_cache_operations_total",
				Help: "Total number of cache operations",
			},
			[]string{"operation", "result"},
		),
		CacheHitRatio: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "gokeys_cache_hit_ratio",
				Help: "Cache hit ratio",
			},
			[]string{"cache_type"},
		),

		// Business metrics
		AccountsTotal: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "gokeys_accounts_total",
				Help: "Total number of accounts",
			},
		),
		ProductsTotal: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "gokeys_products_total",
				Help: "Total number of products",
			},
		),
		PoliciesTotal: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "gokeys_policies_total",
				Help: "Total number of policies",
			},
		),
		UsersTotal: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "gokeys_users_total",
				Help: "Total number of users",
			},
		),
	}

	// Register all metrics
	mc.register()

	return mc
}

// register registers all metrics with Prometheus
func (mc *MetricsCollector) register() {
	prometheus.MustRegister(
		mc.HTTPRequestsTotal,
		mc.HTTPRequestDuration,
		mc.HTTPRequestsInFlight,
		mc.LicensesTotal,
		mc.LicenseValidations,
		mc.LicenseActivations,
		mc.LicenseExpirations,
		mc.MachinesActive,
		mc.MachineHeartbeats,
		mc.MachineRegistrations,
		mc.DatabaseConnections,
		mc.DatabaseQueries,
		mc.DatabaseQueryDuration,
		mc.CacheOperations,
		mc.CacheHitRatio,
		mc.AccountsTotal,
		mc.ProductsTotal,
		mc.PoliciesTotal,
		mc.UsersTotal,
	)
}

// MetricsMiddleware returns a Gin middleware for collecting HTTP metrics
func (mc *MetricsCollector) MetricsMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := prometheus.NewTimer(mc.HTTPRequestDuration.WithLabelValues(c.Request.Method, c.FullPath()))
		mc.HTTPRequestsInFlight.Inc()

		c.Next()

		mc.HTTPRequestsInFlight.Dec()
		start.ObserveDuration()
		mc.HTTPRequestsTotal.WithLabelValues(c.Request.Method, c.FullPath(), fmt.Sprintf("%d", c.Writer.Status())).Inc()
	})
}

// MetricsServer creates a metrics HTTP server
type MetricsServer struct {
	config *config.Config
	server *http.Server
}

// NewMetricsServer creates a new metrics server
func NewMetricsServer(cfg *config.Config) *MetricsServer {
	mux := http.NewServeMux()
	mux.Handle(cfg.Metrics.Path, promhttp.Handler())
	
	// Health check for metrics server
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Metrics.Port),
		Handler: mux,
	}

	return &MetricsServer{
		config: cfg,
		server: server,
	}
}

// Start starts the metrics server
func (ms *MetricsServer) Start() error {
	if !ms.config.Metrics.Enabled {
		return nil
	}

	fmt.Printf("Starting metrics server on port %d\n", ms.config.Metrics.Port)
	return ms.server.ListenAndServe()
}

// Stop stops the metrics server
func (ms *MetricsServer) Stop() error {
	return ms.server.Close()
}