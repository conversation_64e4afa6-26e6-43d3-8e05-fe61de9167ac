package database

import (
	"context"

	"github.com/gokeys/gokeys/internal/domain/entities"
)

// AccountRepository defines the interface for account data operations
type AccountRepository interface {
	Create(ctx context.Context, account *entities.Account) error
	GetByID(ctx context.Context, id string) (*entities.Account, error)
	GetByEmail(ctx context.Context, email string) (*entities.Account, error)
	Update(ctx context.Context, account *entities.Account) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, offset, limit int) ([]*entities.Account, error)
}

// UserRepository defines the interface for user data operations
type UserRepository interface {
	Create(ctx context.Context, user *entities.User) error
	GetByID(ctx context.Context, id string) (*entities.User, error)
	GetByEmail(ctx context.Context, email string) (*entities.User, error)
	GetByAccountID(ctx context.Context, accountID string, offset, limit int) ([]*entities.User, error)
	Update(ctx context.Context, user *entities.User) error
	Delete(ctx context.Context, id string) error
	UpdateLastLogin(ctx context.Context, id string) error
}

// ProductRepository defines the interface for product data operations
type ProductRepository interface {
	Create(ctx context.Context, product *entities.Product) error
	GetByID(ctx context.Context, id string) (*entities.Product, error)
	GetByKey(ctx context.Context, key string) (*entities.Product, error)
	GetByAccountID(ctx context.Context, accountID string, offset, limit int) ([]*entities.Product, error)
	Update(ctx context.Context, product *entities.Product) error
	Delete(ctx context.Context, id string) error
}

// LicenseRepository defines the interface for license data operations
type LicenseRepository interface {
	Create(ctx context.Context, license *entities.License) error
	GetByID(ctx context.Context, id string) (*entities.License, error)
	GetByKey(ctx context.Context, key string) (*entities.License, error)
	GetByAccountID(ctx context.Context, accountID string, offset, limit int) ([]*entities.License, error)
	GetByProductID(ctx context.Context, productID string, offset, limit int) ([]*entities.License, error)
	Update(ctx context.Context, license *entities.License) error
	Delete(ctx context.Context, id string) error
	UpdateUsage(ctx context.Context, id string, increment int) error
}

// PolicyRepository defines the interface for policy data operations
type PolicyRepository interface {
	Create(ctx context.Context, policy *entities.Policy) error
	GetByID(ctx context.Context, id string) (*entities.Policy, error)
	GetByAccountID(ctx context.Context, accountID string, offset, limit int) ([]*entities.Policy, error)
	GetByProductID(ctx context.Context, productID string, offset, limit int) ([]*entities.Policy, error)
	Update(ctx context.Context, policy *entities.Policy) error
	Delete(ctx context.Context, id string) error
}

// MachineRepository defines the interface for machine data operations
type MachineRepository interface {
	Create(ctx context.Context, machine *entities.Machine) error
	GetByID(ctx context.Context, id string) (*entities.Machine, error)
	GetByFingerprint(ctx context.Context, fingerprint string) (*entities.Machine, error)
	GetByLicenseID(ctx context.Context, licenseID string, offset, limit int) ([]*entities.Machine, error)
	GetActiveMachinesByLicenseID(ctx context.Context, licenseID string) ([]*entities.Machine, error)
	Update(ctx context.Context, machine *entities.Machine) error
	Delete(ctx context.Context, id string) error
	UpdateHeartbeat(ctx context.Context, id string) error
	Activate(ctx context.Context, id string) error
	Deactivate(ctx context.Context, id string) error
}

// TokenRepository defines the interface for token data operations
type TokenRepository interface {
	Create(ctx context.Context, token *entities.Token) error
	GetByID(ctx context.Context, id string) (*entities.Token, error)
	GetByToken(ctx context.Context, tokenValue string) (*entities.Token, error)
	GetByBearerID(ctx context.Context, bearerID string, bearerType entities.TokenBearerType, offset, limit int) ([]*entities.Token, error)
	Update(ctx context.Context, token *entities.Token) error
	Delete(ctx context.Context, id string) error
	UpdateLastUsed(ctx context.Context, id string) error
}