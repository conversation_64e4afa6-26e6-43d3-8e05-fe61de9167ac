package repositories

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseRepository defines common repository operations
type BaseRepository[T any] interface {
	Create(ctx context.Context, entity *T) error
	GetByID(ctx context.Context, id uuid.UUID) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filter ListFilter) ([]*T, int64, error)
	Count(ctx context.Context, filter ListFilter) (int64, error)
	Exists(ctx context.Context, id uuid.UUID) (bool, error)
}

// ListFilter represents common filtering options
type ListFilter struct {
	Page           int
	PageSize       int
	SortBy         string
	SortOrder      string // ASC or DESC
	Search         string
	Filters        map[string]interface{}
	AccountID      *uuid.UUID
	EnvironmentID  *uuid.UUID
	IncludeDeleted bool
}

// DefaultListFilter returns a default list filter
func DefaultListFilter() ListFilter {
	return ListFilter{
		Page:      1,
		PageSize:  20,
		SortBy:    "created_at",
		SortOrder: "DESC",
		Filters:   make(map[string]interface{}),
	}
}

// BaseRepositoryImpl provides base implementation for common operations
type BaseRepositoryImpl[T any] struct {
	db *gorm.DB
}

// NewBaseRepository creates a new base repository
func NewBaseRepository[T any](db *gorm.DB) *BaseRepositoryImpl[T] {
	return &BaseRepositoryImpl[T]{
		db: db,
	}
}

// Create creates a new entity
func (r *BaseRepositoryImpl[T]) Create(ctx context.Context, entity *T) error {
	return r.db.WithContext(ctx).Create(entity).Error
}

// GetByID retrieves an entity by ID
func (r *BaseRepositoryImpl[T]) GetByID(ctx context.Context, id uuid.UUID) (*T, error) {
	var entity T
	err := r.db.WithContext(ctx).First(&entity, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

// Update updates an entity
func (r *BaseRepositoryImpl[T]) Update(ctx context.Context, entity *T) error {
	return r.db.WithContext(ctx).Save(entity).Error
}

// Delete permanently deletes an entity
func (r *BaseRepositoryImpl[T]) Delete(ctx context.Context, id uuid.UUID) error {
	var entity T
	return r.db.WithContext(ctx).Unscoped().Delete(&entity, "id = ?", id).Error
}

// SoftDelete soft deletes an entity
func (r *BaseRepositoryImpl[T]) SoftDelete(ctx context.Context, id uuid.UUID) error {
	var entity T
	return r.db.WithContext(ctx).Delete(&entity, "id = ?", id).Error
}

// List retrieves entities with filtering and pagination
func (r *BaseRepositoryImpl[T]) List(ctx context.Context, filter ListFilter) ([]*T, int64, error) {
	var entities []*T
	var total int64

	query := r.buildQuery(ctx, filter)

	// Count total records
	if err := query.Model(new(T)).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filter.Page - 1) * filter.PageSize
	query = query.Offset(offset).Limit(filter.PageSize)

	// Execute query
	if err := query.Find(&entities).Error; err != nil {
		return nil, 0, err
	}

	return entities, total, nil
}

// Count returns the total count of entities matching the filter
func (r *BaseRepositoryImpl[T]) Count(ctx context.Context, filter ListFilter) (int64, error) {
	var count int64
	query := r.buildQuery(ctx, filter)
	err := query.Model(new(T)).Count(&count).Error
	return count, err
}

// Exists checks if an entity exists by ID
func (r *BaseRepositoryImpl[T]) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(new(T)).Where("id = ?", id).Count(&count).Error
	return count > 0, err
}

// buildQuery builds a GORM query based on the filter
func (r *BaseRepositoryImpl[T]) buildQuery(ctx context.Context, filter ListFilter) *gorm.DB {
	query := r.db.WithContext(ctx)

	// Include soft deleted records if requested
	if filter.IncludeDeleted {
		query = query.Unscoped()
	}

	// Apply account filter
	if filter.AccountID != nil {
		query = query.Where("account_id = ?", *filter.AccountID)
	}

	// Apply environment filter
	if filter.EnvironmentID != nil {
		query = query.Where("environment_id = ?", *filter.EnvironmentID)
	}

	// Apply custom filters
	for key, value := range filter.Filters {
		query = query.Where(key, value)
	}

	// Apply search (this would need to be customized per entity type)
	if filter.Search != "" {
		// Generic search on name field if it exists
		query = query.Where("name ILIKE ?", "%"+filter.Search+"%")
	}

	// Apply sorting
	if filter.SortBy != "" {
		order := filter.SortBy
		if filter.SortOrder == "DESC" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	}

	return query
}

// Transaction wraps operations in a database transaction
func (r *BaseRepositoryImpl[T]) Transaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return r.db.WithContext(ctx).Transaction(fn)
}

// GetDB returns the underlying database connection
func (r *BaseRepositoryImpl[T]) GetDB() *gorm.DB {
	return r.db
}

// Repository collection interfaces for Hexagonal Architecture

// AccountRepository defines account-specific operations
type AccountRepository interface {
	BaseRepository[entities.Account]
	GetBySlug(ctx context.Context, slug string) (*entities.Account, error)
	GetByDomain(ctx context.Context, domain string) (*entities.Account, error)
	GetBySubdomain(ctx context.Context, subdomain string) (*entities.Account, error)
	UpdatePlan(ctx context.Context, accountID uuid.UUID, planID uuid.UUID) error
}

// ProductRepository defines product-specific operations
type ProductRepository interface {
	BaseRepository[entities.Product]
	GetByCode(ctx context.Context, code string, accountID uuid.UUID) (*entities.Product, error)
	GetByAccountID(ctx context.Context, accountID uuid.UUID) ([]*entities.Product, error)
}

// PolicyRepository defines policy-specific operations
type PolicyRepository interface {
	BaseRepository[entities.Policy]
	GetByProduct(ctx context.Context, productID uuid.UUID) ([]*entities.Policy, error)
	GetByScheme(ctx context.Context, scheme string, accountID uuid.UUID) ([]*entities.Policy, error)
}

// LicenseRepository defines license-specific operations
type LicenseRepository interface {
	BaseRepository[entities.License]
	GetByKey(ctx context.Context, key string) (*entities.License, error)
	GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.License, error)
	GetExpiring(ctx context.Context, accountID uuid.UUID, beforeDate time.Time) ([]*entities.License, error)
	UpdateLastValidated(ctx context.Context, licenseID uuid.UUID) error
	IncrementValidationCount(ctx context.Context, licenseID uuid.UUID) error
}

// MachineRepository defines machine-specific operations
type MachineRepository interface {
	BaseRepository[entities.Machine]
	GetByFingerprint(ctx context.Context, fingerprint string, accountID uuid.UUID) (*entities.Machine, error)
	GetByLicense(ctx context.Context, licenseID uuid.UUID) ([]*entities.Machine, error)
	UpdateHeartbeat(ctx context.Context, machineID uuid.UUID) error
	GetStale(ctx context.Context, accountID uuid.UUID, olderThan time.Time) ([]*entities.Machine, error)
}

// UserRepository defines user-specific operations
type UserRepository interface {
	BaseRepository[entities.User]
	GetByEmail(ctx context.Context, email string, accountID uuid.UUID) (*entities.User, error)
	GetByGroup(ctx context.Context, groupID uuid.UUID) ([]*entities.User, error)
	UpdateLastLogin(ctx context.Context, userID uuid.UUID) error
}

// TokenRepository defines token-specific operations
type TokenRepository interface {
	BaseRepository[entities.Token]
	GetByBearer(ctx context.Context, bearer string) (*entities.Token, error)
	GetByBearerTypeAndID(ctx context.Context, bearerType string, bearerID uuid.UUID) ([]*entities.Token, error)
	GetByAccount(ctx context.Context, accountID uuid.UUID) ([]*entities.Token, error)
	CleanupExpired(ctx context.Context) (int64, error)
	UpdateLastUsed(ctx context.Context, tokenID uuid.UUID) error
}

// SessionRepository defines session-specific operations
type SessionRepository interface {
	BaseRepository[entities.Session]
	GetByToken(ctx context.Context, tokenID uuid.UUID) (*entities.Session, error)
	CleanupExpired(ctx context.Context) (int64, error)
	UpdateLastUsed(ctx context.Context, sessionID uuid.UUID) error
}

// EnvironmentRepository defines environment-specific operations
type EnvironmentRepository interface {
	BaseRepository[entities.Environment]
	GetByCode(ctx context.Context, code string, accountID uuid.UUID) (*entities.Environment, error)
	GetByIsolationStrategy(ctx context.Context, strategy string, accountID uuid.UUID) ([]*entities.Environment, error)
}

// GroupRepository defines group-specific operations
type GroupRepository interface {
	BaseRepository[entities.Group]
	GetByName(ctx context.Context, name string, accountID uuid.UUID) (*entities.Group, error)
	GetWithUsers(ctx context.Context, groupID uuid.UUID) (*entities.Group, error)
}

// EntitlementRepository defines entitlement-specific operations
type EntitlementRepository interface {
	BaseRepository[entities.Entitlement]
	GetByCode(ctx context.Context, code string, accountID uuid.UUID) (*entities.Entitlement, error)
	GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.Entitlement, error)
}

// RoleRepository defines role-specific operations
type RoleRepository interface {
	BaseRepository[entities.Role]
	GetByResource(ctx context.Context, resourceType string, resourceID uuid.UUID, accountID uuid.UUID) ([]*entities.Role, error)
	GetUserRoles(ctx context.Context, userID uuid.UUID) ([]*entities.Role, error)
}

// PermissionRepository defines permission-specific operations
type PermissionRepository interface {
	BaseRepository[entities.Permission]
	GetByAction(ctx context.Context, action string) (*entities.Permission, error)
	GetByRole(ctx context.Context, roleID uuid.UUID) ([]*entities.Permission, error)
}

// PlanRepository defines plan-specific operations
type PlanRepository interface {
	BaseRepository[entities.Plan]
	GetByPlanID(ctx context.Context, planID string) (*entities.Plan, error)
	GetPublicPlans(ctx context.Context) ([]*entities.Plan, error)
}

// MachineComponentRepository defines machine component operations
type MachineComponentRepository interface {
	BaseRepository[entities.MachineComponent]
	GetByMachine(ctx context.Context, machineID uuid.UUID) ([]*entities.MachineComponent, error)
	GetByFingerprint(ctx context.Context, fingerprint string, machineID uuid.UUID) (*entities.MachineComponent, error)
}

// MachineProcessRepository defines machine process operations
type MachineProcessRepository interface {
	BaseRepository[entities.MachineProcess]
	GetByMachine(ctx context.Context, machineID uuid.UUID) ([]*entities.MachineProcess, error)
	GetByPID(ctx context.Context, pid int, machineID uuid.UUID) (*entities.MachineProcess, error)
	UpdateHeartbeat(ctx context.Context, processID uuid.UUID) error
}

// SecondFactorRepository defines second factor operations
type SecondFactorRepository interface {
	BaseRepository[entities.SecondFactor]
	GetByUser(ctx context.Context, userID uuid.UUID) ([]*entities.SecondFactor, error)
	GetEnabledByUser(ctx context.Context, userID uuid.UUID) ([]*entities.SecondFactor, error)
}

// WebhookEndpointRepository defines webhook endpoint operations
type WebhookEndpointRepository interface {
	BaseRepository[entities.WebhookEndpoint]
	GetByAccount(ctx context.Context, accountID uuid.UUID) ([]*entities.WebhookEndpoint, error)
	GetByEvent(ctx context.Context, event string) ([]*entities.WebhookEndpoint, error)
	UpdateDeliveryStats(ctx context.Context, endpointID uuid.UUID, success bool) error
}

// WebhookEventRepository defines webhook event operations
type WebhookEventRepository interface {
	BaseRepository[entities.WebhookEvent]
	GetPendingEvents(ctx context.Context, limit int) ([]*entities.WebhookEvent, error)
	GetQueuedEvents(ctx context.Context, limit int) ([]*entities.WebhookEvent, error)
	GetRetryableEvents(ctx context.Context, before time.Time) ([]*entities.WebhookEvent, error)
	MarkAsDelivered(ctx context.Context, eventID uuid.UUID) error
	MarkAsFailed(ctx context.Context, eventID uuid.UUID, errorMsg string) error
}
