package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Product struct {
	ID            string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string         `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string        `json:"environment_id" gorm:"type:uuid;index"`
	Name          string         `json:"name" gorm:"not null"`
	Code          string         `json:"code" gorm:"not null"` // unique per account
	Key           string         `json:"key" gorm:"unique;not null"` // Legacy: use Code
	Description   string         `json:"description"`
	URL           string         `json:"url"`
	
	// Platform and distribution
	Platforms            ProductPlatforms    `json:"platforms" gorm:"type:jsonb"`
	DistributionStrategy *string             `json:"distribution_strategy"`
	
	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account     `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Policies    []Policy    `json:"policies,omitempty"`
	Licenses    []License   `json:"licenses,omitempty"`
}

type ProductPlatforms struct {
	Supported []string               `json:"supported,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Scan implements the sql.Scanner interface for JSONB scanning
func (p *ProductPlatforms) Scan(value interface{}) error {
	if value == nil {
		*p = ProductPlatforms{
			Supported: []string{},
			Metadata:  make(map[string]interface{}),
		}
		return nil
	}
	
	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into ProductPlatforms", value)
	}
	
	if len(data) == 0 {
		*p = ProductPlatforms{
			Supported: []string{},
			Metadata:  make(map[string]interface{}),
		}
		return nil
	}
	
	return json.Unmarshal(data, p)
}

// Value implements the driver.Valuer interface for JSONB storage
func (p ProductPlatforms) Value() (driver.Value, error) {
	return json.Marshal(p)
}