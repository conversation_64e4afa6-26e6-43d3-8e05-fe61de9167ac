package entities

import (
	"time"

	"gorm.io/gorm"
)

// Permission represents a specific action that can be performed
type Permission struct {
	ID     string `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Action string `json:"action" gorm:"unique;not null"` // e.g., "license.read", "user.create"
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Roles  []Role  `json:"roles,omitempty" gorm:"many2many:role_permissions"`
	Groups []Group `json:"groups,omitempty" gorm:"many2many:group_permissions"`
	Tokens []Token `json:"tokens,omitempty" gorm:"many2many:token_permissions"`
}

// Common permission actions
const (
	// Account permissions
	PermissionAccountRead   = "account.read"
	PermissionAccountUpdate = "account.update"
	PermissionAccountDelete = "account.delete"
	
	// User permissions
	PermissionUserRead   = "user.read"
	PermissionUserCreate = "user.create"
	PermissionUserUpdate = "user.update"
	PermissionUserDelete = "user.delete"
	
	// Product permissions
	PermissionProductRead   = "product.read"
	PermissionProductCreate = "product.create"
	PermissionProductUpdate = "product.update"
	PermissionProductDelete = "product.delete"
	
	// Policy permissions
	PermissionPolicyRead   = "policy.read"
	PermissionPolicyCreate = "policy.create"
	PermissionPolicyUpdate = "policy.update"
	PermissionPolicyDelete = "policy.delete"
	
	// License permissions
	PermissionLicenseRead     = "license.read"
	PermissionLicenseCreate   = "license.create"
	PermissionLicenseUpdate   = "license.update"
	PermissionLicenseDelete   = "license.delete"
	PermissionLicenseValidate = "license.validate"
	PermissionLicenseActivate = "license.activate"
	
	// Machine permissions
	PermissionMachineRead   = "machine.read"
	PermissionMachineCreate = "machine.create"
	PermissionMachineUpdate = "machine.update"
	PermissionMachineDelete = "machine.delete"
	
	// Token permissions
	PermissionTokenRead    = "token.read"
	PermissionTokenCreate  = "token.create"
	PermissionTokenRevoke  = "token.revoke"
	PermissionTokenGenerate = "token.generate"
	
	// Wildcard permission
	PermissionAll = "*"
)