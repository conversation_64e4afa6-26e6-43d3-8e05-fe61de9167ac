package entities

import (
	"time"

	"github.com/google/uuid"
)

// LicenseFile represents a license certificate file (non-persistent entity)
// Maps to Ruby LicenseFile model
type LicenseFile struct {
	ID            string     `json:"id"`
	AccountID     string     `json:"account_id"`
	EnvironmentID *string    `json:"environment_id,omitempty"`
	LicenseID     string     `json:"license_id"`
	Certificate   string     `json:"certificate"`
	IssuedAt      time.Time  `json:"issued_at"`
	ExpiresAt     *time.Time `json:"expires_at,omitempty"`
	TTL           *int       `json:"ttl,omitempty"`           // TTL in seconds
	Includes      []string   `json:"includes"`
	Algorithm     string     `json:"algorithm"`
}

// Supported algorithms (maps to Ruby LicenseFile::ALGORITHMS)
var SupportedLicenseFileAlgorithms = []string{
	"aes-256-gcm+ed25519",
	"aes-256-gcm+rsa-pss-sha256",
	"aes-256-gcm+rsa-sha256",
	"base64+ed25519",
	"base64+rsa-pss-sha256",
	"base64+rsa-sha256",
}

// NewLicenseFile creates a new license file instance
func NewLicenseFile(
	accountID string,
	environmentID *string,
	licenseID string,
	certificate string,
	issuedAt time.Time,
	expiresAt *time.Time,
	ttl *int,
	includes []string,
	algorithm string,
) *LicenseFile {
	return &LicenseFile{
		ID:            uuid.New().String(),
		AccountID:     accountID,
		EnvironmentID: environmentID,
		LicenseID:     licenseID,
		Certificate:   certificate,
		IssuedAt:      issuedAt,
		ExpiresAt:     expiresAt,
		TTL:           ttl,
		Includes:      includes,
		Algorithm:     algorithm,
	}
}

// IsAlgorithmSupported checks if the algorithm is supported
func (lf *LicenseFile) IsAlgorithmSupported() bool {
	for _, alg := range SupportedLicenseFileAlgorithms {
		if lf.Algorithm == alg {
			return true
		}
	}
	return false
}