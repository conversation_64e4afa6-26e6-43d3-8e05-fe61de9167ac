package entities

import (
	"time"

	"gorm.io/gorm"
)

// MachineProcess represents a running process on a machine for process-level licensing
type MachineProcess struct {
	ID            string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string         `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string        `json:"environment_id" gorm:"type:uuid;index"`
	MachineID     string         `json:"machine_id" gorm:"type:uuid;not null;index"`
	PID           string         `json:"pid" gorm:"not null"` // unique per machine
	
	// Heartbeat tracking
	LastHeartbeatAt      time.Time  `json:"last_heartbeat_at" gorm:"not null;index"`
	LastDeathEventSentAt *time.Time `json:"last_death_event_sent_at"`
	HeartbeatJID         *string    `json:"heartbeat_jid"` // Job ID for heartbeat processing
	
	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Machine     Machine      `json:"machine,omitempty" gorm:"foreignKey:MachineID"`
}

