package entities

import (
	"time"

	"gorm.io/gorm"
)

// Role represents a collection of permissions that can be assigned to users or tokens
type Role struct {
	ID           string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID    string  `json:"account_id" gorm:"type:uuid;not null;index"`
	Name         string  `json:"name" gorm:"not null"`
	ResourceType *string `json:"resource_type"` // polymorphic association
	ResourceID   *string `json:"resource_id" gorm:"type:uuid"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions"`
}

// Standard role names
const (
	RoleAdmin        = "admin"
	RoleDeveloper    = "developer"
	RoleSalesAgent   = "sales_agent"
	RoleSupportAgent = "support_agent"
	RoleReadOnly     = "read_only"
	RoleUser         = "user"
	RoleLicense      = "license"
	RoleProduct      = "product"
	RoleEnvironment  = "environment"
)