package entities

import (
	"time"

	"gorm.io/gorm"
)

// SecondFactor represents a 2FA configuration for a user
type SecondFactor struct {
	ID             string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID      string  `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID  *string `json:"environment_id" gorm:"type:uuid;index"`
	UserID         string  `json:"user_id" gorm:"type:uuid;not null;unique;index"`
	Secret         string  `json:"-" gorm:"type:text;not null;unique"` // TOTP secret, encrypted
	Enabled        bool    `json:"enabled" gorm:"default:false"`
	LastVerifiedAt *time.Time `json:"last_verified_at"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	User        User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
}