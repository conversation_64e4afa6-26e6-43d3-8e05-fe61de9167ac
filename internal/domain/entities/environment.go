package entities

import (
	"time"

	"gorm.io/gorm"
)

// Environment provides multi-tenant isolation within accounts
type Environment struct {
	ID                string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID         string         `json:"account_id" gorm:"type:uuid;not null;index"`
	Name              string         `json:"name" gorm:"not null"`
	Code              string         `json:"code" gorm:"not null"` // unique per account
	IsolationStrategy IsolationStrategy `json:"isolation_strategy" gorm:"not null"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account Account `json:"account,omitempty" gorm:"foreignKey:AccountID"`
}

type IsolationStrategy string

const (
	// Global environment - shared across all tenants
	IsolationStrategyGlobal IsolationStrategy = "global"
	
	// Shared environment - shared within account but isolated from others
	IsolationStrategyShared IsolationStrategy = "shared"
	
	// Isolated environment - completely isolated within account
	IsolationStrategyIsolated IsolationStrategy = "isolated"
)