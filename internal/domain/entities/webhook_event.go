package entities

import (
	"time"

	"gorm.io/gorm"
)

type WebhookEventStatus string

const (
	WebhookEventStatusPending    WebhookEventStatus = "pending"
	WebhookEventStatusQueued     WebhookEventStatus = "queued"
	WebhookEventStatusDelivering WebhookEventStatus = "delivering"
	WebhookEventStatusDelivered  WebhookEventStatus = "delivered"
	WebhookEventStatusFailed     WebhookEventStatus = "failed"
	WebhookEventStatusCancelled  WebhookEventStatus = "cancelled"
)

type WebhookEvent struct {
	ID            string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string  `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string `json:"environment_id" gorm:"type:uuid;index"`

	// Event details
	Event             string                 `json:"event" gorm:"not null;index"`                // e.g., "license.created"
	Data              map[string]interface{} `json:"data" gorm:"type:jsonb"`                     // Event payload
	Payload           map[string]interface{} `json:"payload" gorm:"type:jsonb"`                  // Alternative name for data
	Headers           map[string]string      `json:"headers" gorm:"type:jsonb"`                  // Additional headers
	WebhookEndpointID string                 `json:"webhook_endpoint_id" gorm:"type:uuid;index"` // Target endpoint

	// Delivery tracking
	Status        WebhookEventStatus `json:"status" gorm:"default:'pending';index"`
	AttemptCount  int                `json:"attempt_count" gorm:"default:0"`
	Attempts      int                `json:"attempts" gorm:"default:0"` // Alternative name for attempt_count
	MaxAttempts   int                `json:"max_attempts" gorm:"default:3"`
	NextRetryAt   *time.Time         `json:"next_retry_at,omitempty"`
	LastAttemptAt *time.Time         `json:"last_attempt_at,omitempty"`

	// Response tracking
	LastResponseStatus int    `json:"last_response_status,omitempty"`
	LastResponseBody   string `json:"last_response_body,omitempty"`
	LastErrorMessage   string `json:"last_error_message,omitempty"`

	// Delivery details
	DeliveredAt *time.Time `json:"delivered_at,omitempty"`
	FailedAt    *time.Time `json:"failed_at,omitempty"`

	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
}

// IsRetryable checks if the event can be retried
func (we *WebhookEvent) IsRetryable() bool {
	return we.Status == WebhookEventStatusFailed &&
		we.AttemptCount < we.MaxAttempts &&
		(we.NextRetryAt == nil || time.Now().After(*we.NextRetryAt))
}

// ShouldRetry checks if the event should be retried based on response
func (we *WebhookEvent) ShouldRetry(statusCode int) bool {
	// Retry on 5xx errors and some 4xx errors
	return statusCode >= 500 ||
		statusCode == 408 || // Request Timeout
		statusCode == 429 // Too Many Requests
}

// CalculateNextRetry calculates the next retry time using exponential backoff
func (we *WebhookEvent) CalculateNextRetry() time.Time {
	// Exponential backoff: 2^attempt * base_delay
	baseDelay := 5 * time.Second
	delay := time.Duration(1<<uint(we.AttemptCount)) * baseDelay

	// Cap at 1 hour
	if delay > time.Hour {
		delay = time.Hour
	}

	return time.Now().Add(delay)
}
