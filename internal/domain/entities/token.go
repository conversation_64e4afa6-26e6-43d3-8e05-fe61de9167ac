package entities

import (
	"time"

	"gorm.io/gorm"
)

type Token struct {
	ID            string          `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string          `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string         `json:"environment_id" gorm:"type:uuid;index"`
	BearerID      string          `json:"bearer_id" gorm:"type:uuid;not null;index"`
	BearerType    TokenBearerType `json:"bearer_type" gorm:"not null"`
	Name          string          `json:"name"`

	// Token value (hashed in database)
	Digest string `json:"-" gorm:"unique;not null"` // SHA-256 hash of token
	Token  string `json:"token,omitempty" gorm:"-"` // Plain token (only in memory)

	// Token configuration
	Kind TokenKind `json:"kind" gorm:"default:bearer"`

	// Activation limits (for license tokens)
	MaxActivations   *int `json:"max_activations,omitempty"`
	MaxDeactivations *int `json:"max_deactivations,omitempty"`
	Activations      int  `json:"activations" gorm:"default:0"`
	Deactivations    int  `json:"deactivations" gorm:"default:0"`

	// Permissions (custom permissions for this token)
	CustomPermissions []string `json:"custom_permissions" gorm:"type:text[]"`

	// Expiration and usage
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	LastUsed  *time.Time `json:"last_used,omitempty"`
	RevokedAt *time.Time `json:"revoked_at,omitempty"`

	// Metadata for additional token information
	Metadata map[string]interface{} `json:"metadata,omitempty" gorm:"type:jsonb"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Sessions    []Session    `json:"sessions,omitempty"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:token_permissions"`
}

type TokenBearerType string

const (
	TokenBearerTypeUser        TokenBearerType = "user"
	TokenBearerTypeProduct     TokenBearerType = "product"
	TokenBearerTypeLicense     TokenBearerType = "license"
	TokenBearerTypeEnvironment TokenBearerType = "environment"
)

type TokenKind string

const (
	TokenKindBearer  TokenKind = "bearer"
	TokenKindSession TokenKind = "session"
)
