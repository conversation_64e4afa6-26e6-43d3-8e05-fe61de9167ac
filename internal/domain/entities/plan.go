package entities

import (
	"time"

	"gorm.io/gorm"
)

// Plan defines subscription tiers and limits
type Plan struct {
	ID     string `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name   string `json:"name" gorm:"not null"`
	PlanID string `json:"plan_id" gorm:"unique"` // external billing system ID
	
	// Pricing
	Price    int    `json:"price"`              // in cents
	Interval string `json:"interval"`           // monthly, yearly, etc.
	Private  bool   `json:"private" gorm:"default:false"`
	
	// Resource limits
	MaxUsers     int `json:"max_users"`
	MaxAdmins    int `json:"max_admins"`
	MaxPolicies  int `json:"max_policies"`
	MaxLicenses  int `json:"max_licenses"`
	MaxProducts  int `json:"max_products"`
	
	// API limits
	MaxReqs int `json:"max_reqs"` // requests per hour
	
	// Trial configuration
	TrialDuration int `json:"trial_duration"` // in days
	
	// Data retention
	RequestLogRetentionDuration int `json:"request_log_retention_duration"` // in days
	EventLogRetentionDuration   int `json:"event_log_retention_duration"`   // in days
	
	// Storage limits (bytes)
	MaxStorage  int64 `json:"max_storage"`
	MaxTransfer int64 `json:"max_transfer"`
	MaxUpload   int64 `json:"max_upload"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Accounts []Account `json:"accounts,omitempty"`
}