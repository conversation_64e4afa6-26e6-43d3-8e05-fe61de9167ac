package entities

import (
	"time"

	"gorm.io/gorm"
)

type Machine struct {
	ID            string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string  `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string `json:"environment_id" gorm:"type:uuid;index"`
	LicenseID     string  `json:"license_id" gorm:"type:uuid;not null;index"`
	PolicyID      string  `json:"policy_id" gorm:"type:uuid;not null;index"`
	GroupID       *string `json:"group_id" gorm:"type:uuid;index"`
	OwnerID       *string `json:"owner_id" gorm:"type:uuid;index"` // User who owns this machine

	// Machine identification (unique per license)
	Fingerprint string `json:"fingerprint" gorm:"not null"` // unique per license
	Name        string `json:"name"`
	Hostname    string `json:"hostname"`
	Platform    string `json:"platform"`

	// Network information
	IP string `json:"ip"`

	// Machine state
	Status        MachineStatus `json:"status" gorm:"default:active"`
	ActivatedAt   *time.Time    `json:"activated_at,omitempty"`
	DeactivatedAt *time.Time    `json:"deactivated_at,omitempty"`
	LastSeen      *time.Time    `json:"last_seen,omitempty"`

	// Hardware tracking
	Cores int `json:"cores" gorm:"default:0"`

	// Heartbeat tracking
	LastHeartbeatAt      *time.Time `json:"last_heartbeat_at"`
	NextHeartbeatAt      *time.Time `json:"next_heartbeat_at"`
	LastDeathEventSentAt *time.Time `json:"last_death_event_sent_at"`
	HeartbeatJID         *string    `json:"heartbeat_jid" gorm:"column:heartbeat_jid"` // Job ID for heartbeat processing

	// Check-out tracking (for floating licenses)
	LastCheckOutAt *time.Time `json:"last_check_out_at"`

	// Process limits override
	MaxProcessesOverride *int `json:"max_processes_override"`

	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account            `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment       `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	License     License            `json:"license,omitempty" gorm:"foreignKey:LicenseID"`
	Policy      Policy             `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`
	Group       *Group             `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Owner       *User              `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
	Components  []MachineComponent `json:"components,omitempty"`
	Processes   []MachineProcess   `json:"processes,omitempty"`
}

type MachineStatus string

const (
	MachineStatusActive   MachineStatus = "active"
	MachineStatusInactive MachineStatus = "inactive"
)
