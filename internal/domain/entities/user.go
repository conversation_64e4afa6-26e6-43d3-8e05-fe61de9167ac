package entities

import (
	"time"

	"gorm.io/gorm"
)

type User struct {
	ID            string     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string     `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string    `json:"environment_id" gorm:"type:uuid;index"`
	Email         string     `json:"email" gorm:"not null"`
	FirstName     string     `json:"first_name"`
	LastName      string     `json:"last_name"`
	Password      string     `json:"-" gorm:"not null"` // Argon2id hashed
	Role          UserRole   `json:"role" gorm:"default:user"`
	Status        UserStatus `json:"status" gorm:"default:active"`

	// Password reset
	PasswordResetToken  *string    `json:"-"`
	PasswordResetSentAt *time.Time `json:"password_reset_sent_at"`

	// User management
	GroupID   *string    `json:"group_id" gorm:"type:uuid;index"`
	BannedAt  *time.Time `json:"banned_at"`
	LastLogin *time.Time `json:"last_login,omitempty"`

	// SSO integration
	SSOProfileID    *string `json:"sso_profile_id"`
	SSOIdpID        *string `json:"sso_idp_id"`
	SSOConnectionID *string `json:"sso_connection_id"`

	// Notification preferences
	StdoutUnsubscribedAt *time.Time `json:"stdout_unsubscribed_at"`
	StdoutLastSentAt     *time.Time `json:"stdout_last_sent_at"`

	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account      Account       `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment  *Environment  `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Group        *Group        `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Tokens       []Token       `json:"tokens,omitempty" gorm:"polymorphic:Bearer;polymorphicValue:user"`
	SecondFactor *SecondFactor `json:"second_factor,omitempty"`
}

type UserRole string

const (
	UserRoleAdmin        UserRole = "admin"
	UserRoleDeveloper    UserRole = "developer"
	UserRoleSalesAgent   UserRole = "sales_agent"
	UserRoleSupportAgent UserRole = "support_agent"
	UserRoleReadOnly     UserRole = "read_only"
	UserRoleUser         UserRole = "user"
)

type UserStatus string

const (
	UserStatusActive    UserStatus = "active"
	UserStatusInactive  UserStatus = "inactive"
	UserStatusSuspended UserStatus = "suspended"
)
