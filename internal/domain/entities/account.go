package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Account struct {
	ID           string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name         string         `json:"name" gorm:"not null"`
	Slug         string         `json:"slug" gorm:"unique;not null"`
	Email        string         `json:"email" gorm:"unique;not null"`
	Status       AccountStatus  `json:"status" gorm:"default:active"`
	PlanID       *string        `json:"plan_id" gorm:"type:uuid"`
	Protected    bool           `json:"protected" gorm:"default:false"`
	
	// Cryptographic keys
	PublicKey         *string `json:"-" gorm:"type:text"` // RSA public key
	PrivateKey        *string `json:"-" gorm:"type:text"` // RSA private key (encrypted)
	SecretKey         *string `json:"-" gorm:"type:text"` // 128-char hex secret
	Ed25519PrivateKey *string `json:"-" gorm:"type:text"` // Ed25519 private key (encrypted)
	Ed25519PublicKey  *string `json:"-" gorm:"type:text"` // Ed25519 public key
	
	// Domain and hosting
	Domain    *string `json:"domain" gorm:"unique"`
	Subdomain *string `json:"subdomain" gorm:"unique"`
	CNAME     *string `json:"cname" gorm:"unique"`
	Backend   *string `json:"backend"`
	
	// API configuration  
	APIVersion *string `json:"api_version"`
	
	// SSO configuration
	SSOOrganizationID      *string  `json:"sso_organization_id" gorm:"unique"`
	SSOOrganizationDomains []string `json:"sso_organization_domains" gorm:"type:text[]"`
	SSOSessionDuration     *int     `json:"sso_session_duration"`
	SSOJITProvisioning     bool     `json:"sso_jit_provisioning" gorm:"default:false"`
	SSOExternalAuthn       bool     `json:"sso_external_authn" gorm:"default:false"`
	
	// Slack integration
	SlackInvitedAt  *time.Time `json:"slack_invited_at"`
	SlackAcceptedAt *time.Time `json:"slack_accepted_at"`
	SlackTeamID     *string    `json:"slack_team_id" gorm:"unique"`
	SlackChannelID  *string    `json:"slack_channel_id" gorm:"unique"`
	
	// Notification tracking
	LastLowActivityLifelineSentAt    *time.Time `json:"last_low_activity_lifeline_sent_at"`
	LastTrialWillEndSentAt           *time.Time `json:"last_trial_will_end_sent_at"`
	LastLicenseLimitExceededSentAt   *time.Time `json:"last_license_limit_exceeded_sent_at"`
	LastRequestLimitExceededSentAt   *time.Time `json:"last_request_limit_exceeded_sent_at"`
	LastPromptForReviewSentAt        *time.Time `json:"last_prompt_for_review_sent_at"`
	
	// Legacy fields
	SubscriptionTier string          `json:"subscription_tier" gorm:"default:free"` // Deprecated: use PlanID
	Settings         AccountSettings `json:"settings" gorm:"type:jsonb"`
	Metadata         Metadata        `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
	
	// Relationships
	Users        []User        `json:"users,omitempty"`
	Products     []Product     `json:"products,omitempty"`
	Environments []Environment `json:"environments,omitempty"`
	Groups       []Group       `json:"groups,omitempty"`
}

type AccountStatus string

const (
	AccountStatusActive    AccountStatus = "active"
	AccountStatusSuspended AccountStatus = "suspended" 
	AccountStatusCanceled  AccountStatus = "canceled"
)

type AccountSettings struct {
	Timezone        string            `json:"timezone"`
	WebhookURL      string            `json:"webhook_url,omitempty"`
	WebhookSecret   string            `json:"webhook_secret,omitempty"`
	NotificationSettings map[string]bool `json:"notification_settings"`
}

// Scan implements the sql.Scanner interface for JSONB scanning
func (s *AccountSettings) Scan(value interface{}) error {
	if value == nil {
		*s = AccountSettings{
			NotificationSettings: make(map[string]bool),
		}
		return nil
	}
	
	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into AccountSettings", value)
	}
	
	if len(data) == 0 {
		*s = AccountSettings{
			NotificationSettings: make(map[string]bool),
		}
		return nil
	}
	
	return json.Unmarshal(data, s)
}

// Value implements the driver.Valuer interface for JSONB storage
func (s AccountSettings) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Metadata is a common type for entity metadata across all entities
type Metadata map[string]interface{}

// Scan implements the sql.Scanner interface for JSONB scanning
func (m *Metadata) Scan(value interface{}) error {
	if value == nil {
		*m = make(map[string]interface{})
		return nil
	}
	
	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into Metadata", value)
	}
	
	if len(data) == 0 {
		*m = make(map[string]interface{})
		return nil
	}
	
	return json.Unmarshal(data, m)
}

// Value implements the driver.Valuer interface for JSONB storage
func (m Metadata) Value() (driver.Value, error) {
	if m == nil {
		return "{}", nil
	}
	return json.Marshal(m)
}