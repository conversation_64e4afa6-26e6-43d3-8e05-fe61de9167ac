package entities

import (
	"time"

	"gorm.io/gorm"
)

// Group provides user organization and resource limits
type Group struct {
	ID            string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string         `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string        `json:"environment_id" gorm:"type:uuid;index"`
	Name          string         `json:"name" gorm:"not null"`
	
	// Resource limits
	MaxUsers    *int `json:"max_users,omitempty"`
	MaxLicenses *int `json:"max_licenses,omitempty"`
	MaxMachines *int `json:"max_machines,omitempty"`
	
	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Users       []User       `json:"users,omitempty"`
	Licenses    []License    `json:"licenses,omitempty"`
	Machines    []Machine    `json:"machines,omitempty"`
	Owners      []User       `json:"owners,omitempty" gorm:"many2many:group_owners"`
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:group_permissions"`
}

