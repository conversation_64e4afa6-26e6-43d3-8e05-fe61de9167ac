package entities

import (
	"time"

	"gorm.io/gorm"
)

// MachineComponent represents a hardware component of a machine for detailed fingerprinting
type MachineComponent struct {
	ID            string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string         `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string        `json:"environment_id" gorm:"type:uuid;index"`
	MachineID     string         `json:"machine_id" gorm:"type:uuid;not null;index"`
	Fingerprint   string         `json:"fingerprint" gorm:"not null"` // unique per machine
	Name          string         `json:"name" gorm:"not null"`
	
	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Machine     Machine      `json:"machine,omitempty" gorm:"foreignKey:MachineID"`
}

