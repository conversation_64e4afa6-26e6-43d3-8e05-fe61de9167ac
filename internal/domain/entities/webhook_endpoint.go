package entities

import (
	"time"

	"gorm.io/gorm"
)

type WebhookEndpoint struct {
	ID            string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string  `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string `json:"environment_id" gorm:"type:uuid;index"`

	// Endpoint configuration
	Name          string            `json:"name" gorm:"not null"`
	URL           string            `json:"url" gorm:"not null"`
	Events        []string          `json:"events" gorm:"type:text[]"`      // webhook.*, license.*, etc.
	Secret        string            `json:"-" gorm:"not null"`              // HMAC secret for verification
	SigningSecret string            `json:"-" gorm:"column:signing_secret"` // Alternative name for secret
	Description   string            `json:"description"`
	Headers       map[string]string `json:"headers" gorm:"type:jsonb"` // Custom headers

	// Status and configuration
	Enabled    bool `json:"enabled" gorm:"default:true"`
	MaxRetries int  `json:"max_retries" gorm:"default:3"`
	RetryDelay int  `json:"retry_delay" gorm:"default:5"` // seconds

	// Statistics
	LastDeliveryAt       *time.Time `json:"last_delivery_at,omitempty"`
	LastSuccessAt        *time.Time `json:"last_success_at,omitempty"`
	LastFailureAt        *time.Time `json:"last_failure_at,omitempty"`
	SuccessfulDeliveries int        `json:"successful_deliveries" gorm:"default:0"`
	FailedDeliveries     int        `json:"failed_deliveries" gorm:"default:0"`

	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
}

// Webhook event constants moved to webhook_event.go
