package entities

import (
	"time"

	"gorm.io/gorm"
)

// Session represents an authenticated session
type Session struct {
	ID            string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string  `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string `json:"environment_id" gorm:"type:uuid;index"`
	TokenID       *string `json:"token_id" gorm:"type:uuid;index"`
	
	// Bearer information (polymorphic)
	BearerType string `json:"bearer_type" gorm:"not null"` // user, license, etc.
	BearerID   string `json:"bearer_id" gorm:"type:uuid;not null;index"`
	
	// Session details
	IP        string     `json:"ip" gorm:"not null"`
	UserAgent *string    `json:"user_agent"`
	LastUsedAt *time.Time `json:"last_used_at"`
	ExpiresAt time.Time  `json:"expires_at" gorm:"not null;index"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Token       *Token       `json:"token,omitempty" gorm:"foreignKey:TokenID"`
}

// IsExpired checks if the session has expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// IsActive checks if the session is active (not expired and not deleted)
func (s *Session) IsActive() bool {
	return !s.IsExpired() && s.DeletedAt.Time.IsZero()
}