package entities

import (
	"time"

	"gorm.io/gorm"
)

type License struct {
	ID            string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string         `json:"account_id" gorm:"type:uuid;not null;index"`
	ProductID     string         `json:"product_id" gorm:"type:uuid;not null;index"`
	PolicyID      string         `json:"policy_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string        `json:"environment_id" gorm:"type:uuid;index"`
	UserID        *string        `json:"user_id" gorm:"type:uuid;index"`
	GroupID       *string        `json:"group_id" gorm:"type:uuid;index"`
	
	// License identification
	Key  string `json:"key" gorm:"not null"` // unique per account
	Name string `json:"name"`
	
	// License state
	Status    LicenseStatus `json:"status" gorm:"default:active"`
	Suspended bool          `json:"suspended" gorm:"default:false"`
	Protected *bool         `json:"protected"`
	
	// Usage tracking
	Uses     int        `json:"uses" gorm:"default:0"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	LastUsed  *time.Time `json:"last_used,omitempty"`
	
	// Policy overrides (allow per-license customization)
	MaxUsesOverride     *int `json:"max_uses_override,omitempty"`
	MaxMachinesOverride *int `json:"max_machines_override,omitempty"`
	MaxCoresOverride    *int `json:"max_cores_override,omitempty"`
	MaxUsersOverride    *int `json:"max_users_override,omitempty"`
	MaxProcessesOverride *int `json:"max_processes_override,omitempty"`
	
	// Cached counts for performance
	MachinesCount     int `json:"machines_count" gorm:"default:0"`
	MachinesCoreCount int `json:"machines_core_count" gorm:"default:0"`
	LicenseUsersCount int `json:"license_users_count" gorm:"default:0"`
	
	// Heartbeat and validation tracking
	LastCheckInAt               *time.Time `json:"last_check_in_at"`
	LastValidatedAt             *time.Time `json:"last_validated_at"`
	LastValidatedChecksum       *string    `json:"last_validated_checksum"`
	LastValidatedVersion        *string    `json:"last_validated_version"`
	LastCheckOutAt              *time.Time `json:"last_check_out_at"`
	
	// Event tracking (for notification management)
	LastExpirationEventSentAt   *time.Time `json:"last_expiration_event_sent_at"`
	LastCheckInEventSentAt      *time.Time `json:"last_check_in_event_sent_at"`
	LastExpiringSoonEventSentAt *time.Time `json:"last_expiring_soon_event_sent_at"`
	LastCheckInSoonEventSentAt  *time.Time `json:"last_check_in_soon_event_sent_at"`
	
	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account      Account       `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Product      Product       `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Policy       Policy        `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`
	Environment  *Environment  `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	User         *User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Group        *Group        `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Machines     []Machine     `json:"machines,omitempty"`
	Users        []User        `json:"users,omitempty" gorm:"many2many:license_users"`
	Entitlements []Entitlement `json:"entitlements,omitempty" gorm:"many2many:license_entitlements"`
}

type LicenseStatus string

const (
	LicenseStatusActive    LicenseStatus = "active"
	LicenseStatusExpired   LicenseStatus = "expired"
	LicenseStatusSuspended LicenseStatus = "suspended"
	LicenseStatusBanned    LicenseStatus = "banned"
)

