package entities

import (
	"time"

	"gorm.io/gorm"
)

// Entitlement represents a feature that can be granted to licenses
type Entitlement struct {
	ID            string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AccountID     string         `json:"account_id" gorm:"type:uuid;not null;index"`
	EnvironmentID *string        `json:"environment_id" gorm:"type:uuid;index"`
	Name          string         `json:"name" gorm:"not null"`
	Code          string         `json:"code" gorm:"not null"` // unique per account
	
	// Metadata
	Metadata Metadata `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Account     Account      `json:"account,omitempty" gorm:"foreignKey:AccountID"`
	Environment *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
	Policies    []Policy     `json:"policies,omitempty" gorm:"many2many:policy_entitlements"`
	Licenses    []License    `json:"licenses,omitempty" gorm:"many2many:license_entitlements"`
}

