package entities

import (
	"time"

	"github.com/google/uuid"
)

// MachineFile represents a machine certificate file (non-persistent entity)
// Maps to Ruby MachineFile model
type MachineFile struct {
	ID            string     `json:"id"`
	AccountID     string     `json:"account_id"`
	EnvironmentID *string    `json:"environment_id,omitempty"`
	LicenseID     string     `json:"license_id"`
	MachineID     string     `json:"machine_id"`
	Certificate   string     `json:"certificate"`
	IssuedAt      time.Time  `json:"issued_at"`
	ExpiresAt     *time.Time `json:"expires_at,omitempty"`
	TTL           *int       `json:"ttl,omitempty"`           // TTL in seconds
	Includes      []string   `json:"includes"`
	Algorithm     string     `json:"algorithm"`
}

// Supported algorithms (maps to Ruby MachineFile::ALGORITHMS)
var SupportedMachineFileAlgorithms = []string{
	"aes-256-gcm+ed25519",
	"aes-256-gcm+rsa-pss-sha256",
	"aes-256-gcm+rsa-sha256",
	"base64+ed25519",
	"base64+rsa-pss-sha256",
	"base64+rsa-sha256",
}

// NewMachineFile creates a new machine file instance
func NewMachineFile(
	accountID string,
	environmentID *string,
	licenseID string,
	machineID string,
	certificate string,
	issuedAt time.Time,
	expiresAt *time.Time,
	ttl *int,
	includes []string,
	algorithm string,
) *MachineFile {
	return &MachineFile{
		ID:            uuid.New().String(),
		AccountID:     accountID,
		EnvironmentID: environmentID,
		LicenseID:     licenseID,
		MachineID:     machineID,
		Certificate:   certificate,
		IssuedAt:      issuedAt,
		ExpiresAt:     expiresAt,
		TTL:           ttl,
		Includes:      includes,
		Algorithm:     algorithm,
	}
}

// IsAlgorithmSupported checks if the algorithm is supported
func (mf *MachineFile) IsAlgorithmSupported() bool {
	for _, alg := range SupportedMachineFileAlgorithms {
		if mf.Algorithm == alg {
			return true
		}
	}
	return false
}