package events

import (
	"context"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
)

// EventType represents types of events that can be broadcast
type EventType string

const (
	// License events
	EventLicenseCreated             EventType = "license.created"
	EventLicenseUpdated             EventType = "license.updated"
	EventLicenseDeleted             EventType = "license.deleted"
	EventLicenseValidationSucceeded EventType = "license.validation.succeeded"
	EventLicenseValidationFailed    EventType = "license.validation.failed"
	EventLicenseCheckedOut          EventType = "license.checked-out"
	EventLicenseCheckedIn           EventType = "license.check-in"
	EventLicenseRevoked             EventType = "license.revoked"
	EventLicenseSuspended           EventType = "license.suspended"
	EventLicenseReinstated          EventType = "license.reinstated"
	EventLicenseRenewed             EventType = "license.renewed"
	EventLicenseUsageIncremented    EventType = "license.usage.incremented"
	EventLicenseUsageDecremented    EventType = "license.usage.decremented"
	EventLicenseUsageReset          EventType = "license.usage.reset"

	// Machine events
	EventMachineCreated          EventType = "machine.created"
	EventMachineUpdated          EventType = "machine.updated"
	EventMachineDeleted          EventType = "machine.deleted"
	EventMachineHeartbeatPing    EventType = "machine.heartbeat.ping"
	EventMachineHeartbeatReset   EventType = "machine.heartbeat.reset"
	EventMachineHeartbeatResurrected EventType = "machine.heartbeat.resurrected"
	EventMachineCheckedOut       EventType = "machine.checked-out"

	// User events
	EventUserCreated       EventType = "user.created"
	EventUserUpdated       EventType = "user.updated"
	EventUserDeleted       EventType = "user.deleted"
	EventUserPasswordReset EventType = "user.password-reset"
	EventUserBanned        EventType = "user.banned"
	EventUserUnbanned      EventType = "user.unbanned"

	// Product events
	EventProductCreated EventType = "product.created"
	EventProductUpdated EventType = "product.updated"
	EventProductDeleted EventType = "product.deleted"

	// Policy events
	EventPolicyCreated EventType = "policy.created"
	EventPolicyUpdated EventType = "policy.updated"
	EventPolicyDeleted EventType = "policy.deleted"

	// Token events
	EventTokenGenerated EventType = "token.generated"
	EventTokenRegenerated EventType = "token.regenerated"
	EventTokenRevoked   EventType = "token.revoked"

	// Entitlement events
	EventEntitlementCreated EventType = "entitlement.created"
	EventEntitlementUpdated EventType = "entitlement.updated"
	EventEntitlementDeleted EventType = "entitlement.deleted"
	EventLicenseEntitlementsAttached EventType = "license.entitlements.attached"
	EventLicenseEntitlementsDetached EventType = "license.entitlements.detached"

	// Group events
	EventGroupCreated EventType = "group.created"
	EventGroupUpdated EventType = "group.updated"
	EventGroupDeleted EventType = "group.deleted"

	// Account events
	EventAccountCreated EventType = "account.created"
	EventAccountUpdated EventType = "account.updated"
	EventAccountDeleted EventType = "account.deleted"

	// Webhook events
	EventWebhookEndpointCreated EventType = "webhook-endpoint.created"
	EventWebhookEndpointUpdated EventType = "webhook-endpoint.updated"
	EventWebhookEndpointDeleted EventType = "webhook-endpoint.deleted"
	EventWebhookEventCreated    EventType = "webhook-event.created"
	EventWebhookEventRetried    EventType = "webhook-event.retried"

	// Component events
	EventComponentCreated EventType = "component.created"
	EventComponentUpdated EventType = "component.updated"
	EventComponentDeleted EventType = "component.deleted"

	// Process events
	EventProcessCreated EventType = "process.created"
	EventProcessUpdated EventType = "process.updated"
	EventProcessDeleted EventType = "process.deleted"
	EventProcessHeartbeatPing EventType = "process.heartbeat.ping"
)

// EventMeta represents metadata for an event
type EventMeta map[string]interface{}

// EventResource represents a resource that triggered an event
type EventResource interface {
	GetID() string
	GetType() string
}

// BroadcastService handles event broadcasting to webhooks
type BroadcastService struct {
	// TODO: Add repository factory when available
	// repositories       *repositories.RepositoryFactory
	webhookService     WebhookService
	notificationService NotificationService
	metricsService     MetricsService
}

// WebhookService interface for webhook delivery (placeholder for now)
type WebhookService interface {
	DeliverWebhook(ctx context.Context, endpoint *entities.WebhookEndpoint, event EventType, payload interface{}) error
}

// NotificationService interface for notifications (placeholder for now)
type NotificationService interface {
	SendNotification(ctx context.Context, event EventType, accountID uuid.UUID, resource EventResource) error
}

// MetricsService interface for recording metrics (placeholder for now)
type MetricsService interface {
	RecordEvent(ctx context.Context, event EventType, accountID uuid.UUID, resource EventResource) error
}

// NewBroadcastService creates a new event broadcast service
func NewBroadcastService() *BroadcastService {
	return &BroadcastService{
		// TODO: Add other services when they are implemented
		webhookService:     nil,
		notificationService: nil,
		metricsService:     nil,
	}
}

// BroadcastEvent broadcasts an event to all relevant endpoints
func (bs *BroadcastService) BroadcastEvent(
	ctx context.Context,
	event EventType,
	account *entities.Account,
	resource EventResource,
	meta EventMeta,
) error {
	// Record metrics for the event
	if bs.metricsService != nil {
		if err := bs.metricsService.RecordEvent(ctx, event, uuid.MustParse(account.ID), resource); err != nil {
			// Log error but don't fail the broadcast
			fmt.Printf("Failed to record metrics for event %s: %v\n", event, err)
		}
	}

	// TODO: Skip webhook delivery for now until repository integration is complete
	fmt.Printf("Broadcasting event %s for %s %s (webhook delivery not yet implemented)\n", 
		event, resource.GetType(), resource.GetID())
	
	// For now, we'll create an empty endpoints list to simulate the structure
	var endpoints []*entities.WebhookEndpoint

	// Filter endpoints that are subscribed to this event
	var subscribedEndpoints []*entities.WebhookEndpoint
	for _, endpoint := range endpoints {
		if bs.isSubscribed(endpoint, event) {
			subscribedEndpoints = append(subscribedEndpoints, endpoint)
		}
	}

	// Skip if no endpoints are subscribed
	if len(subscribedEndpoints) == 0 {
		fmt.Printf("Broadcasting event %s for %s %s (no subscribed endpoints)\n", 
			event, resource.GetType(), resource.GetID())
		return nil
	}

	// Prepare webhook payload
	payload := map[string]interface{}{
		"event":     string(event),
		"timestamp": time.Now().UTC(),
		"data": map[string]interface{}{
			"type": resource.GetType(),
			"id":   resource.GetID(),
			"attributes": resource, // This should be the full resource
		},
		"meta": meta,
	}

	// Deliver webhooks to all subscribed endpoints
	for _, endpoint := range subscribedEndpoints {
		if bs.webhookService != nil {
			// This should be done asynchronously in production
			if err := bs.webhookService.DeliverWebhook(ctx, endpoint, event, payload); err != nil {
				// Log error but continue with other endpoints
				fmt.Printf("Failed to deliver webhook to endpoint %s: %v\n", endpoint.ID, err)
			}
		} else {
			// Placeholder logging when webhook service is not implemented
			fmt.Printf("Would deliver webhook %s to endpoint %s\n", event, endpoint.ID)
		}
	}

	// Send notifications if configured
	if bs.notificationService != nil {
		if err := bs.notificationService.SendNotification(ctx, event, uuid.MustParse(account.ID), resource); err != nil {
			// Log error but don't fail the broadcast
			fmt.Printf("Failed to send notification for event %s: %v\n", event, err)
		}
	}

	return nil
}

// isSubscribed checks if a webhook endpoint is subscribed to an event
func (bs *BroadcastService) isSubscribed(endpoint *entities.WebhookEndpoint, event EventType) bool {
	// If no events specified, assume subscribed to all events
	if endpoint.Events == nil || len(endpoint.Events) == 0 {
		return true
	}

	eventStr := string(event)
	for _, subscription := range endpoint.Events {
		// Exact match
		if subscription == eventStr {
			return true
		}

		// Wildcard match (e.g., "license.*" matches all license events)
		if len(subscription) > 2 && subscription[len(subscription)-2:] == ".*" {
			prefix := subscription[:len(subscription)-2]
			if len(eventStr) > len(prefix) && eventStr[:len(prefix)] == prefix {
				return true
			}
		}
	}

	return false
}

// Helper function to create a simple event resource
type SimpleEventResource struct {
	ID   string
	Type string
}

func (r SimpleEventResource) GetID() string   { return r.ID }
func (r SimpleEventResource) GetType() string { return r.Type }

// MakeEventResource creates an event resource from an entity
func MakeEventResource(entity interface{}) EventResource {
	switch e := entity.(type) {
	case *entities.License:
		return SimpleEventResource{ID: e.ID, Type: "license"}
	case *entities.Machine:
		return SimpleEventResource{ID: e.ID, Type: "machine"}
	case *entities.User:
		return SimpleEventResource{ID: e.ID, Type: "user"}
	case *entities.Product:
		return SimpleEventResource{ID: e.ID, Type: "product"}
	case *entities.Policy:
		return SimpleEventResource{ID: e.ID, Type: "policy"}
	case *entities.Token:
		return SimpleEventResource{ID: e.ID, Type: "token"}
	case *entities.Account:
		return SimpleEventResource{ID: e.ID, Type: "account"}
	case *entities.Group:
		return SimpleEventResource{ID: e.ID, Type: "group"}
	case *entities.Entitlement:
		return SimpleEventResource{ID: e.ID, Type: "entitlement"}
	case *entities.WebhookEndpoint:
		return SimpleEventResource{ID: e.ID, Type: "webhook_endpoint"}
	case *entities.MachineComponent:
		return SimpleEventResource{ID: e.ID, Type: "component"}
	case *entities.MachineProcess:
		return SimpleEventResource{ID: e.ID, Type: "process"}
	default:
		// Fallback for unknown types
		return SimpleEventResource{ID: "unknown", Type: "unknown"}
	}
}