package token

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"regexp"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// Modern token pattern for validation
var TokenPattern = regexp.MustCompile(`^[a-z]+\-[a-fA-F0-9]{64}$`)

// LookupService handles token lookup functionality (maps to Ruby TokenLookupService)
type LookupService struct {
	tokenRepo   repositories.TokenRepository
	accountRepo repositories.AccountRepository
}

// NewLookupService creates a new token lookup service
func NewLookupService(
	tokenRepo repositories.TokenRepository,
	accountRepo repositories.AccountRepository,
) *LookupService {
	return &LookupService{
		tokenRepo:   tokenRepo,
		accountRepo: accountRepo,
	}
}

// Modern token pattern for Go implementation
var TokenModernPattern = regexp.MustCompile(`^[a-zA-Z0-9]+(-[a-f0-9]{64})$`)

// LookupOptions represents lookup configuration
type LookupOptions struct {
	Environment *entities.Environment `json:"environment,omitempty"`
}

// LookupToken finds a token by its value (maps to Ruby TokenLookupService.call)
func (ls *LookupService) LookupToken(ctx context.Context, accountID, tokenValue string, options *LookupOptions) (*entities.Token, error) {
	// Set default options
	if options == nil {
		options = &LookupOptions{}
	}

	// Validate inputs
	if accountID == "" || tokenValue == "" {
		return nil, fmt.Errorf("account and token must be present")
	}

	// Parse account ID
	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Get account
	account, err := ls.accountRepo.GetByID(ctx, accountUUID)
	if err != nil {
		return nil, fmt.Errorf("account not found: %w", err)
	}

	// Modern token lookup (Go implementation is modern and independent)
	return ls.lookupToken(ctx, account, tokenValue, options.Environment)
}

// lookupToken handles modern token lookup with HMAC-SHA256
func (ls *LookupService) lookupToken(ctx context.Context, account *entities.Account, tokenValue string, environment *entities.Environment) (*entities.Token, error) {
	// Modern tokens use HMAC-SHA256 with account secret key
	if account.SecretKey == nil || *account.SecretKey == "" {
		return nil, fmt.Errorf("account secret key not available for token verification")
	}

	// Generate digest using HMAC-SHA256
	h := hmac.New(sha256.New, []byte(*account.SecretKey))
	h.Write([]byte(tokenValue))
	digest := hex.EncodeToString(h.Sum(nil))

	// Get tokens scoped to account and environment
	tokens, err := ls.getTokensForEnvironment(ctx, account.ID, environment)
	if err != nil {
		return nil, err
	}

	// Find token by digest
	for _, token := range tokens {
		if token.Digest == digest {
			return token, nil
		}
	}

	return nil, fmt.Errorf("token not found")
}

// getTokensForEnvironment gets tokens scoped to account and environment
func (ls *LookupService) getTokensForEnvironment(ctx context.Context, accountID string, environment *entities.Environment) ([]*entities.Token, error) {
	// Implements strict mode when environment is nil (global environment)
	// to prevent authentication with tokens from other environments

	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Get all tokens for account
	tokens, err := ls.tokenRepo.GetByAccount(ctx, accountUUID)
	if err != nil {
		return nil, err
	}

	// Filter by environment if specified
	if environment != nil {
		var filteredTokens []*entities.Token
		for _, token := range tokens {
			if token.EnvironmentID != nil && *token.EnvironmentID == environment.ID {
				filteredTokens = append(filteredTokens, token)
			}
		}
		return filteredTokens, nil
	}

	// For global environment (nil), only return tokens with no environment
	var globalTokens []*entities.Token
	for _, token := range tokens {
		if token.EnvironmentID == nil {
			globalTokens = append(globalTokens, token)
		}
	}

	return globalTokens, nil
}

// ValidateTokenFormat validates the format of a token
func (ls *LookupService) ValidateTokenFormat(tokenValue string) error {
	if tokenValue == "" {
		return fmt.Errorf("token cannot be empty")
	}

	if !TokenPattern.MatchString(tokenValue) {
		return fmt.Errorf("invalid token format - expected: prefix-hexstring")
	}

	return nil
}

// GenerateTokenDigest generates a token digest for storage using HMAC-SHA256
func (ls *LookupService) GenerateTokenDigest(tokenValue, secretKey string) string {
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(tokenValue))
	return hex.EncodeToString(h.Sum(nil))
}
