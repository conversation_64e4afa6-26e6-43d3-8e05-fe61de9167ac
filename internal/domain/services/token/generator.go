package token

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// GeneratorService handles token generation
type GeneratorService struct {
	tokenRepo     repositories.TokenRepository
	accountRepo   repositories.AccountRepository
	lookupService *LookupService
}

// NewGeneratorService creates a new token generator service
func NewGeneratorService(
	tokenRepo repositories.TokenRepository,
	accountRepo repositories.AccountRepository,
	lookupService *LookupService,
) *GeneratorService {
	return &GeneratorService{
		tokenRepo:     tokenRepo,
		accountRepo:   accountRepo,
		lookupService: lookupService,
	}
}

// TokenType represents different types of tokens
type TokenType string

const (
	TokenTypeUser    TokenType = "user"    // usr-64hexchars
	TokenTypeProduct TokenType = "product" // prod-64hexchars
	TokenTypeService TokenType = "service" // svc-64hexchars
	TokenTypeAdmin   TokenType = "admin"   // admin-64hexchars
)

// TokenPermissions represents token permissions
type TokenPermissions struct {
	Licenses  []string `json:"licenses"`  // license.read, license.write, etc.
	Machines  []string `json:"machines"`  // machine.read, machine.write, etc.
	Users     []string `json:"users"`     // user.read, user.write, etc.
	Products  []string `json:"products"`  // product.read, product.write, etc.
	Policies  []string `json:"policies"`  // policy.read, policy.write, etc.
	Webhooks  []string `json:"webhooks"`  // webhook.read, webhook.write, etc.
	Analytics []string `json:"analytics"` // analytics.read, etc.
	Billing   []string `json:"billing"`   // billing.read, billing.write, etc.
}

// CreateTokenRequest represents a token creation request
type CreateTokenRequest struct {
	Name          string                 `json:"name" validate:"required"`
	Description   string                 `json:"description"`
	TokenType     TokenType              `json:"type" validate:"required"`
	BearerType    string                 `json:"bearer_type"` // User, Product, Account
	BearerID      string                 `json:"bearer_id" validate:"required"`
	EnvironmentID *string                `json:"environment_id,omitempty"`
	Permissions   *TokenPermissions      `json:"permissions,omitempty"`
	ExpiresAt     *time.Time             `json:"expires_at,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// TokenResult represents the result of token creation
type TokenResult struct {
	Token    *entities.Token `json:"token"`
	TokenKey string          `json:"token_key"` // The actual token string to return to user
}

// CreateToken creates a new token
func (gs *GeneratorService) CreateToken(ctx context.Context, accountID string, req CreateTokenRequest) (*TokenResult, error) {
	// Validate account
	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	account, err := gs.accountRepo.GetByID(ctx, accountUUID)
	if err != nil {
		return nil, fmt.Errorf("account not found: %w", err)
	}

	// Generate token key
	tokenKey := gs.generateTokenKey(req.TokenType)

	// Generate digest for storage
	var digest string
	if account.SecretKey != nil {
		digest = gs.lookupService.GenerateTokenDigest(tokenKey, *account.SecretKey)
	} else {
		return nil, fmt.Errorf("account secret key not available")
	}

	// Convert bearer type string to enum
	var bearerType entities.TokenBearerType
	switch req.BearerType {
	case "user":
		bearerType = entities.TokenBearerTypeUser
	case "product":
		bearerType = entities.TokenBearerTypeProduct
	case "license":
		bearerType = entities.TokenBearerTypeLicense
	case "environment":
		bearerType = entities.TokenBearerTypeEnvironment
	default:
		return nil, fmt.Errorf("invalid bearer type: %s", req.BearerType)
	}

	// Create token entity
	token := &entities.Token{
		ID:            uuid.New().String(),
		AccountID:     accountID,
		EnvironmentID: req.EnvironmentID,
		BearerType:    bearerType,
		BearerID:      req.BearerID,
		Name:          req.Name,
		Digest:        digest,
		Token:         tokenKey, // Store plain token for response
		Kind:          entities.TokenKindBearer,
		ExpiresAt:     req.ExpiresAt,
		LastUsed:      nil, // Not used yet
	}

	// Set permissions if provided (convert to string slice)
	if req.Permissions != nil {
		var permissions []string
		// Flatten TokenPermissions struct to string slice
		permissions = append(permissions, req.Permissions.Licenses...)
		permissions = append(permissions, req.Permissions.Machines...)
		permissions = append(permissions, req.Permissions.Users...)
		permissions = append(permissions, req.Permissions.Products...)
		permissions = append(permissions, req.Permissions.Policies...)
		permissions = append(permissions, req.Permissions.Webhooks...)
		permissions = append(permissions, req.Permissions.Analytics...)
		permissions = append(permissions, req.Permissions.Billing...)
		token.CustomPermissions = permissions
	}

	// Set metadata if provided
	if len(req.Metadata) > 0 {
		token.Metadata = req.Metadata
	}

	// Save token to database
	if err := gs.tokenRepo.Create(ctx, token); err != nil {
		return nil, fmt.Errorf("failed to create token: %w", err)
	}

	return &TokenResult{
		Token:    token,
		TokenKey: tokenKey,
	}, nil
}

// generateTokenKey generates a secure token key with the appropriate prefix
func (gs *GeneratorService) generateTokenKey(tokenType TokenType) string {
	// Determine prefix based on token type
	var prefix string
	switch tokenType {
	case TokenTypeUser:
		prefix = "usr"
	case TokenTypeProduct:
		prefix = "prod"
	case TokenTypeService:
		prefix = "svc"
	case TokenTypeAdmin:
		prefix = "admin"
	default:
		prefix = "token"
	}

	// Generate exactly 32 bytes for 64 hex characters (matches regex pattern)
	randomBytes := make([]byte, 32)
	if _, err := rand.Read(randomBytes); err != nil {
		// Fallback to time-based generation if crypto/rand fails
		now := time.Now().UnixNano()
		for i := range randomBytes {
			randomBytes[i] = byte(now >> (i % 8))
			now = now*1103515245 + 12345 // Simple LCG
		}
	}

	// Encode to exactly 64 hex characters
	randomHex := hex.EncodeToString(randomBytes)

	// Format: prefix-64hexchars (matches TokenPattern regex)
	return fmt.Sprintf("%s-%s", prefix, randomHex)
}

// RevokeToken revokes a token by ID
func (gs *GeneratorService) RevokeToken(ctx context.Context, tokenID string) error {
	tokenUUID, err := uuid.Parse(tokenID)
	if err != nil {
		return fmt.Errorf("invalid token ID: %w", err)
	}

	// Mark token as revoked
	token, err := gs.tokenRepo.GetByID(ctx, tokenUUID)
	if err != nil {
		return fmt.Errorf("token not found: %w", err)
	}

	now := time.Now()
	token.RevokedAt = &now
	token.UpdatedAt = now

	return gs.tokenRepo.Update(ctx, token)
}

// RegenerateToken regenerates a token (creates new token key, keeps same permissions)
func (gs *GeneratorService) RegenerateToken(ctx context.Context, tokenID string) (*TokenResult, error) {
	tokenUUID, err := uuid.Parse(tokenID)
	if err != nil {
		return nil, fmt.Errorf("invalid token ID: %w", err)
	}

	// Get existing token
	existingToken, err := gs.tokenRepo.GetByID(ctx, tokenUUID)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Get account for secret key
	accountUUID, err := uuid.Parse(existingToken.AccountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	account, err := gs.accountRepo.GetByID(ctx, accountUUID)
	if err != nil {
		return nil, fmt.Errorf("account not found: %w", err)
	}

	// Generate new token key (determine type from bearer type)
	var tokenType TokenType
	switch existingToken.BearerType {
	case entities.TokenBearerTypeUser:
		tokenType = TokenTypeUser
	case entities.TokenBearerTypeProduct:
		tokenType = TokenTypeProduct
	case entities.TokenBearerTypeLicense:
		tokenType = TokenTypeService // License tokens use service type
	case entities.TokenBearerTypeEnvironment:
		tokenType = TokenTypeAdmin // Environment tokens use admin type
	default:
		tokenType = TokenTypeUser // Default fallback
	}

	newTokenKey := gs.generateTokenKey(tokenType)

	// Generate new digest
	var newDigest string
	if account.SecretKey != nil {
		newDigest = gs.lookupService.GenerateTokenDigest(newTokenKey, *account.SecretKey)
	} else {
		return nil, fmt.Errorf("account secret key not available")
	}

	// Update token with new digest
	existingToken.Digest = newDigest
	existingToken.UpdatedAt = time.Now()
	existingToken.LastUsed = nil // Reset usage tracking

	if err := gs.tokenRepo.Update(ctx, existingToken); err != nil {
		return nil, fmt.Errorf("failed to update token: %w", err)
	}

	// Set plain token for response
	existingToken.Token = newTokenKey

	return &TokenResult{
		Token:    existingToken,
		TokenKey: newTokenKey,
	}, nil

	/*
			// TODO: Fix all remaining methods - they have entity field mismatches
			tokenUUID, err := uuid.Parse(tokenID)
			if err != nil {
				return nil, fmt.Errorf("invalid token ID: %w", err)
			}

			// Get existing token
			existingToken, err := gs.tokenRepo.GetByID(ctx, tokenUUID)
			if err != nil {
				return nil, fmt.Errorf("token not found: %w", err)
			}

			// Get account for secret key
			accountUUID, err := uuid.Parse(existingToken.AccountID)
			if err != nil {
				return nil, fmt.Errorf("invalid account ID: %w", err)
			}

			account, err := gs.accountRepo.GetByID(ctx, accountUUID)
			if err != nil {
				return nil, fmt.Errorf("account not found: %w", err)
			}

			// Generate new token key
			tokenType := TokenType(existingToken.TokenType)
			newTokenKey := gs.generateTokenKey(tokenType)

			// Generate new digest
			newDigest := gs.lookupService.GenerateTokenDigest(newTokenKey, account.SecretKey)

			// Update token with new digest
			existingToken.Digest = newDigest
			existingToken.UpdatedAt = time.Now()
			existingToken.LastUsedAt = nil // Reset usage tracking

			if err := gs.tokenRepo.Update(ctx, tokenUUID, existingToken); err != nil {
				return nil, fmt.Errorf("failed to update token: %w", err)
			}

			return &TokenResult{
				Token:    existingToken,
				TokenKey: newTokenKey,
			}, nil
		}

		// ListTokens lists tokens for an account with optional filtering
		func (gs *GeneratorService) ListTokens(ctx context.Context, accountID string, environmentID *string, bearerType *string) ([]entities.Token, error) {
			accountUUID, err := uuid.Parse(accountID)
			if err != nil {
				return nil, fmt.Errorf("invalid account ID: %w", err)
			}

			return gs.tokenRepo.GetByAccount(ctx, accountUUID)
		}

		// UpdateTokenUsage updates the last used timestamp for a token
		func (gs *GeneratorService) UpdateTokenUsage(ctx context.Context, tokenID string) error {
			tokenUUID, err := uuid.Parse(tokenID)
			if err != nil {
				return fmt.Errorf("invalid token ID: %w", err)
			}

			token, err := gs.tokenRepo.GetByID(ctx, tokenUUID)
			if err != nil {
				return fmt.Errorf("token not found: %w", err)
			}

			now := time.Now()
			token.LastUsedAt = &now
			token.UpdatedAt = now

			return gs.tokenRepo.Update(ctx, tokenUUID, token)
		}

		// encodePermissions encodes permissions to JSON string
		func (gs *GeneratorService) encodePermissions(permissions *TokenPermissions) (string, error) {
			if permissions == nil {
				return "", nil
			}

			permissionsBytes, err := json.Marshal(permissions)
			if err != nil {
				return "", fmt.Errorf("failed to encode permissions: %w", err)
			}

			return string(permissionsBytes), nil
		}

		// encodeMetadata encodes metadata to JSON string
		func (gs *GeneratorService) encodeMetadata(metadata map[string]interface{}) (string, error) {
			if len(metadata) == 0 {
				return "", nil
			}

			metadataBytes, err := json.Marshal(metadata)
			if err != nil {
				return "", fmt.Errorf("failed to encode metadata: %w", err)
			}

			return string(metadataBytes), nil
		}

		// GetTokenPermissions decodes and returns token permissions
		func (gs *GeneratorService) GetTokenPermissions(token *entities.Token) (*TokenPermissions, error) {
			if token.Permissions == nil || *token.Permissions == "" {
				return nil, nil
			}

			var permissions TokenPermissions
			if err := json.Unmarshal([]byte(*token.Permissions), &permissions); err != nil {
				return nil, fmt.Errorf("failed to decode permissions: %w", err)
			}

			return &permissions, nil
		}

		// ValidateTokenPermission checks if a token has a specific permission
		func (gs *GeneratorService) ValidateTokenPermission(token *entities.Token, resource, action string) (bool, error) {
			permissions, err := gs.GetTokenPermissions(token)
			if err != nil {
				return false, err
			}

			if permissions == nil {
				return false, nil // No permissions set
			}

			// Check permission based on resource type
			var allowedActions []string
			switch resource {
			case "license":
				allowedActions = permissions.Licenses
			case "machine":
				allowedActions = permissions.Machines
			case "user":
				allowedActions = permissions.Users
			case "product":
				allowedActions = permissions.Products
			case "policy":
				allowedActions = permissions.Policies
			case "webhook":
				allowedActions = permissions.Webhooks
			case "analytics":
				allowedActions = permissions.Analytics
			case "billing":
				allowedActions = permissions.Billing
			default:
				return false, nil
			}

			// Check if action is allowed
			permissionKey := fmt.Sprintf("%s.%s", resource, action)
			for _, allowed := range allowedActions {
				if allowed == permissionKey || allowed == fmt.Sprintf("%s.*", resource) || allowed == "*" {
					return true, nil
				}
			}

			return false, nil
		}
	*/
}
