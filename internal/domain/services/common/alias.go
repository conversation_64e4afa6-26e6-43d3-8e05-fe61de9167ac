package common

import (
	"context"
	"fmt"
	"regexp"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// AliasService handles flexible entity lookup by ID or aliases (maps to Ruby FindByAliasService)
type AliasService struct {
	// Generic repositories for different entity types
	accountRepo     repositories.AccountRepository
	environmentRepo repositories.EnvironmentRepository
	licenseRepo     repositories.LicenseRepository
	productRepo     repositories.ProductRepository
	policyRepo      repositories.PolicyRepository
	userRepo        repositories.UserRepository
	machineRepo     repositories.MachineRepository
}

// NewAliasService creates a new alias lookup service
func NewAliasService(
	accountRepo repositories.AccountRepository,
	environmentRepo repositories.EnvironmentRepository,
	licenseRepo repositories.LicenseRepository,
	productRepo repositories.ProductRepository,
	policyRepo repositories.PolicyRepository,
	userRepo repositories.UserRepository,
	machineRepo repositories.MachineRepository,
) *AliasService {
	return &AliasService{
		accountRepo:     accountRepo,
		environmentRepo: environmentRepo,
		licenseRepo:     licenseRepo,
		productRepo:     productRepo,
		policyRepo:      policyRepo,
		userRepo:        userRepo,
		machineRepo:     machineRepo,
	}
}

// FindOptions represents lookup configuration (maps to Ruby FindByAliasService params)
type FindOptions struct {
	Aliases []string `json:"aliases"`         // List of alias fields to check
	Reorder bool     `json:"reorder"`         // Whether to reorder by created_at ASC (default: true)
	Scope   *string  `json:"scope,omitempty"` // Optional scope (e.g., account ID)
}

// UUID pattern for validation (maps to Ruby UUID_RE)
var UUIDPattern = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)

// EntityType represents the type of entity to search
type EntityType string

const (
	EntityTypeAccount     EntityType = "account"
	EntityTypeEnvironment EntityType = "environment"
	EntityTypeLicense     EntityType = "license"
	EntityTypeProduct     EntityType = "product"
	EntityTypePolicy      EntityType = "policy"
	EntityTypeUser        EntityType = "user"
	EntityTypeMachine     EntityType = "machine"
)

// FindAccountByAlias finds account by ID or aliases (maps to Ruby FindByAliasService.call for Account)
func (as *AliasService) FindAccountByAlias(ctx context.Context, id string, options FindOptions) (interface{}, error) {
	if id == "" {
		return nil, fmt.Errorf("account ID cannot be blank")
	}

	// Primary key lookup if ID resembles UUID
	if UUIDPattern.MatchString(id) {
		accountUUID, err := uuid.Parse(id)
		if err == nil {
			if account, err := as.accountRepo.GetByID(ctx, accountUUID); err == nil {
				return account, nil
			}
		}
	}

	// Try alias lookups
	for _, alias := range options.Aliases {
		switch alias {
		case "slug":
			if account, err := as.accountRepo.GetBySlug(ctx, id); err == nil {
				return account, nil
			}
		case "cname", "domain":
			// TODO: Implement GetByCNAME/GetByDomain when available
			// if account, err := as.accountRepo.GetByCNAME(ctx, id); err == nil {
			//     return account, nil
			// }
		}
	}

	return nil, fmt.Errorf("account not found")
}

// FindEnvironmentByAlias finds environment by ID or aliases within account scope
func (as *AliasService) FindEnvironmentByAlias(ctx context.Context, id string, accountID string, options FindOptions) (interface{}, error) {
	if id == "" {
		return nil, fmt.Errorf("environment ID cannot be blank")
	}

	if accountID == "" {
		return nil, fmt.Errorf("account ID is required for environment lookup")
	}

	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Primary key lookup if ID resembles UUID
	if UUIDPattern.MatchString(id) {
		environmentUUID, err := uuid.Parse(id)
		if err == nil {
			if environment, err := as.environmentRepo.GetByID(ctx, environmentUUID); err == nil {
				// Verify environment belongs to account
				if environment.AccountID == accountID {
					return environment, nil
				}
			}
		}
	}

	// Get all environments for account using List filter
	filter := repositories.ListFilter{
		AccountID: &accountUUID,
		PageSize:  1000,
		Page:      1,
	}

	environmentPointers, _, err := as.environmentRepo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get environments: %w", err)
	}

	// Convert to slice of values
	environments := make([]entities.Environment, len(environmentPointers))
	for i, envPtr := range environmentPointers {
		environments[i] = *envPtr
	}

	// Try alias lookups
	for _, alias := range options.Aliases {
		for _, env := range environments {
			switch alias {
			case "code":
				if env.Code == id {
					return &env, nil
				}
			case "name":
				if env.Name == id {
					return &env, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("environment not found")
}

// FindLicenseByAlias finds license by ID or aliases within account scope
func (as *AliasService) FindLicenseByAlias(ctx context.Context, id string, accountID string, options FindOptions) (interface{}, error) {
	if id == "" {
		return nil, fmt.Errorf("license ID cannot be blank")
	}

	if accountID == "" {
		return nil, fmt.Errorf("account ID is required for license lookup")
	}

	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Primary key lookup if ID resembles UUID
	if UUIDPattern.MatchString(id) {
		licenseUUID, err := uuid.Parse(id)
		if err == nil {
			if license, err := as.licenseRepo.GetByID(ctx, licenseUUID); err == nil {
				// Verify license belongs to account
				if license.AccountID == accountID {
					return license, nil
				}
			}
		}
	}

	// Try alias lookups
	for _, alias := range options.Aliases {
		switch alias {
		case "key":
			if license, err := as.licenseRepo.GetByKey(ctx, id); err == nil {
				// Verify license belongs to account
				if license.AccountID == accountID {
					return license, nil
				}
			}
		case "name":
			// Get all licenses for account using List filter
			filter := repositories.ListFilter{
				AccountID: &accountUUID,
				PageSize:  1000,
				Page:      1,
			}

			licensePointers, _, err := as.licenseRepo.List(ctx, filter)
			if err == nil {
				for _, licensePtr := range licensePointers {
					if licensePtr.Name == id {
						return licensePtr, nil
					}
				}
			}
		}
	}

	return nil, fmt.Errorf("license not found")
}

// FindUserByAlias finds user by ID or aliases within account scope
func (as *AliasService) FindUserByAlias(ctx context.Context, id string, accountID string, options FindOptions) (interface{}, error) {
	if id == "" {
		return nil, fmt.Errorf("user ID cannot be blank")
	}

	if accountID == "" {
		return nil, fmt.Errorf("account ID is required for user lookup")
	}

	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Primary key lookup if ID resembles UUID
	if UUIDPattern.MatchString(id) {
		userUUID, err := uuid.Parse(id)
		if err == nil {
			if user, err := as.userRepo.GetByID(ctx, userUUID); err == nil {
				// Verify user belongs to account
				if user.AccountID == accountID {
					return user, nil
				}
			}
		}
	}

	// Try alias lookups
	for _, alias := range options.Aliases {
		switch alias {
		case "email":
			if user, err := as.userRepo.GetByEmail(ctx, id, accountUUID); err == nil {
				// User already scoped to account by repository method
				return user, nil
			}
		case "username":
			// Get all users for account and search by username
			// TODO: Implement GetByAccount for users
			// users, err := as.userRepo.GetByAccount(ctx, accountUUID)
			// if err == nil {
			//     for _, user := range users {
			//         if user.Username == id {
			//             return &user, nil
			//         }
			//     }
			// }
		}
	}

	return nil, fmt.Errorf("user not found")
}

// FindMachineByAlias finds machine by ID or aliases within license scope
func (as *AliasService) FindMachineByAlias(ctx context.Context, id string, licenseID string, options FindOptions) (interface{}, error) {
	if id == "" {
		return nil, fmt.Errorf("machine ID cannot be blank")
	}

	if licenseID == "" {
		return nil, fmt.Errorf("license ID is required for machine lookup")
	}

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		return nil, fmt.Errorf("invalid license ID: %w", err)
	}

	// Primary key lookup if ID resembles UUID
	if UUIDPattern.MatchString(id) {
		machineUUID, err := uuid.Parse(id)
		if err == nil {
			if machine, err := as.machineRepo.GetByID(ctx, machineUUID); err == nil {
				// Verify machine belongs to license
				if machine.LicenseID == licenseID {
					return machine, nil
				}
			}
		}
	}

	// Get all machines for license and search by aliases
	machines, err := as.machineRepo.GetByLicense(ctx, licenseUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get machines: %w", err)
	}

	// Try alias lookups
	for _, alias := range options.Aliases {
		for _, machine := range machines {
			switch alias {
			case "fingerprint":
				if machine.Fingerprint == id {
					return &machine, nil
				}
			case "hostname":
				if machine.Hostname == id {
					return &machine, nil
				}
			case "name":
				if machine.Name == id {
					return &machine, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("machine not found")
}

// ValidateID validates if an ID is not blank and optionally checks UUID format
func (as *AliasService) ValidateID(id string, requireUUID bool) error {
	if id == "" {
		return fmt.Errorf("ID cannot be blank")
	}

	if requireUUID && !UUIDPattern.MatchString(id) {
		return fmt.Errorf("ID must be a valid UUID")
	}

	return nil
}

// IsValidUUID checks if a string is a valid UUID format
func (as *AliasService) IsValidUUID(s string) bool {
	return UUIDPattern.MatchString(s)
}
