package environment

import (
	"context"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// ResolverService handles environment resolution (maps to Ruby ResolveEnvironmentService)
type ResolverService struct {
	environmentRepo repositories.EnvironmentRepository
	cache           CacheInterface
	cacheTTL        time.Duration
}

// CacheInterface defines the caching interface for environment resolution
type CacheInterface interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// NewResolverService creates a new environment resolver service
func NewResolverService(
	environmentRepo repositories.EnvironmentRepository,
	cache CacheInterface,
) *ResolverService {
	return &ResolverService{
		environmentRepo: environmentRepo,
		cache:           cache,
		cacheTTL:        15 * time.Minute, // Maps to Ruby ENVIRONMENT_SCOPE_CACHE_TTL
	}
}

// ResolveOptions represents resolution configuration
type ResolveOptions struct {
	EnterpriseRequired bool `json:"enterprise_required"` // Whether enterprise features are required
}

// ResolveEnvironment resolves an environment by identifier (maps to Ruby ResolveEnvironmentService.call)
func (rs *ResolverService) ResolveEnvironment(ctx context.Context, environmentID string, account *entities.Account, options *ResolveOptions) (*entities.Environment, error) {
	// Set default options
	if options == nil {
		options = &ResolveOptions{}
	}

	// Check if enterprise features are required but not available
	if options.EnterpriseRequired {
		// TODO: Check if account has enterprise license
		// This would involve checking account.Plan or similar field
		// For now, assume enterprise is available
	}

	// If no environment specified, return nil (global environment)
	if environmentID == "" {
		return nil, nil
	}

	// Try cache first
	environment, err := rs.getFromCache(ctx, environmentID, account)
	if err == nil && environment != nil {
		return environment, nil
	}

	// Find environment by ID or alias
	environment, err = rs.findByAlias(ctx, environmentID, account, []string{"code"})
	if err != nil {
		return nil, fmt.Errorf("environment is invalid: %w", err)
	}

	// Cache the result
	if rs.cache != nil {
		if err := rs.cacheEnvironment(ctx, environmentID, account, environment); err != nil {
			// Log error but don't fail the resolution
			fmt.Printf("Failed to cache environment: %v\n", err)
		}
	}

	return environment, nil
}

// findByAlias finds environment by ID or alias fields (maps to Ruby FindByAliasService.call)
func (rs *ResolverService) findByAlias(ctx context.Context, id string, account *entities.Account, aliases []string) (*entities.Environment, error) {
	if id == "" {
		return nil, fmt.Errorf("environment ID cannot be blank")
	}

	if account == nil {
		return nil, fmt.Errorf("account is required")
	}

	// Parse account ID
	accountUUID, err := uuid.Parse(account.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Try direct ID lookup first if it looks like a UUID
	if isValidUUID(id) {
		environmentUUID, err := uuid.Parse(id)
		if err == nil {
			// Check if environment belongs to the account
			environment, err := rs.environmentRepo.GetByID(ctx, environmentUUID)
			if err == nil && environment.AccountID == account.ID {
				return environment, nil
			}
		}
	}

	// Try alias lookups within the account scope
	for _, alias := range aliases {
		switch alias {
		case "code":
			// Get environments by code using repository method
			environment, err := rs.environmentRepo.GetByCode(ctx, id, accountUUID)
			if err == nil && environment != nil {
				return environment, nil
			}
		case "name":
			// Get all environments for account and filter by name
			filter := repositories.ListFilter{
				AccountID: &accountUUID,
				PageSize:  1000,
				Page:      1,
			}

			environmentPointers, _, err := rs.environmentRepo.List(ctx, filter)
			if err == nil {
				for _, envPtr := range environmentPointers {
					if envPtr.Name == id {
						return envPtr, nil
					}
				}
			}
		}
	}

	return nil, fmt.Errorf("environment not found")
}

// getFromCache retrieves environment from cache
func (rs *ResolverService) getFromCache(ctx context.Context, environmentID string, account *entities.Account) (*entities.Environment, error) {
	if rs.cache == nil {
		return nil, fmt.Errorf("cache not configured")
	}

	// Build cache key (maps to Ruby Environment.cache_key)
	cacheKey := rs.buildCacheKey(environmentID, account)

	// Check if cached
	exists, err := rs.cache.Exists(ctx, cacheKey)
	if err != nil || !exists {
		return nil, fmt.Errorf("not found in cache")
	}

	// Get cached environment ID
	cachedEnvID, err := rs.cache.Get(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	// Parse and get environment
	environmentUUID, err := uuid.Parse(cachedEnvID)
	if err != nil {
		return nil, fmt.Errorf("invalid cached environment ID")
	}

	environment, err := rs.environmentRepo.GetByID(ctx, environmentUUID)
	if err != nil {
		// Remove invalid cache entry
		rs.cache.Delete(ctx, cacheKey)
		return nil, err
	}

	// Verify environment still belongs to account
	if environment.AccountID != account.ID {
		// Remove invalid cache entry
		rs.cache.Delete(ctx, cacheKey)
		return nil, fmt.Errorf("environment account mismatch")
	}

	return environment, nil
}

// cacheEnvironment stores environment in cache
func (rs *ResolverService) cacheEnvironment(ctx context.Context, environmentID string, account *entities.Account, environment *entities.Environment) error {
	if rs.cache == nil {
		return fmt.Errorf("cache not configured")
	}

	cacheKey := rs.buildCacheKey(environmentID, account)
	return rs.cache.Set(ctx, cacheKey, environment.ID, rs.cacheTTL)
}

// buildCacheKey builds cache key for environment (maps to Ruby Environment.cache_key)
func (rs *ResolverService) buildCacheKey(environmentID string, account *entities.Account) string {
	return fmt.Sprintf("environment:%s:account:%s", environmentID, account.ID)
}

// isValidUUID checks if a string is a valid UUID format
func isValidUUID(s string) bool {
	_, err := uuid.Parse(s)
	return err == nil
}

// SetCacheTTL sets the cache time-to-live duration
func (rs *ResolverService) SetCacheTTL(ttl time.Duration) {
	rs.cacheTTL = ttl
}

// InvalidateEnvironmentCache invalidates cache entries for an environment
func (rs *ResolverService) InvalidateEnvironmentCache(ctx context.Context, environmentID string, account *entities.Account) error {
	if rs.cache == nil {
		return nil
	}

	cacheKey := rs.buildCacheKey(environmentID, account)
	return rs.cache.Delete(ctx, cacheKey)
}

// InvalidateAccountEnvironments invalidates all environment cache entries for an account
func (rs *ResolverService) InvalidateAccountEnvironments(ctx context.Context, account *entities.Account) error {
	if rs.cache == nil {
		return nil
	}

	// TODO: Implement pattern-based cache invalidation
	// This would require cache implementation that supports pattern deletion
	// For now, we'd need to track individual environment IDs or use cache tags

	return nil
}

// ValidateEnvironmentAccess validates that an environment belongs to the account
func (rs *ResolverService) ValidateEnvironmentAccess(ctx context.Context, environment *entities.Environment, account *entities.Account) error {
	if environment == nil {
		return nil // Global environment is always accessible
	}

	if account == nil {
		return fmt.Errorf("account is required")
	}

	if environment.AccountID != account.ID {
		return fmt.Errorf("environment does not belong to account")
	}

	return nil
}
