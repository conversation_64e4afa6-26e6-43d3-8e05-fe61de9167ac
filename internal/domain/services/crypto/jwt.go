package crypto

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// JWTService provides JWT token operations with RS256 and ES256 support
type JWTService struct {
	rsaService     *RSAService
	ed25519Service *Ed25519Service
}

// NewJWTService creates a new JWT service instance
func NewJWTService() *JWTService {
	return &JWTService{
		rsaService:     NewRSAService(),
		ed25519Service: NewEd25519Service(),
	}
}

// JWTHeader represents the JWT header
type JWTHeader struct {
	Algorithm string `json:"alg"`
	Type      string `json:"typ"`
	KeyID     string `json:"kid,omitempty"`
}

// JWTClaims represents standard JWT claims
type JWTClaims struct {
	Issuer         string                 `json:"iss,omitempty"`
	Subject        string                 `json:"sub,omitempty"`
	Audience       []string               `json:"aud,omitempty"`
	ExpiresAt      int64                  `json:"exp,omitempty"`
	NotBefore      int64                  `json:"nbf,omitempty"`
	IssuedAt       int64                  `json:"iat,omitempty"`
	JWTID          string                 `json:"jti,omitempty"`
	CustomClaims   map[string]interface{} `json:"-"`
}

// TokenOptions represents options for token creation
type TokenOptions struct {
	Algorithm   string
	KeyID       string
	ExpiresIn   time.Duration
	NotBefore   time.Time
	Issuer      string
	Subject     string
	Audience    []string
	JWTID       string
	CustomClaims map[string]interface{}
}

// CreateToken creates a new JWT token using the specified algorithm and key
func (js *JWTService) CreateToken(privateKeyPEM string, opts TokenOptions) (string, error) {
	now := time.Now()
	
	// Create header
	header := JWTHeader{
		Algorithm: opts.Algorithm,
		Type:      "JWT",
		KeyID:     opts.KeyID,
	}

	// Create claims
	claims := JWTClaims{
		Issuer:    opts.Issuer,
		Subject:   opts.Subject,
		Audience:  opts.Audience,
		IssuedAt:  now.Unix(),
		JWTID:     opts.JWTID,
		CustomClaims: opts.CustomClaims,
	}

	// Set expiration
	if opts.ExpiresIn > 0 {
		claims.ExpiresAt = now.Add(opts.ExpiresIn).Unix()
	}

	// Set not before
	if !opts.NotBefore.IsZero() {
		claims.NotBefore = opts.NotBefore.Unix()
	}

	// Encode header and payload
	headerJSON, err := json.Marshal(header)
	if err != nil {
		return "", fmt.Errorf("failed to marshal header: %w", err)
	}

	// Merge custom claims into standard claims
	claimsMap := make(map[string]interface{})
	claimsJSON, _ := json.Marshal(claims)
	json.Unmarshal(claimsJSON, &claimsMap)
	
	// Add custom claims
	for k, v := range opts.CustomClaims {
		claimsMap[k] = v
	}

	payloadJSON, err := json.Marshal(claimsMap)
	if err != nil {
		return "", fmt.Errorf("failed to marshal claims: %w", err)
	}

	// Base64URL encode header and payload
	headerB64 := base64URLEncode(headerJSON)
	payloadB64 := base64URLEncode(payloadJSON)

	// Create signing input
	signingInput := headerB64 + "." + payloadB64

	// Sign based on algorithm
	var signature string
	switch opts.Algorithm {
	case "RS256":
		signature, err = js.signRS256(privateKeyPEM, []byte(signingInput))
	case "ES256":
		signature, err = js.signES256(privateKeyPEM, []byte(signingInput))
	default:
		return "", fmt.Errorf("unsupported algorithm: %s", opts.Algorithm)
	}

	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return signingInput + "." + signature, nil
}

// VerifyToken verifies a JWT token and returns the claims
func (js *JWTService) VerifyToken(token, publicKeyPEM string) (*JWTClaims, map[string]interface{}, error) {
	// Split token into parts
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, nil, fmt.Errorf("invalid token format")
	}

	headerB64, payloadB64, signatureB64 := parts[0], parts[1], parts[2]

	// Decode header
	headerJSON, err := base64URLDecode(headerB64)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode header: %w", err)
	}

	var header JWTHeader
	if err := json.Unmarshal(headerJSON, &header); err != nil {
		return nil, nil, fmt.Errorf("failed to parse header: %w", err)
	}

	// Verify signature
	signingInput := headerB64 + "." + payloadB64
	switch header.Algorithm {
	case "RS256":
		if err := js.verifyRS256(publicKeyPEM, []byte(signingInput), signatureB64); err != nil {
			return nil, nil, fmt.Errorf("RS256 signature verification failed: %w", err)
		}
	case "ES256":
		if err := js.verifyES256(publicKeyPEM, []byte(signingInput), signatureB64); err != nil {
			return nil, nil, fmt.Errorf("ES256 signature verification failed: %w", err)
		}
	default:
		return nil, nil, fmt.Errorf("unsupported algorithm: %s", header.Algorithm)
	}

	// Decode payload
	payloadJSON, err := base64URLDecode(payloadB64)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode payload: %w", err)
	}

	// Parse claims
	var allClaims map[string]interface{}
	if err := json.Unmarshal(payloadJSON, &allClaims); err != nil {
		return nil, nil, fmt.Errorf("failed to parse claims: %w", err)
	}

	// Extract standard claims
	var standardClaims JWTClaims
	claimsJSON, _ := json.Marshal(allClaims)
	json.Unmarshal(claimsJSON, &standardClaims)

	// Validate time-based claims
	now := time.Now().Unix()
	
	if standardClaims.ExpiresAt > 0 && now > standardClaims.ExpiresAt {
		return nil, nil, fmt.Errorf("token has expired")
	}

	if standardClaims.NotBefore > 0 && now < standardClaims.NotBefore {
		return nil, nil, fmt.Errorf("token not yet valid")
	}

	return &standardClaims, allClaims, nil
}

// signRS256 signs data using RSA-PSS with SHA-256 (RS256)
func (js *JWTService) signRS256(privateKeyPEM string, data []byte) (string, error) {
	signature, err := js.rsaService.Sign(privateKeyPEM, data)
	if err != nil {
		return "", err
	}

	// Convert to base64URL encoding
	sigBytes, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return "", err
	}

	return base64URLEncode(sigBytes), nil
}

// verifyRS256 verifies RSA-PSS signature with SHA-256 (RS256)
func (js *JWTService) verifyRS256(publicKeyPEM string, data []byte, signatureB64 string) error {
	// Convert from base64URL to standard base64
	sigBytes, err := base64URLDecode(signatureB64)
	if err != nil {
		return err
	}

	signature := base64.StdEncoding.EncodeToString(sigBytes)
	return js.rsaService.Verify(publicKeyPEM, data, signature)
}

// signES256 signs data using Ed25519 (ES256)
func (js *JWTService) signES256(privateKeyPEM string, data []byte) (string, error) {
	signature, err := js.ed25519Service.Sign(privateKeyPEM, data)
	if err != nil {
		return "", err
	}

	// Convert to base64URL encoding
	sigBytes, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return "", err
	}

	return base64URLEncode(sigBytes), nil
}

// verifyES256 verifies Ed25519 signature (ES256)
func (js *JWTService) verifyES256(publicKeyPEM string, data []byte, signatureB64 string) error {
	// Convert from base64URL to standard base64
	sigBytes, err := base64URLDecode(signatureB64)
	if err != nil {
		return err
	}

	signature := base64.StdEncoding.EncodeToString(sigBytes)
	return js.ed25519Service.Verify(publicKeyPEM, data, signature)
}

// ParseTokenWithoutVerification parses a JWT token without verifying the signature
func (js *JWTService) ParseTokenWithoutVerification(token string) (*JWTHeader, map[string]interface{}, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, nil, fmt.Errorf("invalid token format")
	}

	// Decode header
	headerJSON, err := base64URLDecode(parts[0])
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode header: %w", err)
	}

	var header JWTHeader
	if err := json.Unmarshal(headerJSON, &header); err != nil {
		return nil, nil, fmt.Errorf("failed to parse header: %w", err)
	}

	// Decode payload
	payloadJSON, err := base64URLDecode(parts[1])
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode payload: %w", err)
	}

	var claims map[string]interface{}
	if err := json.Unmarshal(payloadJSON, &claims); err != nil {
		return nil, nil, fmt.Errorf("failed to parse claims: %w", err)
	}

	return &header, claims, nil
}

// ValidateTokenStructure validates the basic structure of a JWT token
func (js *JWTService) ValidateTokenStructure(token string) error {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return fmt.Errorf("invalid token format: expected 3 parts, got %d", len(parts))
	}

	// Validate each part is valid base64URL
	for i, part := range parts {
		if _, err := base64URLDecode(part); err != nil {
			return fmt.Errorf("invalid base64URL encoding in part %d: %w", i+1, err)
		}
	}

	return nil
}

// GetTokenInfo returns information about a JWT token without verification
func (js *JWTService) GetTokenInfo(token string) (map[string]interface{}, error) {
	header, claims, err := js.ParseTokenWithoutVerification(token)
	if err != nil {
		return nil, err
	}

	info := map[string]interface{}{
		"algorithm": header.Algorithm,
		"type":      header.Type,
		"key_id":    header.KeyID,
		"issued_at": claims["iat"],
		"expires_at": claims["exp"],
		"subject":   claims["sub"],
		"issuer":    claims["iss"],
	}

	// Check if token is expired
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			info["expired"] = true
		} else {
			info["expired"] = false
		}
	}

	return info, nil
}

// RefreshToken creates a new token with updated expiration using the same claims
func (js *JWTService) RefreshToken(token, privateKeyPEM string, newExpiresIn time.Duration) (string, error) {
	header, claims, err := js.ParseTokenWithoutVerification(token)
	if err != nil {
		return "", err
	}

	// Extract standard claims
	opts := TokenOptions{
		Algorithm: header.Algorithm,
		KeyID:     header.KeyID,
		ExpiresIn: newExpiresIn,
		CustomClaims: make(map[string]interface{}),
	}

	if iss, ok := claims["iss"].(string); ok {
		opts.Issuer = iss
	}
	if sub, ok := claims["sub"].(string); ok {
		opts.Subject = sub
	}
	if jti, ok := claims["jti"].(string); ok {
		opts.JWTID = jti
	}
	if aud, ok := claims["aud"].([]interface{}); ok {
		for _, a := range aud {
			if audStr, ok := a.(string); ok {
				opts.Audience = append(opts.Audience, audStr)
			}
		}
	}

	// Copy custom claims (excluding standard ones)
	standardClaims := map[string]bool{
		"iss": true, "sub": true, "aud": true, "exp": true,
		"nbf": true, "iat": true, "jti": true,
	}

	for k, v := range claims {
		if !standardClaims[k] {
			opts.CustomClaims[k] = v
		}
	}

	return js.CreateToken(privateKeyPEM, opts)
}

// Base64URL encoding functions

func base64URLEncode(data []byte) string {
	return strings.TrimRight(base64.URLEncoding.EncodeToString(data), "=")
}

func base64URLDecode(s string) ([]byte, error) {
	// Add padding if needed
	switch len(s) % 4 {
	case 2:
		s += "=="
	case 3:
		s += "="
	}
	return base64.URLEncoding.DecodeString(s)
}

// Helper function to extract public key from RSA private key for JWT
func (js *JWTService) ExtractPublicKeyForJWT(privateKeyPEM, algorithm string) (string, error) {
	switch algorithm {
	case "RS256":
		return js.rsaService.GetPublicKeyFromPrivate(privateKeyPEM)
	case "ES256":
		return js.ed25519Service.GetPublicKeyFromPrivate(privateKeyPEM)
	default:
		return "", fmt.Errorf("unsupported algorithm for key extraction: %s", algorithm)
	}
}

// CreateSimpleToken creates a simple JWT token with minimal claims
func (js *JWTService) CreateSimpleToken(privateKeyPEM, algorithm, subject string, expiresIn time.Duration) (string, error) {
	opts := TokenOptions{
		Algorithm: algorithm,
		Subject:   subject,
		ExpiresIn: expiresIn,
		Issuer:    "gokeys",
	}

	return js.CreateToken(privateKeyPEM, opts)
}

// VerifyAndExtractClaims verifies a token and extracts specific claims
func (js *JWTService) VerifyAndExtractClaims(token, publicKeyPEM string, claimKeys []string) (map[string]interface{}, error) {
	_, allClaims, err := js.VerifyToken(token, publicKeyPEM)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	for _, key := range claimKeys {
		if value, exists := allClaims[key]; exists {
			result[key] = value
		}
	}

	return result, nil
}