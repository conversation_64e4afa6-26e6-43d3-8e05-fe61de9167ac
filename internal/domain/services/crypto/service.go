package crypto

import (
	"fmt"
	"time"
)

// CryptoService provides a unified interface for all cryptographic operations
type CryptoService struct {
	RSA     *RSAService
	Ed25519 *Ed25519Service
	AES     *AESService
	JWT     *JWTService
}

// NewCryptoService creates a new unified cryptographic service
func NewCryptoService() *CryptoService {
	return &CryptoService{
		RSA:     NewRSAService(),
		Ed25519: NewEd25519Service(),
		AES:     NewAESService(),
		JWT:     NewJWTService(),
	}
}

// CryptoScheme represents supported cryptographic schemes
type CryptoScheme string

const (
	SchemeRSA2048    CryptoScheme = "RSA-2048"
	SchemeEd25519    CryptoScheme = "Ed25519"
	SchemeAES256GCM  CryptoScheme = "AES-256-GCM"
	SchemeJWTRS256   CryptoScheme = "JWT-RS256"
	SchemeJWTES256   CryptoScheme = "JWT-ES256"
)

// GenerateKeyPairForScheme generates a key pair for the specified scheme
func (cs *CryptoService) GenerateKeyPairForScheme(scheme CryptoScheme) (interface{}, error) {
	switch scheme {
	case SchemeRSA2048:
		return cs.RSA.GenerateKeyPair()
	case SchemeEd25519:
		return cs.Ed25519.GenerateKeyPair()
	case SchemeAES256GCM:
		key, err := cs.AES.GenerateKey()
		if err != nil {
			return nil, err
		}
		return map[string]string{"symmetric_key": key}, nil
	default:
		return nil, fmt.Errorf("unsupported cryptographic scheme: %s", scheme)
	}
}

// SignData signs data using the specified scheme and private key
func (cs *CryptoService) SignData(scheme CryptoScheme, privateKey string, data []byte) (string, error) {
	switch scheme {
	case SchemeRSA2048:
		return cs.RSA.Sign(privateKey, data)
	case SchemeEd25519:
		return cs.Ed25519.Sign(privateKey, data)
	default:
		return "", fmt.Errorf("signing not supported for scheme: %s", scheme)
	}
}

// VerifySignature verifies a signature using the specified scheme and public key
func (cs *CryptoService) VerifySignature(scheme CryptoScheme, publicKey string, data []byte, signature string) error {
	switch scheme {
	case SchemeRSA2048:
		return cs.RSA.Verify(publicKey, data, signature)
	case SchemeEd25519:
		return cs.Ed25519.Verify(publicKey, data, signature)
	default:
		return fmt.Errorf("signature verification not supported for scheme: %s", scheme)
	}
}

// EncryptData encrypts data using the specified scheme
func (cs *CryptoService) EncryptData(scheme CryptoScheme, key string, data []byte) (interface{}, error) {
	switch scheme {
	case SchemeRSA2048:
		return cs.RSA.Encrypt(key, data)
	case SchemeAES256GCM:
		return cs.AES.Encrypt(key, data)
	default:
		return nil, fmt.Errorf("encryption not supported for scheme: %s", scheme)
	}
}

// DecryptData decrypts data using the specified scheme
func (cs *CryptoService) DecryptData(scheme CryptoScheme, key string, encryptedData interface{}) ([]byte, error) {
	switch scheme {
	case SchemeRSA2048:
		if ciphertext, ok := encryptedData.(string); ok {
			return cs.RSA.Decrypt(key, ciphertext)
		}
		return nil, fmt.Errorf("invalid encrypted data format for RSA")
	case SchemeAES256GCM:
		if encData, ok := encryptedData.(*EncryptedData); ok {
			return cs.AES.Decrypt(key, encData)
		}
		return nil, fmt.Errorf("invalid encrypted data format for AES")
	default:
		return nil, fmt.Errorf("decryption not supported for scheme: %s", scheme)
	}
}

// CreateJWTToken creates a JWT token using the specified algorithm
func (cs *CryptoService) CreateJWTToken(scheme CryptoScheme, privateKey string, opts TokenOptions) (string, error) {
	switch scheme {
	case SchemeJWTRS256:
		opts.Algorithm = "RS256"
		return cs.JWT.CreateToken(privateKey, opts)
	case SchemeJWTES256:
		opts.Algorithm = "ES256"
		return cs.JWT.CreateToken(privateKey, opts)
	default:
		return "", fmt.Errorf("JWT creation not supported for scheme: %s", scheme)
	}
}

// VerifyJWTToken verifies a JWT token using the specified scheme
func (cs *CryptoService) VerifyJWTToken(token string, publicKey string) (*JWTClaims, map[string]interface{}, error) {
	return cs.JWT.VerifyToken(token, publicKey)
}

// ValidateKeyPair validates a key pair for the specified scheme
func (cs *CryptoService) ValidateKeyPair(scheme CryptoScheme, privateKey, publicKey string) error {
	switch scheme {
	case SchemeRSA2048:
		return cs.RSA.ValidateKeyPair(privateKey, publicKey)
	case SchemeEd25519:
		return cs.Ed25519.ValidateKeyPair(privateKey, publicKey)
	default:
		return fmt.Errorf("key pair validation not supported for scheme: %s", scheme)
	}
}

// GetKeyInfo returns information about a key for the specified scheme
func (cs *CryptoService) GetKeyInfo(scheme CryptoScheme, key string) (map[string]interface{}, error) {
	switch scheme {
	case SchemeRSA2048:
		return cs.RSA.GetKeyInfo(key)
	case SchemeEd25519:
		return cs.Ed25519.GetKeyInfo(key)
	case SchemeAES256GCM:
		return cs.AES.GetKeyInfo(key)
	default:
		return nil, fmt.Errorf("key info not supported for scheme: %s", scheme)
	}
}

// GetPublicKeyFromPrivate extracts public key from private key for the specified scheme
func (cs *CryptoService) GetPublicKeyFromPrivate(scheme CryptoScheme, privateKey string) (string, error) {
	switch scheme {
	case SchemeRSA2048:
		return cs.RSA.GetPublicKeyFromPrivate(privateKey)
	case SchemeEd25519:
		return cs.Ed25519.GetPublicKeyFromPrivate(privateKey)
	default:
		return "", fmt.Errorf("public key extraction not supported for scheme: %s", scheme)
	}
}

// LicenseKeyGenerationOptions represents options for license key generation
type LicenseKeyGenerationOptions struct {
	AccountID     string
	ProductID     string
	LicenseID     string
	PolicyScheme  CryptoScheme
	ExpiresAt     *time.Time
	CustomClaims  map[string]interface{}
}

// GenerateLicenseKey generates a cryptographically signed license key
func (cs *CryptoService) GenerateLicenseKey(privateKey string, opts LicenseKeyGenerationOptions) (string, error) {
	// Create JWT claims for the license
	tokenOpts := TokenOptions{
		Subject:   opts.LicenseID,
		Issuer:    "gokeys-license-server",
		CustomClaims: map[string]interface{}{
			"account_id": opts.AccountID,
			"product_id": opts.ProductID,
			"license_id": opts.LicenseID,
			"policy_scheme": string(opts.PolicyScheme),
		},
	}

	// Add custom claims
	for k, v := range opts.CustomClaims {
		tokenOpts.CustomClaims[k] = v
	}

	// Set expiration
	if opts.ExpiresAt != nil {
		tokenOpts.ExpiresIn = time.Until(*opts.ExpiresAt)
	}

	// Generate token based on policy scheme
	switch opts.PolicyScheme {
	case SchemeRSA2048:
		return cs.CreateJWTToken(SchemeJWTRS256, privateKey, tokenOpts)
	case SchemeEd25519:
		return cs.CreateJWTToken(SchemeJWTES256, privateKey, tokenOpts)
	default:
		return "", fmt.Errorf("unsupported policy scheme for license key generation: %s", opts.PolicyScheme)
	}
}

// ValidateLicenseKey validates a license key and extracts its claims
func (cs *CryptoService) ValidateLicenseKey(licenseKey, publicKey string) (map[string]interface{}, error) {
	_, claims, err := cs.JWT.VerifyToken(licenseKey, publicKey)
	if err != nil {
		return nil, fmt.Errorf("license key validation failed: %w", err)
	}

	return claims, nil
}

// GetSupportedSchemes returns a list of supported cryptographic schemes
func (cs *CryptoService) GetSupportedSchemes() []CryptoScheme {
	return []CryptoScheme{
		SchemeRSA2048,
		SchemeEd25519,
		SchemeAES256GCM,
		SchemeJWTRS256,
		SchemeJWTES256,
	}
}

// ValidateScheme validates if a scheme is supported
func (cs *CryptoService) ValidateScheme(scheme CryptoScheme) error {
	for _, supported := range cs.GetSupportedSchemes() {
		if scheme == supported {
			return nil
		}
	}
	return fmt.Errorf("unsupported cryptographic scheme: %s", scheme)
}

// GenerateSecureToken generates a secure token for API authentication
func (cs *CryptoService) GenerateSecureToken(accountID, userID string, permissions []string, expiresIn time.Duration, privateKey string, scheme CryptoScheme) (string, error) {
	tokenOpts := TokenOptions{
		Subject:   userID,
		Issuer:    "gokeys-api",
		ExpiresIn: expiresIn,
		CustomClaims: map[string]interface{}{
			"account_id":  accountID,
			"permissions": permissions,
			"token_type":  "api_access",
		},
	}

	return cs.CreateJWTToken(scheme, privateKey, tokenOpts)
}

// ValidateAPIToken validates an API token and returns user context
func (cs *CryptoService) ValidateAPIToken(token, publicKey string) (map[string]interface{}, error) {
	_, claims, err := cs.JWT.VerifyToken(token, publicKey)
	if err != nil {
		return nil, fmt.Errorf("API token validation failed: %w", err)
	}

	// Ensure this is an API token
	if tokenType, ok := claims["token_type"].(string); !ok || tokenType != "api_access" {
		return nil, fmt.Errorf("invalid token type for API access")
	}

	return claims, nil
}

// CreateMachineFingerprint creates a cryptographic fingerprint for a machine
func (cs *CryptoService) CreateMachineFingerprint(machineInfo map[string]interface{}) (string, error) {
	// Serialize machine info to JSON for consistent hashing
	data, err := cs.serializeForHashing(machineInfo)
	if err != nil {
		return "", err
	}

	// Generate AES key for symmetric hashing
	key, err := cs.AES.GenerateKey()
	if err != nil {
		return "", err
	}

	// Encrypt the machine info to create a unique fingerprint
	encData, err := cs.AES.Encrypt(key, data)
	if err != nil {
		return "", err
	}

	// Return a shorter fingerprint based on the encrypted data
	return cs.createShortFingerprint(encData.Ciphertext), nil
}

// Helper function to serialize data consistently for hashing
func (cs *CryptoService) serializeForHashing(data map[string]interface{}) ([]byte, error) {
	// This would need a proper JSON serialization that's deterministic
	// For now, using a simple approach
	result := ""
	for k, v := range data {
		result += fmt.Sprintf("%s:%v;", k, v)
	}
	return []byte(result), nil
}

// Helper function to create a short fingerprint from encrypted data
func (cs *CryptoService) createShortFingerprint(ciphertext string) string {
	// Take first 16 characters of the ciphertext for a shorter fingerprint
	if len(ciphertext) > 16 {
		return ciphertext[:16]
	}
	return ciphertext
}