package services

import (
	"context"
	"os"
	"strconv"
	"time"

	"github.com/gokeys/gokeys/internal/adapters/cache"
	"github.com/gokeys/gokeys/internal/adapters/database/postgres/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/gokeys/gokeys/internal/domain/services/license"
	"github.com/gokeys/gokeys/internal/ports/delegate"
	"github.com/rs/zerolog"
	"gorm.io/gorm"
)

// ServiceCoordinator manages all domain services
type ServiceCoordinator struct {
	// Core services
	Crypto            *crypto.CryptoService
	LicenseValidation *license.ValidationService
	Events            *events.BroadcastService

	// Repository factory
	Repositories *repositories.RepositoryFactory

	// Cache (can be either MemoryCache or ValkeyCache)
	Cache     license.CacheInterface
	cacheType string

	// Delegate for inter-domain communication
	Delegate *delegate.Delegate
}

// NewServiceCoordinator creates a new service coordinator
func NewServiceCoordinator(db *gorm.DB, logger zerolog.Logger) *ServiceCoordinator {
	// Initialize cache based on environment
	var cacheInstance license.CacheInterface
	var cacheType string

	// Try to use Valkey if configured, fallback to memory cache
	if useValkey := os.Getenv("USE_VALKEY"); useValkey == "true" {
		valkeyConfig := cache.ValkeyConfig{
			Host:     getEnvString("VALKEY_HOST", "localhost"),
			Port:     getEnvInt("VALKEY_PORT", 6379),
			Password: getEnvString("VALKEY_PASSWORD", ""),
			DB:       getEnvInt("VALKEY_DB", 0),
		}

		if valkeyCache, err := cache.NewValkeyCache(valkeyConfig); err == nil {
			cacheInstance = valkeyCache
			cacheType = "valkey"
		} else {
			// Fallback to memory cache if Valkey connection fails
			cacheInstance = cache.NewMemoryCache()
			cacheType = "memory"
		}
	} else {
		cacheInstance = cache.NewMemoryCache()
		cacheType = "memory"
	}

	// Initialize repository factory
	repoFactory := repositories.NewRepositoryFactory(db)

	// Initialize delegate for inter-domain communication
	delegateInstance := delegate.New(logger)

	// Initialize crypto service
	cryptoService := crypto.NewCryptoService()

	// Initialize license validation service
	licenseValidationService := license.NewValidationService(
		repoFactory.License(),
		repoFactory.Account(),
		repoFactory.Machine(),
		repoFactory.Policy(),
		repoFactory.User(),
		cryptoService,
		cacheInstance,
	)

	// Initialize event broadcasting service
	eventService := events.NewBroadcastService()

	coordinator := &ServiceCoordinator{
		Crypto:            cryptoService,
		LicenseValidation: licenseValidationService,
		Events:            eventService,
		Repositories:      repoFactory,
		Cache:             cacheInstance,
		cacheType:         cacheType,
		Delegate:          delegateInstance,
	}

	// Register delegate functions
	coordinator.registerDelegateFunctions()

	return coordinator
}

// registerDelegateFunctions registers all delegate functions to avoid import cycles
func (sc *ServiceCoordinator) registerDelegateFunctions() {
	// License domain functions
	sc.Delegate.Register(delegate.LicenseDomain, delegate.LicenseValidateAction, sc.handleLicenseValidation)
	sc.Delegate.Register(delegate.LicenseDomain, delegate.LicenseGetInfoAction, sc.handleLicenseGetInfo)
	sc.Delegate.Register(delegate.LicenseDomain, delegate.LicenseInvalidateAction, sc.handleLicenseInvalidate)

	// Cache domain functions
	sc.Delegate.Register(delegate.CacheDomain, delegate.CacheGetAction, sc.handleCacheGet)
	sc.Delegate.Register(delegate.CacheDomain, delegate.CacheSetAction, sc.handleCacheSet)
	sc.Delegate.Register(delegate.CacheDomain, delegate.CacheDeleteAction, sc.handleCacheDelete)

	// Machine domain functions
	sc.Delegate.Register(delegate.MachineDomain, delegate.MachineRegisterAction, sc.handleMachineRegister)
	sc.Delegate.Register(delegate.MachineDomain, delegate.MachineUpdateLastSeenAction, sc.handleMachineUpdateLastSeen)

	// Crypto domain functions
	sc.Delegate.Register(delegate.CryptoDomain, delegate.CryptoSignAction, sc.handleCryptoSign)
	sc.Delegate.Register(delegate.CryptoDomain, delegate.CryptoVerifyAction, sc.handleCryptoVerify)
	sc.Delegate.Register(delegate.CryptoDomain, delegate.CryptoEncryptAction, sc.handleCryptoEncrypt)
	sc.Delegate.Register(delegate.CryptoDomain, delegate.CryptoDecryptAction, sc.handleCryptoDecrypt)
}

// GetCryptoService returns the cryptographic service
func (sc *ServiceCoordinator) GetCryptoService() *crypto.CryptoService {
	return sc.Crypto
}

// GetLicenseValidationService returns the license validation service
func (sc *ServiceCoordinator) GetLicenseValidationService() *license.ValidationService {
	return sc.LicenseValidation
}

// GetRepositoryFactory returns the repository factory
func (sc *ServiceCoordinator) GetRepositoryFactory() *repositories.RepositoryFactory {
	return sc.Repositories
}

// GetCache returns the cache instance
func (sc *ServiceCoordinator) GetCache() license.CacheInterface {
	return sc.Cache
}

// GetCacheType returns the type of cache being used
func (sc *ServiceCoordinator) GetCacheType() string {
	return sc.cacheType
}

// Health checks the health of all services
func (sc *ServiceCoordinator) Health() map[string]interface{} {
	health := make(map[string]interface{})

	// Check crypto service
	schemes := sc.Crypto.GetSupportedSchemes()
	health["crypto"] = map[string]interface{}{
		"status":            "healthy",
		"supported_schemes": schemes,
	}

	// Check cache
	health["cache"] = map[string]interface{}{
		"status": "healthy",
		"type":   sc.cacheType,
	}

	// Add cache stats if available
	if memCache, ok := sc.Cache.(*cache.MemoryCache); ok {
		health["cache"].(map[string]interface{})["stats"] = memCache.GetStats()
	} else if valkeyCache, ok := sc.Cache.(*cache.ValkeyCache); ok {
		health["cache"].(map[string]interface{})["stats"] = valkeyCache.GetStats()
	}

	// Check database connection
	db := sc.Repositories.GetDB()
	if sqlDB, err := db.DB(); err == nil {
		if err := sqlDB.Ping(); err == nil {
			health["database"] = map[string]interface{}{
				"status": "healthy",
			}
		} else {
			health["database"] = map[string]interface{}{
				"status": "unhealthy",
				"error":  err.Error(),
			}
		}
	} else {
		health["database"] = map[string]interface{}{
			"status": "unhealthy",
			"error":  err.Error(),
		}
	}

	return health
}

// Cleanup performs cleanup operations for all services
func (sc *ServiceCoordinator) Cleanup() {
	// Clear cache based on type
	if memCache, ok := sc.Cache.(*cache.MemoryCache); ok {
		memCache.Clear()
	} else if valkeyCache, ok := sc.Cache.(*cache.ValkeyCache); ok {
		// For Valkey, we might want to close the connection instead of clearing
		valkeyCache.Close()
	}

	// Close database connections would be handled by the database service
}

// GetServiceInfo returns information about all services
func (sc *ServiceCoordinator) GetServiceInfo() map[string]interface{} {
	return map[string]interface{}{
		"services": []string{
			"crypto",
			"license_validation",
			"repositories",
			"cache",
		},
		"crypto_schemes": sc.Crypto.GetSupportedSchemes(),
		"cache_type":     sc.cacheType,
		"repository_count": map[string]int{
			"account":           1,
			"product":           1,
			"policy":            1,
			"license":           1,
			"machine":           1,
			"user":              1,
			"token":             1,
			"session":           1,
			"environment":       1,
			"group":             1,
			"entitlement":       1,
			"role":              1,
			"permission":        1,
			"plan":              1,
			"machine_component": 1,
			"machine_process":   1,
			"second_factor":     1,
		},
	}
}

// Helper functions for environment variables
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// Delegate handler functions

// handleLicenseValidation handles license validation delegate calls
func (sc *ServiceCoordinator) handleLicenseValidation(ctx context.Context, data delegate.Data) error {
	var params struct {
		LicenseKey         string `json:"license_key"`
		MachineFingerprint string `json:"machine_fingerprint"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Call the actual license validation service
	machineFingerprint := &params.MachineFingerprint
	environment := ""
	envPtr := &environment
	_, err := sc.LicenseValidation.ValidateLicense(ctx, params.LicenseKey, machineFingerprint, envPtr)
	return err
}

// handleLicenseGetInfo handles license info retrieval delegate calls
func (sc *ServiceCoordinator) handleLicenseGetInfo(ctx context.Context, data delegate.Data) error {
	var params struct {
		LicenseKey string `json:"license_key"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Get license info (implementation depends on your license service)
	// This is a placeholder - you would implement actual license info retrieval
	return nil
}

// handleLicenseInvalidate handles license cache invalidation delegate calls
func (sc *ServiceCoordinator) handleLicenseInvalidate(ctx context.Context, data delegate.Data) error {
	var params struct {
		LicenseKey string `json:"license_key"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Invalidate license cache
	cacheKey := "license:" + params.LicenseKey
	sc.Cache.Delete(ctx, cacheKey)
	return nil
}

// handleCacheGet handles cache retrieval delegate calls
func (sc *ServiceCoordinator) handleCacheGet(ctx context.Context, data delegate.Data) error {
	var params struct {
		Key string `json:"key"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	_, _ = sc.Cache.Get(ctx, params.Key)
	return nil
}

// handleCacheSet handles cache storage delegate calls
func (sc *ServiceCoordinator) handleCacheSet(ctx context.Context, data delegate.Data) error {
	var params struct {
		Key   string      `json:"key"`
		Value interface{} `json:"value"`
		TTL   int         `json:"ttl,omitempty"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	var ttl time.Duration
	if params.TTL > 0 {
		ttl = time.Duration(params.TTL) * time.Second
	} else {
		ttl = 3600 * time.Second // Default 1 hour
	}
	return sc.Cache.Set(ctx, params.Key, params.Value, ttl)
}

// handleCacheDelete handles cache deletion delegate calls
func (sc *ServiceCoordinator) handleCacheDelete(ctx context.Context, data delegate.Data) error {
	var params struct {
		Key string `json:"key"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	sc.Cache.Delete(ctx, params.Key)
	return nil
}

// handleMachineRegister handles machine registration delegate calls
func (sc *ServiceCoordinator) handleMachineRegister(ctx context.Context, data delegate.Data) error {
	var params struct {
		MachineID   string `json:"machine_id"`
		Fingerprint string `json:"fingerprint"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Machine registration logic would go here
	// This is a placeholder - implement actual machine registration
	return nil
}

// handleMachineUpdateLastSeen handles machine last seen update delegate calls
func (sc *ServiceCoordinator) handleMachineUpdateLastSeen(ctx context.Context, data delegate.Data) error {
	var params struct {
		MachineID string `json:"machine_id"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Update machine last seen logic would go here
	return nil
}

// handleCryptoSign handles cryptographic signing delegate calls
func (sc *ServiceCoordinator) handleCryptoSign(ctx context.Context, data delegate.Data) error {
	var params struct {
		Data      []byte `json:"data"`
		Algorithm string `json:"algorithm"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Placeholder for crypto signing - implement based on your crypto service
	// Example: Call appropriate crypto method based on algorithm
	return nil
}

// handleCryptoVerify handles cryptographic verification delegate calls
func (sc *ServiceCoordinator) handleCryptoVerify(ctx context.Context, data delegate.Data) error {
	var params struct {
		Data      []byte `json:"data"`
		Signature []byte `json:"signature"`
		Algorithm string `json:"algorithm"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Placeholder for crypto verification - implement based on your crypto service
	return nil
}

// handleCryptoEncrypt handles cryptographic encryption delegate calls
func (sc *ServiceCoordinator) handleCryptoEncrypt(ctx context.Context, data delegate.Data) error {
	var params struct {
		Data      []byte `json:"data"`
		Algorithm string `json:"algorithm"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Placeholder for crypto encryption - implement based on your crypto service
	return nil
}

// handleCryptoDecrypt handles cryptographic decryption delegate calls
func (sc *ServiceCoordinator) handleCryptoDecrypt(ctx context.Context, data delegate.Data) error {
	var params struct {
		Data      []byte `json:"data"`
		Algorithm string `json:"algorithm"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Placeholder for crypto decryption - implement based on your crypto service
	return nil
}
