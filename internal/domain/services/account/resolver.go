package account

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"github.com/google/uuid"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// ResolverService handles account resolution (maps to Ruby ResolveAccountService)
type ResolverService struct {
	accountRepo repositories.AccountRepository
	cache       CacheInterface
	cacheTTL    time.Duration
}

// CacheInterface defines the caching interface for account resolution
type CacheInterface interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// NewResolverService creates a new account resolver service
func NewResolverService(
	accountRepo repositories.AccountRepository,
	cache CacheInterface,
) *ResolverService {
	return &ResolverService{
		accountRepo: accountRepo,
		cache:       cache,
		cacheTTL:    15 * time.Minute, // Maps to Ruby ACCOUNT_CACHE_TTL
	}
}

// RequestContext represents the request context for account resolution
type RequestContext struct {
	AccountID string `json:"account_id,omitempty"`
	Host      string `json:"host,omitempty"`
	// Add other request fields as needed
}

// ResolveOptions represents resolution configuration
type ResolveOptions struct {
	Singleplayer bool   `json:"singleplayer"`
	Multiplayer  bool   `json:"multiplayer"`
	RequiredID   string `json:"required_id,omitempty"` // For singleplayer validation
}

// Internal domain pattern for filtering (maps to Ruby ACCOUNT_INTERNAL_DOMAIN_RE)
var InternalDomainPattern = regexp.MustCompile(`\.keygen\.sh$|\.localhost$`)

// ResolveAccount resolves an account from request context (maps to Ruby ResolveAccountService.call!)
func (rs *ResolverService) ResolveAccount(ctx context.Context, request RequestContext, options ResolveOptions) (*entities.Account, error) {
	switch {
	case options.Singleplayer:
		return rs.resolveSingleplayerAccount(ctx, request, options)
	case options.Multiplayer:
		return rs.resolveMultiplayerAccount(ctx, request)
	default:
		return nil, fmt.Errorf("must specify singleplayer or multiplayer mode")
	}
}

// ResolveAccountSafe resolves an account without throwing errors (maps to Ruby ResolveAccountService.call)
func (rs *ResolverService) ResolveAccountSafe(ctx context.Context, request RequestContext, options ResolveOptions) (*entities.Account, error) {
	account, err := rs.ResolveAccount(ctx, request, options)
	if err != nil {
		// Return nil instead of error for safe resolution
		return nil, nil
	}
	return account, nil
}

// resolveSingleplayerAccount handles singleplayer account resolution
func (rs *ResolverService) resolveSingleplayerAccount(ctx context.Context, request RequestContext, options ResolveOptions) (*entities.Account, error) {
	// Get account ID from request or environment
	accountID := request.AccountID
	if accountID == "" && options.RequiredID != "" {
		accountID = options.RequiredID
	}
	
	if accountID == "" {
		return nil, fmt.Errorf("account is required")
	}
	
	// Find account by ID
	account, err := rs.findByAccountID(ctx, accountID)
	if err != nil {
		return nil, fmt.Errorf("account not found: %w", err)
	}
	
	// Validate account ID matches required ID in singleplayer mode
	if options.RequiredID != "" && account.ID != options.RequiredID {
		return nil, fmt.Errorf("account is invalid (expected %s)", options.RequiredID)
	}
	
	return account, nil
}

// resolveMultiplayerAccount handles multiplayer account resolution
func (rs *ResolverService) resolveMultiplayerAccount(ctx context.Context, request RequestContext) (*entities.Account, error) {
	// Try to resolve by custom domain first, then fall back to account ID
	if request.Host != "" {
		if account, err := rs.findByAccountCNAME(ctx, request.Host); err == nil && account != nil {
			return account, nil
		}
	}
	
	// Fall back to account ID resolution
	if request.AccountID != "" {
		return rs.findByAccountID(ctx, request.AccountID)
	}
	
	return nil, fmt.Errorf("account not found")
}

// findByAccountCNAME finds account by custom domain (maps to Ruby find_by_account_cname!)
func (rs *ResolverService) findByAccountCNAME(ctx context.Context, domain string) (*entities.Account, error) {
	if domain == "" {
		return nil, fmt.Errorf("domain is required")
	}
	
	// Check if domain is internal (should be rejected)
	if InternalDomainPattern.MatchString(domain) {
		return nil, fmt.Errorf("domain is internal")
	}
	
	// Try cache first
	cacheKey := fmt.Sprintf("account:cname:%s", domain)
	if rs.cache != nil {
		if exists, err := rs.cache.Exists(ctx, cacheKey); err == nil && exists {
			if cachedID, err := rs.cache.Get(ctx, cacheKey); err == nil {
				if accountUUID, err := uuid.Parse(cachedID); err == nil {
					if account, err := rs.accountRepo.GetByID(ctx, accountUUID); err == nil {
						return account, nil
					}
				}
			}
		}
	}
	
	// Find by alias (domain or cname field)
	account, err := rs.findByAlias(ctx, domain, []string{"cname", "domain"})
	if err != nil {
		return nil, err
	}
	
	// Cache the result
	if rs.cache != nil {
		rs.cache.Set(ctx, cacheKey, account.ID, rs.cacheTTL)
	}
	
	return account, nil
}

// findByAccountID finds account by ID with caching (maps to Ruby find_by_account_id!)
func (rs *ResolverService) findByAccountID(ctx context.Context, id string) (*entities.Account, error) {
	if id == "" {
		return nil, fmt.Errorf("account ID is required")
	}
	
	// Try cache first
	cacheKey := fmt.Sprintf("account:id:%s", id)
	if rs.cache != nil {
		if exists, err := rs.cache.Exists(ctx, cacheKey); err == nil && exists {
			if cachedID, err := rs.cache.Get(ctx, cacheKey); err == nil {
				if accountUUID, err := uuid.Parse(cachedID); err == nil {
					if account, err := rs.accountRepo.GetByID(ctx, accountUUID); err == nil {
						return account, nil
					}
				}
			}
		}
	}
	
	// Find by alias (ID or slug field)
	account, err := rs.findByAlias(ctx, id, []string{"slug"})
	if err != nil {
		return nil, err
	}
	
	// Cache the result
	if rs.cache != nil {
		rs.cache.Set(ctx, cacheKey, account.ID, rs.cacheTTL)
	}
	
	return account, nil
}

// findByAlias finds account by ID or alias fields (maps to Ruby FindByAliasService.call)
func (rs *ResolverService) findByAlias(ctx context.Context, id string, aliases []string) (*entities.Account, error) {
	if id == "" {
		return nil, fmt.Errorf("ID cannot be blank")
	}
	
	// Try direct ID lookup first if it looks like a UUID
	if isValidUUID(id) {
		accountUUID, err := uuid.Parse(id)
		if err == nil {
			if account, err := rs.accountRepo.GetByID(ctx, accountUUID); err == nil {
				return account, nil
			}
		}
	}
	
	// Try alias lookups
	for _, alias := range aliases {
		switch alias {
		case "slug":
			if account, err := rs.accountRepo.GetBySlug(ctx, id); err == nil {
				return account, nil
			}
		case "cname", "domain":
			// TODO: Implement GetByCNAME or GetByDomain in repository
			// if account, err := rs.accountRepo.GetByCNAME(ctx, id); err == nil {
			//     return account, nil
			// }
		}
	}
	
	return nil, fmt.Errorf("account not found")
}

// isValidUUID checks if a string is a valid UUID format
func isValidUUID(s string) bool {
	uuidPattern := regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)
	return uuidPattern.MatchString(s)
}

// SetCacheTTL sets the cache time-to-live duration
func (rs *ResolverService) SetCacheTTL(ttl time.Duration) {
	rs.cacheTTL = ttl
}

// InvalidateAccountCache invalidates cache entries for an account
func (rs *ResolverService) InvalidateAccountCache(ctx context.Context, accountID string) error {
	if rs.cache == nil {
		return nil
	}
	
	// Delete various cache keys that might exist for this account
	cacheKeys := []string{
		fmt.Sprintf("account:id:%s", accountID),
		// TODO: Add other cache keys if we cache by slug, cname, etc.
	}
	
	for _, key := range cacheKeys {
		rs.cache.Delete(ctx, key)
	}
	
	return nil
}