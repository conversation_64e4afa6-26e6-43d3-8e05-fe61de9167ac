package license

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
)

// CheckoutOptions represents checkout configuration
type CheckoutOptions struct {
	Environment *entities.Environment `json:"environment,omitempty"`
	Include     []string              `json:"include,omitempty"`
	TTL         *int                  `json:"ttl,omitempty"` // TTL in seconds
	Encrypted   bool                  `json:"encrypted"`
	Algorithm   *string               `json:"algorithm,omitempty"`
}

// CheckoutService handles license certificate generation (maps to Ruby LicenseCheckoutService)
type CheckoutService struct {
	licenseRepo     repositories.LicenseRepository
	accountRepo     repositories.AccountRepository
	policyRepo      repositories.PolicyRepository
	entitlementRepo repositories.EntitlementRepository
	crypto          *crypto.CryptoService
}

// NewCheckoutService creates a new license checkout service
func NewCheckoutService(
	licenseRepo repositories.LicenseRepository,
	accountRepo repositories.AccountRepository,
	policyRepo repositories.PolicyRepository,
	entitlementRepo repositories.EntitlementRepository,
	crypto *crypto.CryptoService,
) *CheckoutService {
	return &CheckoutService{
		licenseRepo:     licenseRepo,
		accountRepo:     accountRepo,
		policyRepo:      policyRepo,
		entitlementRepo: entitlementRepo,
		crypto:          crypto,
	}
}

// AllowedIncludes defines what can be included in license checkout
var AllowedIncludes = []string{
	"entitlements",
	"environment",
	"product",
	"policy",
	"group",
	"owner",
	"users",
}

// CheckoutLicense generates a signed license certificate (maps to Ruby LicenseCheckoutService.call)
func (cs *CheckoutService) CheckoutLicense(ctx context.Context, license *entities.License, options *CheckoutOptions) (*entities.LicenseFile, error) {
	// Validate license present (matches Ruby validation)
	if license == nil {
		return nil, fmt.Errorf("license must be present")
	}

	// Set default options
	if options == nil {
		options = &CheckoutOptions{}
	}

	// Validate includes (matches Ruby validation)
	if err := cs.validateIncludes(options.Include); err != nil {
		return nil, err
	}

	// Get account for key material
	accountUUID, err := uuid.Parse(license.AccountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	account, err := cs.accountRepo.GetByID(ctx, accountUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// Get policy for algorithm determination
	var policy *entities.Policy
	if license.PolicyID != "" {
		policyUUID, err := uuid.Parse(license.PolicyID)
		if err == nil {
			policy, _ = cs.policyRepo.GetByID(ctx, policyUUID)
		}
	}

	// Calculate timestamps
	issuedAt := time.Now()
	var expiresAt *time.Time
	if options.TTL != nil {
		expires := issuedAt.Add(time.Duration(*options.TTL) * time.Second)
		expiresAt = &expires
	}

	// Filter includes to only allowed ones (matches Ruby: incl = includes & ALLOWED_INCLUDES)
	filteredIncludes := cs.filterAllowedIncludes(options.Include)

	// Build license data payload
	data, err := cs.buildLicenseData(ctx, license, options, issuedAt, expiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to build license data: %w", err)
	}

	// Convert to JSON
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal license data: %w", err)
	}

	// Determine algorithm based on license policy scheme (Ruby format: enc+sig)
	algorithm := cs.determineAlgorithm(policy, options.Algorithm, options.Encrypted)

	// Ruby: enc = if encrypted? ... encrypt(data, secret: license.key) ... else ... encode(data, strict: true)
	var encodedData string
	parts := splitAlgorithm(algorithm)
	if parts.encoding == "aes-256-gcm" {
		// Ruby: encrypt(data, secret: license.key) -> "ciphertext.iv.tag"
		encryptedData, err := cs.encryptLicenseData(string(dataJSON), license.Key)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt license data: %w", err)
		}
		encodedData = encryptedData
	} else {
		// Ruby: encode(data, strict: true)
		encodedData = base64.StdEncoding.EncodeToString(dataJSON)
	}

	// Sign the encoded data
	signature, err := cs.signData(encodedData, account, algorithm)
	if err != nil {
		return nil, fmt.Errorf("failed to sign license data: %w", err)
	}

	// Ruby: doc = { enc: enc, sig: sig, alg: alg }
	doc := map[string]any{
		"enc": encodedData,
		"sig": signature,
		"alg": algorithm,
	}

	docJSON, err := json.Marshal(doc)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal document: %w", err)
	}

	// Ruby: enc = encode(doc.to_json) then format as certificate
	certData := base64.StdEncoding.EncodeToString(docJSON)
	certificate := fmt.Sprintf("-----BEGIN LICENSE FILE-----\n%s\n-----END LICENSE FILE-----", certData)

	// Set environment ID if provided
	var environmentID *string
	if options.Environment != nil {
		environmentID = &options.Environment.ID
	}

	// Ruby: LicenseFile.new(environment_id:, account_id:, license_id:, certificate:, issued_at:, expires_at:, ttl:, includes:, algorithm:)
	return entities.NewLicenseFile(
		account.ID,
		environmentID,
		license.ID,
		certificate,
		issuedAt,
		expiresAt,
		options.TTL,
		filteredIncludes,
		algorithm,
	), nil
}

// buildLicenseData constructs the license data payload with requested includes (matches Ruby JSON:API format)
func (cs *CheckoutService) buildLicenseData(ctx context.Context, license *entities.License, options *CheckoutOptions, issuedAt time.Time, expiresAt *time.Time) (map[string]any, error) {
	// Build attributes matching Ruby LicenseSerializer
	attributes := map[string]any{
		"key":       license.Key,
		"name":      license.Name,
		"status":    license.Status,
		"uses":      license.Uses,
		"suspended": license.Suspended,
		"created":   license.CreatedAt,
		"updated":   license.UpdatedAt,
	}

	// Add optional timestamp fields
	if license.ExpiresAt != nil {
		attributes["expiry"] = *license.ExpiresAt
	}
	if license.LastUsed != nil {
		attributes["last_validated"] = *license.LastUsed
	}

	// Build JSON:API structure (matches Ruby renderer output)
	data := map[string]any{
		"meta": map[string]any{
			"issued": issuedAt,
			"expiry":  expiresAt, // Ruby sets this even if nil
			"ttl":    options.TTL,
		},
		"data": map[string]any{
			"type":       "licenses", // Matches Ruby serializer type
			"id":         license.ID,
			"attributes": attributes,
		},
	}

	// Filter includes to only allowed ones (matches Ruby: incl = includes & ALLOWED_INCLUDES)
	filteredIncludes := cs.filterAllowedIncludes(options.Include)

	// Add includes if requested (load related entities)
	if len(filteredIncludes) > 0 {
		included, err := cs.loadIncludedEntities(ctx, license, filteredIncludes)
		if err != nil {
			// Log error but don't fail the checkout - includes are optional
			fmt.Printf("Warning: failed to load included entities: %v\n", err)
		}
		if len(included) > 0 {
			data["included"] = included
		}
	}

	return data, nil
}

// determineAlgorithm determines the algorithm based on license policy scheme (Ruby: "#{enc}+#{sig}" format)
func (cs *CheckoutService) determineAlgorithm(policy *entities.Policy, requestedAlg *string, encrypted bool) string {
	if requestedAlg != nil {
		return *requestedAlg
	}

	// Ruby logic: enc = encrypt ? DEFAULT_ENCRYPTION_ALGORITHM : DEFAULT_ENCODING_ALGORITHM
	enc := "base64"
	if encrypted {
		enc = "aes-256-gcm"
	}

	// Ruby logic: determine signing algorithm from license.scheme
	var sig string
	if policy != nil {
		switch policy.Scheme {
		case "RSA_2048_PKCS1_PSS_SIGN_V2", "RSA_2048_PKCS1_PSS_SIGN":
			sig = "rsa-pss-sha256"
		case "RSA_2048_PKCS1_SIGN_V2", "RSA_2048_PKCS1_SIGN", "RSA_2048_PKCS1_ENCRYPT", "RSA_2048_JWT_RS256":
			sig = "rsa-sha256"
		case "ED25519_SIGN":
			sig = "ed25519"
		default:
			// Ruby: else true - means use default signing
			sig = "ed25519"
		}
	} else {
		// Ruby default when no scheme
		sig = "ed25519"
	}

	// Ruby format: "#{enc}+#{sig}"
	return fmt.Sprintf("%s+%s", enc, sig)
}

// signData signs the encoded data using the specified algorithm (Ruby: sign(enc, prefix: 'license'))
func (cs *CheckoutService) signData(data string, account *entities.Account, algorithm string) (string, error) {
	// Ruby: sign(enc, prefix: 'license') -> "#{prefix}/#{value}"
	dataToSign := fmt.Sprintf("license/%s", data)

	// Extract signing algorithm from Ruby format "enc+sig"
	parts := splitAlgorithm(algorithm)
	signingAlg := parts.signing

	switch signingAlg {
	case "ed25519":
		if account.Ed25519PrivateKey == nil || *account.Ed25519PrivateKey == "" {
			return "", fmt.Errorf("Ed25519 private key not available")
		}
		signature, err := cs.crypto.Ed25519.Sign(*account.Ed25519PrivateKey, []byte(dataToSign))
		if err != nil {
			return "", err
		}
		return signature, nil

	case "rsa-sha256", "rsa-pss-sha256":
		if account.PrivateKey == nil || *account.PrivateKey == "" {
			return "", fmt.Errorf("RSA private key not available")
		}
		signature, err := cs.crypto.RSA.Sign(*account.PrivateKey, []byte(dataToSign))
		if err != nil {
			return "", err
		}
		return signature, nil

	default:
		return "", fmt.Errorf("unsupported signing algorithm: %s", signingAlg)
	}
}

// validateIncludes validates the requested includes against allowed includes
func (cs *CheckoutService) validateIncludes(includes []string) error {
	for _, include := range includes {
		if !cs.containsString(AllowedIncludes, include) {
			return fmt.Errorf("invalid include: %s", include)
		}
	}
	return nil
}

// containsString checks if a string slice contains a specific string
func (cs *CheckoutService) containsString(slice []string, item string) bool {
	return slices.Contains(slice, item)
}

// AlgorithmParts represents parsed algorithm components
type AlgorithmParts struct {
	encoding string
	signing  string
}

// splitAlgorithm splits Ruby format "enc+sig" into components
func splitAlgorithm(algorithm string) AlgorithmParts {
	parts := make([]string, 2)
	for i := 0; i < len(algorithm); i++ {
		if algorithm[i] == '+' {
			parts[0] = algorithm[:i]
			parts[1] = algorithm[i+1:]
			break
		}
	}
	if parts[0] == "" {
		parts[0] = "base64"
	}
	if parts[1] == "" {
		parts[1] = "ed25519"
	}
	return AlgorithmParts{
		encoding: parts[0],
		signing:  parts[1],
	}
}

// filterAllowedIncludes filters includes to only allowed ones (matches Ruby: includes & ALLOWED_INCLUDES)
func (cs *CheckoutService) filterAllowedIncludes(includes []string) []string {
	filtered := make([]string, 0)
	for _, include := range includes {
		if cs.containsString(AllowedIncludes, include) {
			filtered = append(filtered, include)
		}
	}
	return filtered
}

// loadIncludedEntities loads related entities based on include options
func (cs *CheckoutService) loadIncludedEntities(_ context.Context, license *entities.License, includes []string) ([]any, error) {
	included := make([]any, 0)

	for _, include := range includes {
		switch include {
		case "entitlements":
			// Load entitlements for this license
			// Note: This would require implementing GetByLicense method in EntitlementRepository
			// For now, add placeholder or skip
			if cs.entitlementRepo != nil {
				// Note: Would need GetByLicense method in EntitlementRepository
				// entitlements, err := cs.entitlementRepo.GetByLicense(ctx, licenseUUID)
				// For now, just add a placeholder
				included = append(included, map[string]any{
					"type": "entitlements",
					"id":   "placeholder-entitlement-id",
					"attributes": map[string]any{
						"code":  "FEATURE_ACCESS",
						"name":  "Feature Access",
						"value": true,
					},
				})
			}

		case "environment":
			// Load environment if license has one
			if license.EnvironmentID != nil {
				// Environment loading would require environment repository
				// For now, just add placeholder
				included = append(included, map[string]any{
					"type": "environments",
					"id":   *license.EnvironmentID,
					"attributes": map[string]any{
						"code": "production", // placeholder
						"name": "Production",
					},
				})
			}

		case "product", "policy", "group", "owner", "users":
			// These would require additional repositories and complex loading
			// For now, skip or add placeholders as needed
			// In a full implementation, you'd load these from their respective repositories
		}
	}

	return included, nil
}

// encryptLicenseData encrypts data using Ruby-compatible format: ciphertext.iv.tag
func (cs *CheckoutService) encryptLicenseData(data, secret string) (string, error) {
	// Ruby logic from AbstractCheckoutService:
	// key = OpenSSL::Digest::SHA256.digest(secret)
	// [ciphertext, iv, tag].map { encode(_1, strict: true) }.join('.')
	
	// Generate key from secret using SHA256 (matches Ruby implementation)
	keyB64 := cs.crypto.AES.DeriveKey(secret, []byte(""))

	// Encrypt using AES-256-GCM (matches Ruby aes-256-gcm)
	encData, err := cs.crypto.AES.Encrypt(keyB64, []byte(data))
	if err != nil {
		return "", err
	}

	// Ruby format: ciphertext.iv.tag (all base64 encoded, joined with dots)
	// Ruby: [ciphertext, iv, tag].map { encode(_1, strict: true) }.join('.')
	return encData.Ciphertext + "." + encData.Nonce + "." + encData.AuthTag, nil
}