package webhook

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// Orchestrator coordinates all webhook services and provides a unified interface
type Orchestrator struct {
	endpointService     *EndpointService
	deliveryService     *DeliveryService
	queueService        *QueueService
	verificationService *VerificationService

	// Configuration
	config OrchestratorConfig
}

// OrchestratorConfig represents configuration for the webhook orchestrator
type OrchestratorConfig struct {
	Workers         int           `json:"workers"`
	BatchSize       int           `json:"batch_size"`
	PollInterval    time.Duration `json:"poll_interval"`
	MaxRetries      int           `json:"max_retries"`
	HTTPTimeout     time.Duration `json:"http_timeout"`
	ToleranceWindow time.Duration `json:"tolerance_window"`
	PurgeOlderThan  time.Duration `json:"purge_older_than"`
	AutoPurge       bool          `json:"auto_purge"`
}

// DefaultOrchestratorConfig returns default configuration
func DefaultOrchestratorConfig() OrchestratorConfig {
	return OrchestratorConfig{
		Workers:         5,
		BatchSize:       10,
		PollInterval:    5 * time.Second,
		MaxRetries:      5,
		HTTPTimeout:     30 * time.Second,
		ToleranceWindow: 5 * time.Minute,
		PurgeOlderThan:  7 * 24 * time.Hour, // 7 days
		AutoPurge:       true,
	}
}

// NewOrchestrator creates a new webhook orchestrator
func NewOrchestrator(
	webhookEndpointRepo repositories.WebhookEndpointRepository,
	webhookEventRepo repositories.WebhookEventRepository,
	accountRepo repositories.AccountRepository,
	environmentRepo repositories.EnvironmentRepository,
	config OrchestratorConfig,
) *Orchestrator {
	// Create services
	endpointService := NewEndpointService(webhookEndpointRepo, accountRepo, environmentRepo)
	deliveryService := NewDeliveryService(webhookEndpointRepo, webhookEventRepo)
	queueService := NewQueueService(webhookEventRepo, webhookEndpointRepo, deliveryService)
	verificationService := NewVerificationService()

	// Configure services
	deliveryService.SetMaxRetries(config.MaxRetries)
	deliveryService.SetHTTPTimeout(config.HTTPTimeout)

	queueService.SetWorkers(config.Workers)
	queueService.SetBatchSize(config.BatchSize)
	queueService.SetPollInterval(config.PollInterval)

	verificationService.SetToleranceWindow(config.ToleranceWindow)

	return &Orchestrator{
		endpointService:     endpointService,
		deliveryService:     deliveryService,
		queueService:        queueService,
		verificationService: verificationService,
		config:              config,
	}
}

// Start starts the webhook orchestrator
func (o *Orchestrator) Start() error {
	log.Println("Starting webhook orchestrator...")

	// Start queue service
	if err := o.queueService.Start(); err != nil {
		return fmt.Errorf("failed to start queue service: %w", err)
	}

	// Start auto-purge if enabled
	if o.config.AutoPurge {
		go o.autoPurgeWorker()
	}

	log.Println("Webhook orchestrator started successfully")
	return nil
}

// Stop stops the webhook orchestrator
func (o *Orchestrator) Stop() error {
	log.Println("Stopping webhook orchestrator...")

	// Stop queue service
	if err := o.queueService.Stop(); err != nil {
		return fmt.Errorf("failed to stop queue service: %w", err)
	}

	log.Println("Webhook orchestrator stopped successfully")
	return nil
}

// SendWebhook sends a webhook event (high-level interface)
func (o *Orchestrator) SendWebhook(ctx context.Context, event QueueEvent) error {
	return o.queueService.EnqueueEvent(ctx, event)
}

// SendWebhookSync sends a webhook synchronously (immediate delivery)
func (o *Orchestrator) SendWebhookSync(ctx context.Context, event QueueEvent) ([]*DeliveryResult, error) {
	// Prepare delivery options
	options := DeliveryOptions{
		Event:         event.Event,
		Payload:       event.Payload,
		AccountID:     event.AccountID,
		EnvironmentID: event.EnvironmentID,
	}

	// Get subscribed endpoints
	accountUUID, err := uuid.Parse(event.AccountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	endpoints, err := o.endpointService.webhookEndpointRepo.GetByAccount(ctx, accountUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get endpoints: %w", err)
	}

	// Filter by environment if specified
	if event.EnvironmentID != nil {
		var filtered []*entities.WebhookEndpoint
		for _, endpoint := range endpoints {
			if endpoint.EnvironmentID != nil && *endpoint.EnvironmentID == *event.EnvironmentID {
				filtered = append(filtered, endpoint)
			}
		}
		endpoints = filtered
	}

	// Filter subscribed endpoints
	var endpointIDs []string
	for _, endpoint := range endpoints {
		if endpoint.Enabled && o.endpointService.IsSubscribedToEvent(endpoint, event.Event) {
			endpointIDs = append(endpointIDs, endpoint.ID)
		}
	}

	options.EndpointIDs = endpointIDs

	// Deliver immediately
	return o.deliveryService.DeliverWebhooks(ctx, options)
}

// GetEndpointService returns the endpoint service
func (o *Orchestrator) GetEndpointService() *EndpointService {
	return o.endpointService
}

// GetDeliveryService returns the delivery service
func (o *Orchestrator) GetDeliveryService() *DeliveryService {
	return o.deliveryService
}

// GetQueueService returns the queue service
func (o *Orchestrator) GetQueueService() *QueueService {
	return o.queueService
}

// GetVerificationService returns the verification service
func (o *Orchestrator) GetVerificationService() *VerificationService {
	return o.verificationService
}

// GetStats returns comprehensive webhook statistics
func (o *Orchestrator) GetStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Queue stats
	queueStats, err := o.queueService.GetQueueStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get queue stats: %w", err)
	}
	stats["queue"] = queueStats

	// Configuration
	stats["config"] = o.config

	// Supported events
	stats["supported_events"] = o.endpointService.GetSupportedEvents()

	return stats, nil
}

// UpdateConfig updates the orchestrator configuration
func (o *Orchestrator) UpdateConfig(config OrchestratorConfig) {
	o.config = config

	// Update service configurations
	o.deliveryService.SetMaxRetries(config.MaxRetries)
	o.deliveryService.SetHTTPTimeout(config.HTTPTimeout)

	o.queueService.SetWorkers(config.Workers)
	o.queueService.SetBatchSize(config.BatchSize)
	o.queueService.SetPollInterval(config.PollInterval)

	o.verificationService.SetToleranceWindow(config.ToleranceWindow)
}

// autoPurgeWorker periodically purges old webhook events
func (o *Orchestrator) autoPurgeWorker() {
	ticker := time.NewTicker(24 * time.Hour) // Run daily
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ctx := context.Background()
			if err := o.queueService.PurgeOldEvents(ctx, o.config.PurgeOlderThan); err != nil {
				log.Printf("Auto-purge failed: %v", err)
			}
		}
	}
}

// HealthCheck performs a health check on all webhook services
func (o *Orchestrator) HealthCheck(ctx context.Context) map[string]interface{} {
	health := make(map[string]interface{})

	// Queue service health
	if stats, err := o.queueService.GetQueueStats(ctx); err == nil {
		health["queue_service"] = "healthy"
		health["queue_running"] = stats["running"]
	} else {
		health["queue_service"] = "unhealthy"
		health["queue_error"] = err.Error()
	}

	// Delivery service health (simple check)
	health["delivery_service"] = "healthy"

	// Endpoint service health (simple check)
	health["endpoint_service"] = "healthy"

	// Verification service health (simple check)
	health["verification_service"] = "healthy"

	health["timestamp"] = time.Now()

	return health
}

// BroadcastEvent is a convenience method that integrates with the event broadcasting service
func (o *Orchestrator) BroadcastEvent(ctx context.Context, eventName string, accountID string, environmentID *string, resourceType, resourceID string, payload map[string]interface{}, metadata map[string]interface{}) error {
	event := QueueEvent{
		Event:         eventName,
		AccountID:     accountID,
		EnvironmentID: environmentID,
		ResourceType:  resourceType,
		ResourceID:    resourceID,
		Payload:       payload,
		Metadata:      metadata,
		Priority:      1, // Default priority
	}

	return o.SendWebhook(ctx, event)
}
