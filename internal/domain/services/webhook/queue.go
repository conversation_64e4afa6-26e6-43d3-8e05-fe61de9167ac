package webhook

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// QueueService handles webhook event queuing and processing
type QueueService struct {
	webhookEventRepo    repositories.WebhookEventRepository
	webhookEndpointRepo repositories.WebhookEndpointRepository
	deliveryService     *DeliveryService
	workers             int
	batchSize           int
	pollInterval        time.Duration
	stopChan            chan struct{}
	wg                  sync.WaitGroup
	mu                  sync.RWMutex
	running             bool
}

// NewQueueService creates a new webhook queue service
func NewQueueService(
	webhookEventRepo repositories.WebhookEventRepository,
	webhookEndpointRepo repositories.WebhookEndpointRepository,
	deliveryService *DeliveryService,
) *QueueService {
	return &QueueService{
		webhookEventRepo:    webhookEventRepo,
		webhookEndpointRepo: webhookEndpointRepo,
		deliveryService:     deliveryService,
		workers:             5,               // Default 5 workers
		batchSize:           10,              // Process 10 events per batch
		pollInterval:        5 * time.Second, // Poll every 5 seconds
		stopChan:            make(chan struct{}),
	}
}

// QueueEvent represents an event to be queued for webhook delivery
type QueueEvent struct {
	ID            string                 `json:"id"`
	Event         string                 `json:"event"`
	AccountID     string                 `json:"account_id"`
	EnvironmentID *string                `json:"environment_id,omitempty"`
	ResourceType  string                 `json:"resource_type"`
	ResourceID    string                 `json:"resource_id"`
	Payload       map[string]interface{} `json:"payload"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	ScheduledAt   *time.Time             `json:"scheduled_at,omitempty"`
	Priority      int                    `json:"priority"` // Higher number = higher priority
}

// EnqueueEvent adds an event to the webhook queue
func (qs *QueueService) EnqueueEvent(ctx context.Context, event QueueEvent) error {
	// Set default ID if not provided
	if event.ID == "" {
		event.ID = uuid.New().String()
	}

	// Set default scheduled time
	if event.ScheduledAt == nil {
		now := time.Now()
		event.ScheduledAt = &now
	}

	// Find matching webhook endpoints
	accountUUID, err := uuid.Parse(event.AccountID)
	if err != nil {
		return fmt.Errorf("invalid account ID: %w", err)
	}

	endpoints, err := qs.webhookEndpointRepo.GetByAccount(ctx, accountUUID)
	if err != nil {
		return fmt.Errorf("failed to get webhook endpoints: %w", err)
	}

	// Filter by environment if specified
	if event.EnvironmentID != nil {
		var filtered []*entities.WebhookEndpoint
		for _, endpoint := range endpoints {
			if endpoint.EnvironmentID != nil && *endpoint.EnvironmentID == *event.EnvironmentID {
				filtered = append(filtered, endpoint)
			}
		}
		endpoints = filtered
	}

	// Filter endpoints that are subscribed to this event
	var subscribedEndpoints []*entities.WebhookEndpoint
	endpointService := &EndpointService{} // Temporary instance for method access

	for _, endpoint := range endpoints {
		if endpoint.Enabled && endpointService.IsSubscribedToEvent(endpoint, event.Event) {
			subscribedEndpoints = append(subscribedEndpoints, endpoint)
		}
	}

	if len(subscribedEndpoints) == 0 {
		// No endpoints subscribed to this event
		return nil
	}

	// Create webhook events for each endpoint
	for _, endpoint := range subscribedEndpoints {
		webhookEvent := &entities.WebhookEvent{
			ID:                uuid.New().String(),
			WebhookEndpointID: endpoint.ID,
			AccountID:         event.AccountID,
			EnvironmentID:     event.EnvironmentID,
			Event:             event.Event,
			Data:              event.Payload,
			Payload:           event.Payload,
			Status:            entities.WebhookEventStatusQueued,
			AttemptCount:      0,
			Attempts:          0,
			MaxAttempts:       3, // Default
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
		}

		// Payload is already set above as map[string]interface{}
		// Metadata is also already set as map[string]interface{} in entity
		if len(event.Metadata) > 0 {
			webhookEvent.Metadata = event.Metadata
		}

		// Save to database
		if err := qs.webhookEventRepo.Create(ctx, webhookEvent); err != nil {
			log.Printf("Failed to create webhook event: %v", err)
		}
	}

	return nil
}

// Start starts the webhook queue workers
func (qs *QueueService) Start() error {
	qs.mu.Lock()
	defer qs.mu.Unlock()

	if qs.running {
		return fmt.Errorf("queue service is already running")
	}

	qs.running = true
	qs.stopChan = make(chan struct{})

	// Start worker goroutines
	for i := 0; i < qs.workers; i++ {
		qs.wg.Add(1)
		go qs.worker(i)
	}

	log.Printf("Started webhook queue service with %d workers", qs.workers)
	return nil
}

// Stop stops the webhook queue workers
func (qs *QueueService) Stop() error {
	qs.mu.Lock()
	defer qs.mu.Unlock()

	if !qs.running {
		return fmt.Errorf("queue service is not running")
	}

	close(qs.stopChan)
	qs.wg.Wait()
	qs.running = false

	log.Println("Stopped webhook queue service")
	return nil
}

// worker processes webhook events from the queue
func (qs *QueueService) worker(workerID int) {
	defer qs.wg.Done()

	log.Printf("Worker %d started", workerID)
	defer log.Printf("Worker %d stopped", workerID)

	ticker := time.NewTicker(qs.pollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-qs.stopChan:
			return
		case <-ticker.C:
			qs.processEvents(workerID)
		}
	}
}

// processEvents processes a batch of queued webhook events
func (qs *QueueService) processEvents(workerID int) {
	ctx := context.Background()

	// Get queued events
	events, err := qs.webhookEventRepo.GetQueuedEvents(ctx, qs.batchSize)
	if err != nil {
		log.Printf("Worker %d: Failed to get queued events: %v", workerID, err)
		return
	}

	if len(events) == 0 {
		return // No events to process
	}

	log.Printf("Worker %d: Processing %d events", workerID, len(events))

	for _, event := range events {
		qs.processEvent(ctx, workerID, *event)
	}
}

// processEvent processes a single webhook event
func (qs *QueueService) processEvent(ctx context.Context, workerID int, event entities.WebhookEvent) {
	// Get webhook endpoint
	endpointUUID, err := uuid.Parse(event.WebhookEndpointID)
	if err != nil {
		log.Printf("Worker %d: Invalid endpoint ID for event %s: %v", workerID, event.ID, err)
		return
	}

	endpoint, err := qs.webhookEndpointRepo.GetByID(ctx, endpointUUID)
	if err != nil {
		log.Printf("Worker %d: Failed to get endpoint for event %s: %v", workerID, event.ID, err)

		// Mark event as failed if endpoint not found
		event.Status = entities.WebhookEventStatusFailed
		event.UpdatedAt = time.Now()
		qs.webhookEventRepo.Update(ctx, &event)
		return
	}

	// Skip if endpoint is disabled
	if !endpoint.Enabled {
		log.Printf("Worker %d: Skipping disabled endpoint for event %s", workerID, event.ID)

		// Mark event as skipped
		event.Status = entities.WebhookEventStatusCancelled
		event.UpdatedAt = time.Now()
		qs.webhookEventRepo.Update(ctx, &event)
		return
	}

	// Update event status to processing
	event.Status = entities.WebhookEventStatusDelivering
	event.UpdatedAt = time.Now()
	qs.webhookEventRepo.Update(ctx, &event)

	// Use payload directly (it's already map[string]interface{})
	payload := event.Payload
	if payload == nil {
		payload = make(map[string]interface{})
	}

	// Prepare delivery options
	options := DeliveryOptions{
		Event:         event.Event,
		EndpointIDs:   []string{endpoint.ID},
		Payload:       payload,
		AccountID:     event.AccountID,
		EnvironmentID: event.EnvironmentID,
	}

	// Attempt delivery
	log.Printf("Worker %d: Delivering event %s to endpoint %s", workerID, event.ID, endpoint.URL)

	results, err := qs.deliveryService.DeliverWebhooks(ctx, options)
	if err != nil {
		log.Printf("Worker %d: Delivery failed for event %s: %v", workerID, event.ID, err)

		// Mark event as failed
		event.Status = entities.WebhookEventStatusFailed
		event.UpdatedAt = time.Now()
		qs.webhookEventRepo.Update(ctx, &event)
		return
	}

	// Process delivery results
	if len(results) > 0 {
		result := results[0]

		if result.Success {
			log.Printf("Worker %d: Successfully delivered event %s (status: %d)",
				workerID, event.ID, result.StatusCode)
		} else {
			log.Printf("Worker %d: Failed to deliver event %s (status: %d, attempts: %d)",
				workerID, event.ID, result.StatusCode, result.Attempts)
		}
	}
}

// GetQueueStats returns statistics about the webhook queue
func (qs *QueueService) GetQueueStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Get event counts by status (placeholder implementation)
	statusCounts := make(map[string]int)
	statusCounts["pending"] = 0
	statusCounts["queued"] = 0
	statusCounts["delivering"] = 0
	statusCounts["delivered"] = 0
	statusCounts["failed"] = 0

	stats["events_by_status"] = statusCounts
	stats["workers"] = qs.workers
	stats["batch_size"] = qs.batchSize
	stats["poll_interval"] = qs.pollInterval.String()

	qs.mu.RLock()
	stats["running"] = qs.running
	qs.mu.RUnlock()

	return stats, nil
}

// SetWorkers sets the number of worker goroutines
func (qs *QueueService) SetWorkers(workers int) {
	if workers < 1 {
		workers = 1
	}
	qs.workers = workers
}

// SetBatchSize sets the batch size for processing events
func (qs *QueueService) SetBatchSize(batchSize int) {
	if batchSize < 1 {
		batchSize = 1
	}
	qs.batchSize = batchSize
}

// SetPollInterval sets the polling interval for workers
func (qs *QueueService) SetPollInterval(interval time.Duration) {
	if interval < time.Second {
		interval = time.Second
	}
	qs.pollInterval = interval
}

// PurgeOldEvents removes old webhook events to prevent database bloat
func (qs *QueueService) PurgeOldEvents(ctx context.Context, olderThan time.Duration) error {
	cutoffTime := time.Now().Add(-olderThan)

	// Purge old events (placeholder implementation)
	// In real implementation, this would delete events older than cutoffTime
	_ = cutoffTime
	count := int64(0) // Placeholder - no events purged

	if count > 0 {
		log.Printf("Purged %d old webhook events", count)
	}

	return nil
}
