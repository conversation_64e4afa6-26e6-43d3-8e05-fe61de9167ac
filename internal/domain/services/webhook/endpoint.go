package webhook

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// EndpointService handles webhook endpoint management
type EndpointService struct {
	webhookEndpointRepo repositories.WebhookEndpointRepository
	accountRepo         repositories.AccountRepository
	environmentRepo     repositories.EnvironmentRepository
}

// NewEndpointService creates a new webhook endpoint service
func NewEndpointService(
	webhookEndpointRepo repositories.WebhookEndpointRepository,
	accountRepo repositories.AccountRepository,
	environmentRepo repositories.EnvironmentRepository,
) *EndpointService {
	return &EndpointService{
		webhookEndpointRepo: webhookEndpointRepo,
		accountRepo:         accountRepo,
		environmentRepo:     environmentRepo,
	}
}

// CreateEndpointRequest represents the request to create a webhook endpoint
type CreateEndpointRequest struct {
	URL           string            `json:"url" validate:"required,url"`
	Name          string            `json:"name"`
	Description   string            `json:"description"`
	Events        []string          `json:"events" validate:"required,min=1"`
	Headers       map[string]string `json:"headers,omitempty"`
	SigningSecret string            `json:"signing_secret,omitempty"`
	Enabled       bool              `json:"enabled"`
	EnvironmentID *string           `json:"environment_id,omitempty"`
}

// UpdateEndpointRequest represents the request to update a webhook endpoint
type UpdateEndpointRequest struct {
	URL           *string           `json:"url,omitempty" validate:"omitempty,url"`
	Name          *string           `json:"name,omitempty"`
	Description   *string           `json:"description,omitempty"`
	Events        []string          `json:"events,omitempty"`
	Headers       map[string]string `json:"headers,omitempty"`
	SigningSecret *string           `json:"signing_secret,omitempty"`
	Enabled       *bool             `json:"enabled,omitempty"`
}

// Supported webhook events
var SupportedEvents = []string{
	"license.created",
	"license.updated",
	"license.deleted",
	"license.validation.succeeded",
	"license.validation.failed",
	"machine.created",
	"machine.updated",
	"machine.deleted",
	"machine.heartbeat.ping",
	"machine.heartbeat.pong",
	"user.created",
	"user.updated",
	"user.deleted",
	"account.updated",
	"policy.created",
	"policy.updated",
	"policy.deleted",
	"product.created",
	"product.updated",
	"product.deleted",
	"entitlement.created",
	"entitlement.updated",
	"entitlement.deleted",
}

// URL validation pattern
var urlPattern = regexp.MustCompile(`^https?://`)

// CreateEndpoint creates a new webhook endpoint
func (es *EndpointService) CreateEndpoint(ctx context.Context, accountID string, req CreateEndpointRequest) (*entities.WebhookEndpoint, error) {
	// Validate account
	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	_, err = es.accountRepo.GetByID(ctx, accountUUID)
	if err != nil {
		return nil, fmt.Errorf("account not found: %w", err)
	}

	// Validate URL
	if !urlPattern.MatchString(req.URL) {
		return nil, fmt.Errorf("URL must start with http:// or https://")
	}

	// Validate events
	if err := es.validateEvents(req.Events); err != nil {
		return nil, err
	}

	// Validate environment if specified
	var environmentID *string
	if req.EnvironmentID != nil {
		envUUID, err := uuid.Parse(*req.EnvironmentID)
		if err != nil {
			return nil, fmt.Errorf("invalid environment ID: %w", err)
		}

		environment, err := es.environmentRepo.GetByID(ctx, envUUID)
		if err != nil {
			return nil, fmt.Errorf("environment not found: %w", err)
		}

		// Verify environment belongs to account
		if environment.AccountID != accountID {
			return nil, fmt.Errorf("environment does not belong to account")
		}

		environmentID = req.EnvironmentID
	}

	// Create webhook endpoint
	endpoint := &entities.WebhookEndpoint{
		ID:            uuid.New().String(),
		AccountID:     accountID,
		EnvironmentID: environmentID,
		URL:           req.URL,
		Name:          req.Name,
		Description:   req.Description,
		Events:        req.Events,
		SigningSecret: req.SigningSecret,
		Enabled:       req.Enabled,
	}

	// Set headers if provided
	if len(req.Headers) > 0 {
		endpoint.Headers = req.Headers
	}

	// Save to database
	endpointUUID, _ := uuid.Parse(endpoint.ID)
	if err := es.webhookEndpointRepo.Create(ctx, endpoint); err != nil {
		return nil, fmt.Errorf("failed to create webhook endpoint: %w", err)
	}

	// Reload from database to get timestamps
	return es.webhookEndpointRepo.GetByID(ctx, endpointUUID)
}

// UpdateEndpoint updates an existing webhook endpoint
func (es *EndpointService) UpdateEndpoint(ctx context.Context, endpointID string, req UpdateEndpointRequest) (*entities.WebhookEndpoint, error) {
	// Get existing endpoint
	endpointUUID, err := uuid.Parse(endpointID)
	if err != nil {
		return nil, fmt.Errorf("invalid endpoint ID: %w", err)
	}

	endpoint, err := es.webhookEndpointRepo.GetByID(ctx, endpointUUID)
	if err != nil {
		return nil, fmt.Errorf("endpoint not found: %w", err)
	}

	// Update fields
	if req.URL != nil {
		if !urlPattern.MatchString(*req.URL) {
			return nil, fmt.Errorf("URL must start with http:// or https://")
		}
		endpoint.URL = *req.URL
	}

	if req.Name != nil {
		endpoint.Name = *req.Name
	}

	if req.Description != nil {
		endpoint.Description = *req.Description
	}

	if req.Events != nil {
		if err := es.validateEvents(req.Events); err != nil {
			return nil, err
		}
		endpoint.Events = req.Events
	}

	if req.Headers != nil {
		if len(req.Headers) > 0 {
			endpoint.Headers = req.Headers
		} else {
			endpoint.Headers = nil
		}
	}

	if req.SigningSecret != nil {
		endpoint.SigningSecret = *req.SigningSecret
	}

	if req.Enabled != nil {
		endpoint.Enabled = *req.Enabled
	}

	// Save changes
	if err := es.webhookEndpointRepo.Update(ctx, endpoint); err != nil {
		return nil, fmt.Errorf("failed to update webhook endpoint: %w", err)
	}

	return endpoint, nil
}

// DeleteEndpoint deletes a webhook endpoint
func (es *EndpointService) DeleteEndpoint(ctx context.Context, endpointID string) error {
	endpointUUID, err := uuid.Parse(endpointID)
	if err != nil {
		return fmt.Errorf("invalid endpoint ID: %w", err)
	}

	return es.webhookEndpointRepo.Delete(ctx, endpointUUID)
}

// GetEndpoint retrieves a webhook endpoint by ID
func (es *EndpointService) GetEndpoint(ctx context.Context, endpointID string) (*entities.WebhookEndpoint, error) {
	endpointUUID, err := uuid.Parse(endpointID)
	if err != nil {
		return nil, fmt.Errorf("invalid endpoint ID: %w", err)
	}

	return es.webhookEndpointRepo.GetByID(ctx, endpointUUID)
}

// ListEndpoints lists webhook endpoints for an account
func (es *EndpointService) ListEndpoints(ctx context.Context, accountID string, environmentID *string) ([]*entities.WebhookEndpoint, error) {
	accountUUID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Repository only takes accountID, filter by environment in service layer
	endpoints, err := es.webhookEndpointRepo.GetByAccount(ctx, accountUUID)
	if err != nil {
		return nil, err
	}

	// Filter by environment if specified
	if environmentID != nil {
		var filtered []*entities.WebhookEndpoint
		for _, endpoint := range endpoints {
			if endpoint.EnvironmentID != nil && *endpoint.EnvironmentID == *environmentID {
				filtered = append(filtered, endpoint)
			}
		}
		return filtered, nil
	}

	return endpoints, nil
}

// TestEndpoint tests a webhook endpoint by sending a test event
func (es *EndpointService) TestEndpoint(ctx context.Context, endpointID string) (*DeliveryResult, error) {
	// Get endpoint
	endpoint, err := es.GetEndpoint(ctx, endpointID)
	if err != nil {
		return nil, err
	}

	// Create delivery service for testing
	deliveryService := NewDeliveryService(es.webhookEndpointRepo, nil)

	// Create test payload
	testPayload := map[string]interface{}{
		"test":      true,
		"message":   "This is a test webhook from GoKeys",
		"timestamp": fmt.Sprintf("%d", **********),
	}

	options := DeliveryOptions{
		Event:         "test.event",
		EndpointIDs:   []string{endpointID},
		Payload:       testPayload,
		AccountID:     endpoint.AccountID,
		EnvironmentID: endpoint.EnvironmentID,
	}

	// Attempt delivery
	results, err := deliveryService.DeliverWebhooks(ctx, options)
	if err != nil {
		return nil, err
	}

	if len(results) > 0 {
		return results[0], nil
	}

	return nil, fmt.Errorf("no delivery result")
}

// IsSubscribedToEvent checks if endpoint is subscribed to a specific event
func (es *EndpointService) IsSubscribedToEvent(endpoint *entities.WebhookEndpoint, eventName string) bool {
	if len(endpoint.Events) == 0 {
		return false
	}

	events := endpoint.Events
	for _, event := range events {
		event = strings.TrimSpace(event)
		if event == eventName || event == "*" {
			return true
		}

		// Support wildcard patterns like "license.*"
		if strings.HasSuffix(event, "*") {
			prefix := strings.TrimSuffix(event, "*")
			if strings.HasPrefix(eventName, prefix) {
				return true
			}
		}
	}

	return false
}

// validateEvents validates that all events are supported
func (es *EndpointService) validateEvents(events []string) error {
	if len(events) == 0 {
		return fmt.Errorf("at least one event must be specified")
	}

	for _, event := range events {
		event = strings.TrimSpace(event)
		if event == "*" {
			continue // Wildcard is allowed
		}

		// Check for wildcard patterns
		if strings.HasSuffix(event, "*") {
			prefix := strings.TrimSuffix(event, "*")
			found := false
			for _, supported := range SupportedEvents {
				if strings.HasPrefix(supported, prefix) {
					found = true
					break
				}
			}
			if !found {
				return fmt.Errorf("unsupported event pattern: %s", event)
			}
			continue
		}

		// Check exact match
		found := false
		for _, supported := range SupportedEvents {
			if event == supported {
				found = true
				break
			}
		}

		if !found {
			return fmt.Errorf("unsupported event: %s", event)
		}
	}

	return nil
}

// encodeHeaders encodes headers map to JSON string
func (es *EndpointService) encodeHeaders(headers map[string]string) (string, error) {
	if len(headers) == 0 {
		return "", nil
	}

	// Validate headers
	for key, value := range headers {
		if key == "" || value == "" {
			return "", fmt.Errorf("header key and value cannot be empty")
		}

		// Prevent override of system headers
		lowerKey := strings.ToLower(key)
		if lowerKey == "content-type" || lowerKey == "user-agent" ||
			strings.HasPrefix(lowerKey, "x-webhook-") {
			return "", fmt.Errorf("cannot override system header: %s", key)
		}
	}

	headersBytes, err := json.Marshal(headers)
	if err != nil {
		return "", fmt.Errorf("failed to encode headers: %w", err)
	}

	return string(headersBytes), nil
}

// GetSupportedEvents returns the list of supported webhook events
func (es *EndpointService) GetSupportedEvents() []string {
	return SupportedEvents
}
