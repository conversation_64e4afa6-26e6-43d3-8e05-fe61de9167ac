package webhook

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// VerificationService handles webhook signature verification and security
type VerificationService struct {
	toleranceWindow time.Duration // Time window for timestamp verification
}

// NewVerificationService creates a new webhook verification service
func NewVerificationService() *VerificationService {
	return &VerificationService{
		toleranceWindow: 5 * time.Minute, // Default 5 minute tolerance
	}
}

// VerificationOptions represents options for webhook verification
type VerificationOptions struct {
	Payload       []byte `json:"payload"`
	Signature     string `json:"signature"`
	Timestamp     string `json:"timestamp"`
	Secret        string `json:"secret"`
	SkipTimestamp bool   `json:"skip_timestamp"`
}

// VerificationResult represents the result of webhook verification
type VerificationResult struct {
	Valid             bool   `json:"valid"`
	SignatureValid    bool   `json:"signature_valid"`
	TimestampValid    bool   `json:"timestamp_valid"`
	Error             string `json:"error,omitempty"`
	TimestampAge      *time.Duration `json:"timestamp_age,omitempty"`
	ExpectedSignature string `json:"expected_signature,omitempty"`
}

// VerifyWebhook verifies webhook signature and timestamp
func (vs *VerificationService) VerifyWebhook(options VerificationOptions) *VerificationResult {
	result := &VerificationResult{
		Valid: false,
	}
	
	// Verify signature
	expectedSignature := vs.generateSignature(options.Payload, options.Secret)
	result.ExpectedSignature = expectedSignature
	result.SignatureValid = vs.compareSignatures(options.Signature, expectedSignature)
	
	if !result.SignatureValid {
		result.Error = "Invalid webhook signature"
		return result
	}
	
	// Verify timestamp if provided and not skipped
	if !options.SkipTimestamp && options.Timestamp != "" {
		timestampValid, age, err := vs.verifyTimestamp(options.Timestamp)
		result.TimestampValid = timestampValid
		result.TimestampAge = age
		
		if err != nil {
			result.Error = fmt.Sprintf("Timestamp verification error: %v", err)
			return result
		}
		
		if !timestampValid {
			result.Error = "Webhook timestamp is too old"
			return result
		}
	} else {
		result.TimestampValid = true // Skip timestamp check
	}
	
	result.Valid = result.SignatureValid && result.TimestampValid
	return result
}

// generateSignature generates HMAC-SHA256 signature for webhook payload
func (vs *VerificationService) generateSignature(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	signature := hex.EncodeToString(h.Sum(nil))
	return fmt.Sprintf("sha256=%s", signature)
}

// compareSignatures securely compares two signatures to prevent timing attacks
func (vs *VerificationService) compareSignatures(received, expected string) bool {
	// Normalize both signatures to lowercase for comparison
	received = strings.ToLower(strings.TrimSpace(received))
	expected = strings.ToLower(strings.TrimSpace(expected))
	
	// Use constant-time comparison to prevent timing attacks
	return hmac.Equal([]byte(received), []byte(expected))
}

// verifyTimestamp verifies webhook timestamp is within tolerance window
func (vs *VerificationService) verifyTimestamp(timestampStr string) (bool, *time.Duration, error) {
	// Parse timestamp
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return false, nil, fmt.Errorf("invalid timestamp format: %w", err)
	}
	
	// Convert to time
	webhookTime := time.Unix(timestamp, 0)
	now := time.Now()
	
	// Calculate age
	age := now.Sub(webhookTime)
	
	// Check if within tolerance window
	valid := age >= 0 && age <= vs.toleranceWindow
	
	return valid, &age, nil
}

// SignWebhook signs a webhook payload with the given secret
func (vs *VerificationService) SignWebhook(payload []byte, secret string) string {
	return vs.generateSignature(payload, secret)
}

// CreateTimestamp creates a timestamp for webhook signing
func (vs *VerificationService) CreateTimestamp() string {
	return fmt.Sprintf("%d", time.Now().Unix())
}

// CreateSignedHeaders creates headers for webhook with signature and timestamp
func (vs *VerificationService) CreateSignedHeaders(payload []byte, secret string) map[string]string {
	timestamp := vs.CreateTimestamp()
	signature := vs.generateSignature(payload, secret)
	
	return map[string]string{
		"X-Webhook-Timestamp": timestamp,
		"X-Webhook-Signature": signature,
	}
}

// VerifySignatureOnly verifies only the signature without timestamp check
func (vs *VerificationService) VerifySignatureOnly(payload []byte, signature, secret string) bool {
	expectedSignature := vs.generateSignature(payload, secret)
	return vs.compareSignatures(signature, expectedSignature)
}

// SetToleranceWindow sets the time window for timestamp verification
func (vs *VerificationService) SetToleranceWindow(window time.Duration) {
	vs.toleranceWindow = window
}

// GetToleranceWindow returns the current tolerance window
func (vs *VerificationService) GetToleranceWindow() time.Duration {
	return vs.toleranceWindow
}

// WebhookSecurityHeaders represents security headers for webhook verification
type WebhookSecurityHeaders struct {
	Signature string `json:"signature" header:"X-Webhook-Signature"`
	Timestamp string `json:"timestamp" header:"X-Webhook-Timestamp"`
	Event     string `json:"event" header:"X-Webhook-Event"`
	ID        string `json:"id" header:"X-Webhook-ID"`
}

// ExtractSecurityHeaders extracts security headers from HTTP headers
func (vs *VerificationService) ExtractSecurityHeaders(headers map[string]string) *WebhookSecurityHeaders {
	return &WebhookSecurityHeaders{
		Signature: headers["X-Webhook-Signature"],
		Timestamp: headers["X-Webhook-Timestamp"],
		Event:     headers["X-Webhook-Event"],
		ID:        headers["X-Webhook-ID"],
	}
}

// ValidateSecurityHeaders validates that required security headers are present
func (vs *VerificationService) ValidateSecurityHeaders(headers *WebhookSecurityHeaders) error {
	if headers.Signature == "" {
		return fmt.Errorf("missing X-Webhook-Signature header")
	}
	
	if headers.Timestamp == "" {
		return fmt.Errorf("missing X-Webhook-Timestamp header")
	}
	
	if headers.Event == "" {
		return fmt.Errorf("missing X-Webhook-Event header")
	}
	
	if headers.ID == "" {
		return fmt.Errorf("missing X-Webhook-ID header")
	}
	
	// Validate signature format
	if !strings.HasPrefix(headers.Signature, "sha256=") {
		return fmt.Errorf("invalid signature format (must start with 'sha256=')")
	}
	
	// Validate timestamp format
	if _, err := strconv.ParseInt(headers.Timestamp, 10, 64); err != nil {
		return fmt.Errorf("invalid timestamp format: %w", err)
	}
	
	return nil
}

// GenerateSecret generates a cryptographically secure webhook secret
func (vs *VerificationService) GenerateSecret() string {
	// Generate 32 random bytes and encode as hex
	secret := make([]byte, 32)
	for i := range secret {
		secret[i] = byte(time.Now().UnixNano() % 256) // Simple random for demo
	}
	return hex.EncodeToString(secret)
}

// WebhookMiddleware creates middleware for webhook signature verification
func (vs *VerificationService) WebhookMiddleware(secret string, skipTimestamp bool) func(payload []byte, headers map[string]string) error {
	return func(payload []byte, headers map[string]string) error {
		// Extract security headers
		securityHeaders := vs.ExtractSecurityHeaders(headers)
		
		// Validate headers are present
		if err := vs.ValidateSecurityHeaders(securityHeaders); err != nil {
			return fmt.Errorf("webhook verification failed: %w", err)
		}
		
		// Verify webhook
		options := VerificationOptions{
			Payload:       payload,
			Signature:     securityHeaders.Signature,
			Timestamp:     securityHeaders.Timestamp,
			Secret:        secret,
			SkipTimestamp: skipTimestamp,
		}
		
		result := vs.VerifyWebhook(options)
		if !result.Valid {
			return fmt.Errorf("webhook verification failed: %s", result.Error)
		}
		
		return nil
	}
}