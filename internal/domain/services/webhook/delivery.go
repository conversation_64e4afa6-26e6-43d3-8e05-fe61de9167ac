package webhook

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// DeliveryService handles webhook delivery with retry logic
type DeliveryService struct {
	webhookEndpointRepo repositories.WebhookEndpointRepository
	webhookEventRepo    repositories.WebhookEventRepository
	httpClient          *http.Client
	maxRetries          int
	retryDelays         []time.Duration
}

// NewDeliveryService creates a new webhook delivery service
func NewDeliveryService(
	webhookEndpointRepo repositories.WebhookEndpointRepository,
	webhookEventRepo repositories.WebhookEventRepository,
) *DeliveryService {
	return &DeliveryService{
		webhookEndpointRepo: webhookEndpointRepo,
		webhookEventRepo:    webhookEventRepo,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		maxRetries: 5,
		retryDelays: []time.Duration{
			1 * time.Second,  // 1st retry: 1s
			5 * time.Second,  // 2nd retry: 5s
			30 * time.Second, // 3rd retry: 30s
			5 * time.Minute,  // 4th retry: 5m
			30 * time.Minute, // 5th retry: 30m
		},
	}
}

// WebhookPayload represents the webhook payload structure
type WebhookPayload struct {
	ID        string                 `json:"id"`
	Event     string                 `json:"event"`
	Data      map[string]interface{} `json:"data"`
	Meta      map[string]interface{} `json:"meta,omitempty"`
	Account   map[string]interface{} `json:"account"`
	Timestamp time.Time              `json:"timestamp"`
}

// DeliveryOptions represents delivery configuration
type DeliveryOptions struct {
	Event         string                 `json:"event"`
	EndpointIDs   []string               `json:"endpoint_ids"`
	Payload       map[string]interface{} `json:"payload"`
	AccountID     string                 `json:"account_id"`
	EnvironmentID *string                `json:"environment_id,omitempty"`
}

// DeliveryResult represents the result of webhook delivery
type DeliveryResult struct {
	EndpointID   string        `json:"endpoint_id"`
	EventID      string        `json:"event_id"`
	Success      bool          `json:"success"`
	StatusCode   int           `json:"status_code"`
	ResponseBody string        `json:"response_body"`
	Error        string        `json:"error,omitempty"`
	Attempts     int           `json:"attempts"`
	NextRetry    *time.Time    `json:"next_retry,omitempty"`
	Duration     time.Duration `json:"duration"`
}

// DeliverWebhooks delivers webhooks to multiple endpoints (maps to Ruby CreateWebhookEventsWorker2.perform_async)
func (ds *DeliveryService) DeliverWebhooks(ctx context.Context, options DeliveryOptions) ([]*DeliveryResult, error) {
	var results []*DeliveryResult

	// Get webhook endpoints
	_, err := uuid.Parse(options.AccountID)
	if err != nil {
		return nil, fmt.Errorf("invalid account ID: %w", err)
	}

	// Filter endpoints by environment if specified
	var endpoints []entities.WebhookEndpoint
	for _, endpointID := range options.EndpointIDs {
		endpointUUID, err := uuid.Parse(endpointID)
		if err != nil {
			continue
		}

		endpoint, err := ds.webhookEndpointRepo.GetByID(ctx, endpointUUID)
		if err != nil {
			continue
		}

		// Verify endpoint belongs to account
		if endpoint.AccountID != options.AccountID {
			continue
		}

		// Check environment scope
		if options.EnvironmentID != nil {
			if endpoint.EnvironmentID == nil || *endpoint.EnvironmentID != *options.EnvironmentID {
				continue
			}
		} else if endpoint.EnvironmentID != nil {
			continue // Global events shouldn't go to environment-scoped endpoints
		}

		endpoints = append(endpoints, *endpoint)
	}

	// Deliver to each endpoint
	for _, endpoint := range endpoints {
		result := ds.deliverToEndpoint(ctx, endpoint, options)
		results = append(results, result)
	}

	return results, nil
}

// deliverToEndpoint delivers webhook to a single endpoint with retry logic
func (ds *DeliveryService) deliverToEndpoint(ctx context.Context, endpoint entities.WebhookEndpoint, options DeliveryOptions) *DeliveryResult {
	// Create webhook event record
	eventID := uuid.New().String()
	event := &entities.WebhookEvent{
		ID:                eventID,
		WebhookEndpointID: endpoint.ID,
		AccountID:         options.AccountID,
		EnvironmentID:     options.EnvironmentID,
		Event:             options.Event,
		Data:              options.Payload, // Add Data field
		Payload:           options.Payload, // Same as Data for compatibility
		Status:            entities.WebhookEventStatusPending,
		AttemptCount:      0,
		Attempts:          0, // Alternative field
		MaxAttempts:       endpoint.MaxRetries,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Payload is already set above as map[string]interface{}
	// No need to marshal/unmarshal

	// Save event to database
	ds.webhookEventRepo.Create(ctx, event)

	result := &DeliveryResult{
		EndpointID: endpoint.ID,
		EventID:    eventID,
		Attempts:   0,
	}

	// Attempt delivery with retries
	for attempt := 0; attempt <= ds.maxRetries; attempt++ {
		result.Attempts = attempt + 1

		// Prepare payload
		payload := ds.buildWebhookPayload(eventID, options)

		// Attempt delivery
		startTime := time.Now()
		success, statusCode, responseBody, err := ds.attemptDelivery(ctx, endpoint, payload)
		result.Duration = time.Since(startTime)

		result.Success = success
		result.StatusCode = statusCode
		result.ResponseBody = responseBody

		if err != nil {
			result.Error = err.Error()
		}

		// Update event status
		if success {
			event.Status = "delivered"
			event.DeliveredAt = &startTime
		} else if attempt == ds.maxRetries {
			event.Status = "failed"
		} else {
			event.Status = "retrying"
			nextRetry := time.Now().Add(ds.retryDelays[attempt])
			event.NextRetryAt = &nextRetry
			result.NextRetry = &nextRetry
		}

		event.Attempts = attempt + 1
		event.LastResponseStatus = statusCode
		event.LastResponseBody = responseBody
		event.UpdatedAt = time.Now()

		// Update database
		ds.webhookEventRepo.Update(ctx, event)

		// If successful or max retries reached, break
		if success || attempt == ds.maxRetries {
			break
		}

		// Wait before retry (with context cancellation support)
		select {
		case <-ctx.Done():
			return result
		case <-time.After(ds.retryDelays[attempt]):
			// Continue to next attempt
		}
	}

	return result
}

// attemptDelivery attempts to deliver webhook to endpoint
func (ds *DeliveryService) attemptDelivery(ctx context.Context, endpoint entities.WebhookEndpoint, payload *WebhookPayload) (bool, int, string, error) {
	// Marshal payload
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return false, 0, "", fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint.URL, bytes.NewReader(payloadBytes))
	if err != nil {
		return false, 0, "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "GoKeys-Webhook/1.0")
	req.Header.Set("X-Webhook-Event", payload.Event)
	req.Header.Set("X-Webhook-ID", payload.ID)
	req.Header.Set("X-Webhook-Timestamp", fmt.Sprintf("%d", payload.Timestamp.Unix()))

	// Add signature if secret is configured
	if endpoint.SigningSecret != "" {
		signature := ds.generateSignature(payloadBytes, endpoint.SigningSecret)
		req.Header.Set("X-Webhook-Signature", signature)
	}

	// Add custom headers if configured
	if endpoint.Headers != nil {
		for key, value := range endpoint.Headers {
			req.Header.Set(key, value)
		}
	}

	// Make request
	resp, err := ds.httpClient.Do(req)
	if err != nil {
		return false, 0, "", fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, resp.StatusCode, "", fmt.Errorf("failed to read response: %w", err)
	}

	responseBody := string(bodyBytes)

	// Consider 2xx status codes as success
	success := resp.StatusCode >= 200 && resp.StatusCode < 300

	return success, resp.StatusCode, responseBody, nil
}

// buildWebhookPayload builds the webhook payload
func (ds *DeliveryService) buildWebhookPayload(eventID string, options DeliveryOptions) *WebhookPayload {
	return &WebhookPayload{
		ID:    eventID,
		Event: options.Event,
		Data:  options.Payload,
		Meta: map[string]interface{}{
			"account_id": options.AccountID,
		},
		Account: map[string]interface{}{
			"id": options.AccountID,
		},
		Timestamp: time.Now(),
	}
}

// generateSignature generates HMAC-SHA256 signature for webhook payload
func (ds *DeliveryService) generateSignature(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	signature := hex.EncodeToString(h.Sum(nil))
	return fmt.Sprintf("sha256=%s", signature)
}

// VerifySignature verifies webhook signature
func (ds *DeliveryService) VerifySignature(payload []byte, signature, secret string) bool {
	expectedSignature := ds.generateSignature(payload, secret)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// RetryFailedWebhooks retries failed webhook deliveries
func (ds *DeliveryService) RetryFailedWebhooks(ctx context.Context) error {
	// Get failed events that are ready for retry
	events, err := ds.webhookEventRepo.GetRetryableEvents(ctx, time.Now())
	if err != nil {
		return fmt.Errorf("failed to get retryable events: %w", err)
	}

	for _, event := range events {
		// Get endpoint
		endpointUUID, err := uuid.Parse(event.WebhookEndpointID)
		if err != nil {
			continue
		}

		endpoint, err := ds.webhookEndpointRepo.GetByID(ctx, endpointUUID)
		if err != nil {
			continue
		}

		// Prepare options
		payload := event.Payload
		if payload == nil {
			payload = make(map[string]interface{})
		}

		options := DeliveryOptions{
			Event:         event.Event,
			EndpointIDs:   []string{endpoint.ID},
			Payload:       payload,
			AccountID:     event.AccountID,
			EnvironmentID: event.EnvironmentID,
		}

		// Retry delivery
		ds.deliverToEndpoint(ctx, *endpoint, options)
	}

	return nil
}

// SetMaxRetries sets the maximum number of retry attempts
func (ds *DeliveryService) SetMaxRetries(maxRetries int) {
	ds.maxRetries = maxRetries
}

// SetRetryDelays sets custom retry delay intervals
func (ds *DeliveryService) SetRetryDelays(delays []time.Duration) {
	ds.retryDelays = delays
}

// SetHTTPTimeout sets the HTTP client timeout
func (ds *DeliveryService) SetHTTPTimeout(timeout time.Duration) {
	ds.httpClient.Timeout = timeout
}
