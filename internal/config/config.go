package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	Metrics  MetricsConfig  `mapstructure:"metrics"`
	NATS     NATSConfig     `mapstructure:"nats"`
	Security SecurityConfig `mapstructure:"security"`
	Logging  LoggingConfig  `mapstructure:"logging"`
}

type ServerConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Environment  string `mapstructure:"environment"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
}

type DatabaseConfig struct {
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	User            string `mapstructure:"user"`
	Password        string `mapstructure:"password"`
	DBName          string `mapstructure:"dbname"`
	SSLMode         string `mapstructure:"sslmode"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

type RedisConfig struct {
	Address     string `mapstructure:"address"`
	Password    string `mapstructure:"password"`
	DB          int    `mapstructure:"db"`
	PoolSize    int    `mapstructure:"pool_size"`
	MinIdleConn int    `mapstructure:"min_idle_conn"`
}

type MetricsConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	Port            int    `mapstructure:"port"`
	Path            string `mapstructure:"path"`
	VictoriaMetrics struct {
		Enabled  bool   `mapstructure:"enabled"`
		Endpoint string `mapstructure:"endpoint"`
		Username string `mapstructure:"username"`
		Password string `mapstructure:"password"`
	} `mapstructure:"victoria_metrics"`
}

type NATSConfig struct {
	URL             string `mapstructure:"url"`
	ClusterID       string `mapstructure:"cluster_id"`
	ClientID        string `mapstructure:"client_id"`
	MaxReconnects   int    `mapstructure:"max_reconnects"`
	ReconnectDelay  int    `mapstructure:"reconnect_delay"`
	ConnectionName  string `mapstructure:"connection_name"`
}

type SecurityConfig struct {
	JWTSecret         string `mapstructure:"jwt_secret"`
	JWTExpiration     int    `mapstructure:"jwt_expiration"`
	BcryptCost        int    `mapstructure:"bcrypt_cost"`
	RateLimitEnabled  bool   `mapstructure:"rate_limit_enabled"`
	RateLimitRequests int    `mapstructure:"rate_limit_requests"`
	RateLimitWindow   int    `mapstructure:"rate_limit_window"`
	CORSEnabled       bool   `mapstructure:"cors_enabled"`
	CORSOrigins       []string `mapstructure:"cors_origins"`
}

type LoggingConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	config := &Config{}

	// Set default values
	setDefaults()

	// Set up Viper
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/gokeys/")

	// Enable environment variable reading
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read config file (optional)
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// Config file not found is OK, we'll use defaults and env vars
	}

	// Unmarshal config
	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Validate config
	if err := validate(config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.environment", "development")
	viper.SetDefault("server.read_timeout", 30)
	viper.SetDefault("server.write_timeout", 30)
	viper.SetDefault("server.idle_timeout", 120)

	// Database defaults
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "gokeys")
	viper.SetDefault("database.password", "gokeys")
	viper.SetDefault("database.dbname", "gokeys")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.max_idle_conns", 10)
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("database.conn_max_lifetime", 3600)

	// Redis defaults
	viper.SetDefault("redis.address", "localhost:6379")
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)
	viper.SetDefault("redis.min_idle_conn", 5)

	// Metrics defaults
	viper.SetDefault("metrics.enabled", true)
	viper.SetDefault("metrics.port", 9090)
	viper.SetDefault("metrics.path", "/metrics")
	viper.SetDefault("metrics.victoria_metrics.enabled", false)

	// NATS defaults
	viper.SetDefault("nats.url", "nats://localhost:4222")
	viper.SetDefault("nats.cluster_id", "gokeys-cluster")
	viper.SetDefault("nats.client_id", "gokeys-client")
	viper.SetDefault("nats.max_reconnects", 10)
	viper.SetDefault("nats.reconnect_delay", 2)
	viper.SetDefault("nats.connection_name", "GoKeys")

	// Security defaults
	viper.SetDefault("security.jwt_secret", "change-me-in-production")
	viper.SetDefault("security.jwt_expiration", 3600)
	viper.SetDefault("security.bcrypt_cost", 12)
	viper.SetDefault("security.rate_limit_enabled", true)
	viper.SetDefault("security.rate_limit_requests", 100)
	viper.SetDefault("security.rate_limit_window", 60)
	viper.SetDefault("security.cors_enabled", true)
	viper.SetDefault("security.cors_origins", []string{"*"})

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")
	viper.SetDefault("logging.max_size", 100)
	viper.SetDefault("logging.max_backups", 3)
	viper.SetDefault("logging.max_age", 28)
	viper.SetDefault("logging.compress", true)
}

// validate validates the configuration
func validate(config *Config) error {
	if config.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}

	if config.Database.User == "" {
		return fmt.Errorf("database user is required")
	}

	if config.Database.DBName == "" {
		return fmt.Errorf("database name is required")
	}

	if config.Security.JWTSecret == "change-me-in-production" && config.Server.Environment == "production" {
		return fmt.Errorf("JWT secret must be changed in production")
	}

	return nil
}