package delegate

import (
	"context"
	"time"

	"github.com/google/uuid"
)

// Common delegate function names used across the GoKeys domain
const (
	// License validation delegates
	LicenseValidateFunc     = "license.validate"
	LicenseCheckExpiryFunc  = "license.check_expiry"
	LicenseGetInfoFunc      = "license.get_info"
	LicenseCacheGetFunc     = "license.cache.get"
	LicenseCacheSetFunc     = "license.cache.set"
	LicenseCacheDeleteFunc  = "license.cache.delete"

	// Machine management delegates
	MachineRegisterFunc     = "machine.register"
	MachineUpdateFunc       = "machine.update"
	MachineGetByFingerprintFunc = "machine.get_by_fingerprint"
	MachineCheckLimitFunc   = "machine.check_limit"

	// Account management delegates
	AccountGetFunc          = "account.get"
	AccountValidateFunc     = "account.validate"
	AccountGetSettingsFunc  = "account.get_settings"

	// User management delegates
	UserGetFunc             = "user.get"
	UserValidateTokenFunc   = "user.validate_token"
	UserGetPermissionsFunc  = "user.get_permissions"

	// Policy enforcement delegates
	PolicyValidateFunc      = "policy.validate"
	PolicyGetRulesFunc      = "policy.get_rules"
	PolicyCheckLimitsFunc   = "policy.check_limits"

	// Audit and logging delegates
	AuditLogFunc            = "audit.log"
	EventPublishFunc        = "event.publish"
	MetricsRecordFunc       = "metrics.record"

	// Cryptographic delegates
	CryptoSignFunc          = "crypto.sign"
	CryptoVerifyFunc        = "crypto.verify"
	CryptoEncryptFunc       = "crypto.encrypt"
	CryptoDecryptFunc       = "crypto.decrypt"
)

// Common types used in delegate functions

// ValidationRequest represents a generic validation request
type ValidationRequest struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Data        map[string]interface{} `json:"data"`
	Context     context.Context        `json:"-"`
	RequestedAt time.Time              `json:"requested_at"`
}

// ValidationResult represents a generic validation result
type ValidationResult struct {
	Valid       bool                   `json:"valid"`
	Errors      []string               `json:"errors,omitempty"`
	Warnings    []string               `json:"warnings,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
	ValidatedAt time.Time              `json:"validated_at"`
}

// LicenseInfo represents license information for delegation
type LicenseInfo struct {
	ID              uuid.UUID              `json:"id"`
	Key             string                 `json:"key"`
	Status          string                 `json:"status"`
	ExpiresAt       *time.Time             `json:"expires_at,omitempty"`
	MachinesUsed    int                    `json:"machines_used"`
	MachinesAllowed int                    `json:"machines_allowed"`
	PolicyID        uuid.UUID              `json:"policy_id"`
	AccountID       uuid.UUID              `json:"account_id"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// MachineInfo represents machine information for delegation
type MachineInfo struct {
	ID          uuid.UUID              `json:"id"`
	Fingerprint string                 `json:"fingerprint"`
	Name        string                 `json:"name"`
	Environment string                 `json:"environment"`
	OS          string                 `json:"os"`
	Hostname    string                 `json:"hostname"`
	IPAddress   string                 `json:"ip_address"`
	LastSeen    time.Time              `json:"last_seen"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AccountInfo represents account information for delegation
type AccountInfo struct {
	ID       uuid.UUID              `json:"id"`
	Name     string                 `json:"name"`
	Status   string                 `json:"status"`
	Settings map[string]interface{} `json:"settings,omitempty"`
}

// PolicyInfo represents policy information for delegation
type PolicyInfo struct {
	ID               uuid.UUID              `json:"id"`
	Name             string                 `json:"name"`
	MachineLimit     int                    `json:"machine_limit"`
	ExpirationPolicy string                 `json:"expiration_policy"`
	Rules            map[string]interface{} `json:"rules,omitempty"`
}

// AuditEvent represents an audit event for delegation
type AuditEvent struct {
	EventType   string                 `json:"event_type"`
	EntityType  string                 `json:"entity_type"`
	EntityID    string                 `json:"entity_id"`
	UserID      *uuid.UUID             `json:"user_id,omitempty"`
	AccountID   uuid.UUID              `json:"account_id"`
	Action      string                 `json:"action"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
	IPAddress   string                 `json:"ip_address,omitempty"`
	UserAgent   string                 `json:"user_agent,omitempty"`
}

// MetricData represents metric data for delegation
type MetricData struct {
	Name      string                 `json:"name"`
	Value     float64                `json:"value"`
	Tags      map[string]string      `json:"tags,omitempty"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// CryptoRequest represents a cryptographic operation request
type CryptoRequest struct {
	Operation string                 `json:"operation"`
	Algorithm string                 `json:"algorithm"`
	Data      []byte                 `json:"data"`
	Key       []byte                 `json:"key,omitempty"`
	Options   map[string]interface{} `json:"options,omitempty"`
}

// CryptoResult represents a cryptographic operation result
type CryptoResult struct {
	Success   bool                   `json:"success"`
	Data      []byte                 `json:"data,omitempty"`
	Signature []byte                 `json:"signature,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}