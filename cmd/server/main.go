// @title GoKeys License Management API
// @version 1.0
// @description Enterprise License Management Platform with comprehensive license validation, machine tracking, and policy management capabilities.
// @termsOfService https://gokeys.com/terms

// @contact.name GoKeys API Support
// @contact.url https://gokeys.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name X-API-Key
// @description API key for authentication

// @securityDefinitions.apikey LicenseKeyAuth
// @in header
// @name X-License-Key
// @description License key for validation endpoints

// @tag.name Authentication
// @tag.description Authentication and authorization endpoints

// @tag.name Licenses
// @tag.description License management and validation

// @tag.name Accounts
// @tag.description Account management

// @tag.name Products
// @tag.description Product management

// @tag.name Policies
// @tag.description Policy configuration

// @tag.name Machines
// @tag.description Machine registration and tracking

// @tag.name Users
// @tag.description User management

// @tag.name Health
// @tag.description System health and monitoring

// @tag.name Metrics
// @tag.description System metrics and analytics

package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gokeys/gokeys/internal/adapters/database"
	httpAdapter "github.com/gokeys/gokeys/internal/adapters/http"
	"github.com/gokeys/gokeys/internal/adapters/metrics"
	"github.com/gokeys/gokeys/internal/config"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/rs/zerolog"
	
	// Import docs for swagger
	_ "github.com/gokeys/gokeys/docs"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	log.Printf("Starting GoKeys License Management Platform in %s mode", cfg.Server.Environment)

	// Initialize structured logger
	logger := zerolog.New(os.Stdout).With().
		Timestamp().
		Str("service", "gokeys").
		Logger()

	// Initialize database
	dbService, err := database.NewPostgreSQL(database.Config{
		Host:            cfg.Database.Host,
		Port:            cfg.Database.Port,
		User:            cfg.Database.User,
		Password:        cfg.Database.Password,
		DBName:          cfg.Database.DBName,
		SSLMode:         cfg.Database.SSLMode,
		MaxIdleConns:    cfg.Database.MaxIdleConns,
		MaxOpenConns:    cfg.Database.MaxOpenConns,
		ConnMaxLifetime: cfg.Database.ConnMaxLifetime,
	})
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer dbService.Close()

	// Initialize database (extensions and migrations)
	if err := dbService.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database schema: %v", err)
	}

	// Initialize service coordinator with all domain services
	serviceCoordinator := services.NewServiceCoordinator(dbService.GetDB(), logger)
	log.Printf("Service coordinator initialized with all domain services")

	// Initialize metrics
	metricsCollector := metrics.NewMetricsCollector()
	customMetrics := metrics.NewCustomMetrics(metricsCollector)

	// Start metrics server
	metricsServer := metrics.NewMetricsServer(cfg)
	go func() {
		if err := metricsServer.Start(); err != nil && err != http.ErrServerClosed {
			log.Printf("Metrics server error: %v", err)
		}
	}()

	// Start VictoriaMetrics export if enabled
	var vmExporter *metrics.VictoriaMetricsExporter
	if cfg.Metrics.VictoriaMetrics.Enabled {
		vmExporter = metrics.NewVictoriaMetricsExporter(cfg)
		vmExporter.StartPeriodicExport(30 * time.Second)
	}

	// Create HTTP server with metrics middleware
	httpServer := httpAdapter.NewServer(cfg)
	
	// Add metrics middleware to the server
	httpServer.AddMetricsMiddleware(metricsCollector)
	
	// Set database for health checks
	httpServer.SetDatabase(dbService)
	
	// Set metrics exporter for monitoring
	if vmExporter != nil {
		httpServer.SetMetricsExporter(vmExporter)
	}
	
	// Set service coordinator to enable API routes
	httpServer.SetServiceCoordinator(serviceCoordinator)
	log.Printf("HTTP server configured with service coordinator and API routes")
	
	// Store custom metrics for later use
	_ = customMetrics // Will be used when implementing business logic

	// Start HTTP server in a goroutine
	go func() {
		if err := httpServer.Start(); err != nil {
			log.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Create context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := httpServer.Stop(ctx); err != nil {
		log.Printf("HTTP server forced to shutdown: %v", err)
	}

	// Stop VictoriaMetrics exporter
	if vmExporter != nil && vmExporter.IsRunning() {
		vmExporter.Stop()
	}

	// Stop metrics server
	if err := metricsServer.Stop(); err != nil {
		log.Printf("Metrics server forced to shutdown: %v", err)
	}

	log.Println("Server shutdown complete")
}