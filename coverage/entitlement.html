
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>handlers: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">github.com/gokeys/gokeys/internal/adapters/http/handlers/account.go (0.0%)</option>
				
				<option value="file1">github.com/gokeys/gokeys/internal/adapters/http/handlers/entitlement.go (0.0%)</option>
				
				<option value="file2">github.com/gokeys/gokeys/internal/adapters/http/handlers/group.go (0.0%)</option>
				
				<option value="file3">github.com/gokeys/gokeys/internal/adapters/http/handlers/license.go (0.0%)</option>
				
				<option value="file4">github.com/gokeys/gokeys/internal/adapters/http/handlers/machine.go (0.0%)</option>
				
				<option value="file5">github.com/gokeys/gokeys/internal/adapters/http/handlers/plan.go (0.0%)</option>
				
				<option value="file6">github.com/gokeys/gokeys/internal/adapters/http/handlers/policy.go (0.0%)</option>
				
				<option value="file7">github.com/gokeys/gokeys/internal/adapters/http/handlers/product.go (0.0%)</option>
				
				<option value="file8">github.com/gokeys/gokeys/internal/adapters/http/handlers/webhook_endpoint.go (0.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">no coverage</span>
				<span class="cov1">low coverage</span>
				<span class="cov2">*</span>
				<span class="cov3">*</span>
				<span class="cov4">*</span>
				<span class="cov5">*</span>
				<span class="cov6">*</span>
				<span class="cov7">*</span>
				<span class="cov8">*</span>
				<span class="cov9">*</span>
				<span class="cov10">high coverage</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package handlers

import (
        "net/http"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/services"
)

type AccountHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewAccountHandler(serviceCoordinator *services.ServiceCoordinator) *AccountHandler <span class="cov0" title="0">{
        return &amp;AccountHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

type AccountUpdateRequest struct {
        Data struct {
                Type       string `json:"type" binding:"required"`
                Attributes struct {
                        Name     *string                `json:"name,omitempty"`
                        Slug     *string                `json:"slug,omitempty"`
                        Metadata map[string]interface{} `json:"metadata,omitempty"`
                } `json:"attributes"`
        } `json:"data"`
}

// GetAccountInfoHandler handles GET /api/v1/accounts/:id/info
func (h *AccountHandler) GetAccountInfoHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account not found")
                return
        }</span>

        <span class="cov0" title="0">requestedAccountID := c.Param("account_id")

        // Users can only access their own account info unless they have admin permissions
        userPermissions, _ := middleware.GetPermissions(c)
        isAdmin := false
        for _, perm := range userPermissions </span><span class="cov0" title="0">{
                if perm == middleware.PermissionSystemAdmin || perm == middleware.PermissionSuperAdmin </span><span class="cov0" title="0">{
                        isAdmin = true
                        break</span>
                }
        }

        <span class="cov0" title="0">if !isAdmin &amp;&amp; requestedAccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderForbidden(c, "Access denied to account information")
                return
        }</span>

        // Get account from repository
        <span class="cov0" title="0">account := &amp;entities.Account{} // Placeholder

        resource := responses.ConvertAccount(account)
        responses.RenderSingle(c, http.StatusOK, resource, nil)</span>
}

// UpdateAccountSettingsHandler handles PUT /api/v1/accounts/:id/settings
func (h *AccountHandler) UpdateAccountSettingsHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account not found")
                return
        }</span>

        <span class="cov0" title="0">requestedAccountID := c.Param("account_id")

        // Users can only update their own account unless they have admin permissions
        userPermissions, _ := middleware.GetPermissions(c)
        isAdmin := false
        for _, perm := range userPermissions </span><span class="cov0" title="0">{
                if perm == middleware.PermissionSystemAdmin || perm == middleware.PermissionSuperAdmin </span><span class="cov0" title="0">{
                        isAdmin = true
                        break</span>
                }
        }

        <span class="cov0" title="0">if !isAdmin &amp;&amp; requestedAccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderForbidden(c, "Access denied to update account settings")
                return
        }</span>

        <span class="cov0" title="0">var req AccountUpdateRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Get existing account from repository
        // Update account fields
        // Save to repository
        // Broadcast event

        <span class="cov0" title="0">account := &amp;entities.Account{} // Placeholder

        resource := responses.ConvertAccount(account)
        responses.RenderSingle(c, http.StatusOK, resource, nil)</span>
}

// GetCurrentAccountHandler handles GET /api/v1/account (current account)
func (h *AccountHandler) GetCurrentAccountHandler(c *gin.Context) <span class="cov0" title="0">{
        _, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account not found")
                return
        }</span>

        // Get account from repository
        <span class="cov0" title="0">account := &amp;entities.Account{} // Placeholder

        resource := responses.ConvertAccount(account)
        responses.RenderSingle(c, http.StatusOK, resource, nil)</span>
}

// UpdateCurrentAccountHandler handles PUT /api/v1/account (current account)
func (h *AccountHandler) UpdateCurrentAccountHandler(c *gin.Context) <span class="cov0" title="0">{
        _, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account not found")
                return
        }</span>

        <span class="cov0" title="0">var req AccountUpdateRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Get existing account from repository
        // Update account fields (only non-protected fields)
        // Save to repository
        // Broadcast event

        <span class="cov0" title="0">account := &amp;entities.Account{} // Placeholder

        resource := responses.ConvertAccount(account)
        responses.RenderSingle(c, http.StatusOK, resource, nil)</span>
}

// ListAccountsHandler handles GET /api/v1/admin/accounts (admin only)
func (h *AccountHandler) ListAccountsHandler(c *gin.Context) <span class="cov0" title="0">{
        // Parse pagination
        page := 1
        limit := 25
        // Parse pagination parameters

        // Get accounts from repository with filters
        accounts := []entities.Account{} // Placeholder

        // Convert to resource objects
        resources := make([]interface{}, len(accounts))
        for i, account := range accounts </span><span class="cov0" title="0">{
                resources[i] = responses.ConvertAccount(&amp;account)
        }</span>

        // Create pagination metadata
        <span class="cov0" title="0">totalAccounts := int64(len(accounts))
        pagination := &amp;responses.PaginationMeta{
                Page:       page,
                PerPage:    limit,
                Total:      totalAccounts,
                TotalPages: (totalAccounts + int64(limit) - 1) / int64(limit),
        }

        responses.RenderCollection(c, http.StatusOK, resources, pagination, nil)</span>
}

// GetAccountDetailsHandler handles GET /api/v1/admin/accounts/:id (admin only)
func (h *AccountHandler) GetAccountDetailsHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID := c.Param("id")
        if accountID == "" </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Account ID is required")
                return
        }</span>

        // Get account from repository by ID
        <span class="cov0" title="0">account := &amp;entities.Account{} // Placeholder

        resource := responses.ConvertAccount(account)
        responses.RenderSingle(c, http.StatusOK, resource, nil)</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package handlers

import (
        "net/http"
        "strconv"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/gokeys/gokeys/internal/domain/services/events"
        "github.com/google/uuid"
)

type EntitlementHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewEntitlementHandler(serviceCoordinator *services.ServiceCoordinator) *EntitlementHandler <span class="cov0" title="0">{
        return &amp;EntitlementHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

type EntitlementRequest struct {
        Data struct {
                Type       string `json:"type" binding:"required"`
                Attributes struct {
                        Name     string                 `json:"name" binding:"required"`
                        Code     string                 `json:"code" binding:"required"`
                        Metadata map[string]interface{} `json:"metadata,omitempty"`
                } `json:"attributes"`
                Relationships *struct {
                        Environment *struct {
                                Data *struct {
                                        Type string `json:"type"`
                                        ID   string `json:"id"`
                                } `json:"data"`
                        } `json:"environment,omitempty"`
                } `json:"relationships,omitempty"`
        } `json:"data"`
}

func (h *EntitlementHandler) ListEntitlementsHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Account not found"})
                return
        }</span>

        // Parse pagination
        <span class="cov0" title="0">page := 1
        limit := 25
        if p := c.Query("page"); p != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(p); err == nil &amp;&amp; parsed &gt; 0 </span><span class="cov0" title="0">{
                        page = parsed
                }</span>
        }
        <span class="cov0" title="0">if l := c.Query("limit"); l != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(l); err == nil &amp;&amp; parsed &gt; 0 &amp;&amp; parsed &lt;= 100 </span><span class="cov0" title="0">{
                        limit = parsed
                }</span>
        }

        <span class="cov0" title="0">_ = (page - 1) * limit // offset calculation

        // Build filter for repository
        filter := repositories.ListFilter{
                Page:      page,
                PageSize:  limit,
                SortBy:    "created_at",
                SortOrder: "DESC",
                AccountID: &amp;accountID,
                Filters:   make(map[string]interface{}),
        }

        entitlements, total, err := h.serviceCoordinator.Repositories.Entitlement().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch entitlements"})
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": entitlements,
                "meta": gin.H{
                        "page":  page,
                        "limit": limit,
                        "total": total,
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *EntitlementHandler) GetEntitlementHandler(c *gin.Context) <span class="cov0" title="0">{
        _, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Account not found"})
                return
        }</span>

        <span class="cov0" title="0">entitlementID := c.Param("id")
        if entitlementID == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Entitlement ID is required"})
                return
        }</span>

        <span class="cov0" title="0">entitlementUUID, err := uuid.Parse(entitlementID)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entitlement ID format"})
                return
        }</span>

        <span class="cov0" title="0">entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementUUID)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Entitlement not found"})
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": entitlement,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *EntitlementHandler) CreateEntitlementHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Account not found"})
                return
        }</span>

        <span class="cov0" title="0">var req EntitlementRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">entitlement := &amp;entities.Entitlement{
                AccountID: accountID.String(),
                Name:      req.Data.Attributes.Name,
                Code:      req.Data.Attributes.Code,
                Metadata:  req.Data.Attributes.Metadata,
        }

        if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Environment != nil &amp;&amp; req.Data.Relationships.Environment.Data != nil </span><span class="cov0" title="0">{
                entitlement.EnvironmentID = &amp;req.Data.Relationships.Environment.Data.ID
        }</span>

        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Entitlement().Create(c.Request.Context(), entitlement); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnprocessableEntity, gin.H{"error": "Failed to create entitlement"})
                return
        }</span>

        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventEntitlementCreated,
                account,
                events.MakeEventResource(entitlement),
                events.EventMeta{},
        )

        response := gin.H{
                "data": entitlement,
        }

        c.Header("Location", "/api/v1/entitlements/"+entitlement.ID)
        c.JSON(http.StatusCreated, response)</span>
}

func (h *EntitlementHandler) UpdateEntitlementHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Account not found"})
                return
        }</span>

        <span class="cov0" title="0">entitlementID := c.Param("id")
        if entitlementID == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Entitlement ID is required"})
                return
        }</span>

        <span class="cov0" title="0">var req EntitlementRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">entitlementUUID, err := uuid.Parse(entitlementID)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entitlement ID format"})
                return
        }</span>

        <span class="cov0" title="0">entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementUUID)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Entitlement not found"})
                return
        }</span>

        <span class="cov0" title="0">entitlement.Name = req.Data.Attributes.Name
        entitlement.Code = req.Data.Attributes.Code
        entitlement.Metadata = req.Data.Attributes.Metadata

        if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Environment != nil &amp;&amp; req.Data.Relationships.Environment.Data != nil </span><span class="cov0" title="0">{
                entitlement.EnvironmentID = &amp;req.Data.Relationships.Environment.Data.ID
        }</span>

        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Entitlement().Update(c.Request.Context(), entitlement); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnprocessableEntity, gin.H{"error": "Failed to update entitlement"})
                return
        }</span>

        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventEntitlementUpdated,
                account,
                events.MakeEventResource(entitlement),
                events.EventMeta{},
        )

        response := gin.H{
                "data": entitlement,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *EntitlementHandler) DeleteEntitlementHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Account not found"})
                return
        }</span>

        <span class="cov0" title="0">entitlementID := c.Param("id")
        if entitlementID == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Entitlement ID is required"})
                return
        }</span>

        <span class="cov0" title="0">entitlementUUID, err := uuid.Parse(entitlementID)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entitlement ID format"})
                return
        }</span>

        <span class="cov0" title="0">entitlement, err := h.serviceCoordinator.Repositories.Entitlement().GetByID(c.Request.Context(), entitlementUUID)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Entitlement not found"})
                return
        }</span>

        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Entitlement().Delete(c.Request.Context(), entitlementUUID); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete entitlement"})
                return
        }</span>

        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventEntitlementDeleted,
                account,
                events.MakeEventResource(entitlement),
                events.EventMeta{},
        )

        c.Status(http.StatusNoContent)</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package handlers

import (
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/gokeys/gokeys/internal/domain/services/events"
        "github.com/google/uuid"
)

type GroupHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewGroupHandler(serviceCoordinator *services.ServiceCoordinator) *GroupHandler <span class="cov0" title="0">{
        return &amp;GroupHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

type GroupRequest struct {
        Data struct {
                Type       string `json:"type" binding:"required"`
                Attributes struct {
                        Name        string                 `json:"name" binding:"required"`
                        MaxUsers    *int                   `json:"max_users,omitempty"`
                        MaxLicenses *int                   `json:"max_licenses,omitempty"`
                        MaxMachines *int                   `json:"max_machines,omitempty"`
                        Metadata    map[string]interface{} `json:"metadata,omitempty"`
                } `json:"attributes"`
                Relationships *struct {
                        Environment *struct {
                                Data *struct {
                                        Type string `json:"type"`
                                        ID   string `json:"id"`
                                } `json:"data"`
                        } `json:"environment,omitempty"`
                } `json:"relationships,omitempty"`
        } `json:"data"`
}

func (h *GroupHandler) ListGroupsHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        // Parse pagination
        <span class="cov0" title="0">page := 1
        limit := 25
        if p := c.Query("page"); p != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(p); err == nil &amp;&amp; parsed &gt; 0 </span><span class="cov0" title="0">{
                        page = parsed
                }</span>
        }
        <span class="cov0" title="0">if l := c.Query("limit"); l != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(l); err == nil &amp;&amp; parsed &gt; 0 &amp;&amp; parsed &lt;= 100 </span><span class="cov0" title="0">{
                        limit = parsed
                }</span>
        }

        // Build filter
        <span class="cov0" title="0">filter := repositories.ListFilter{
                Page:      page,
                PageSize:  limit,
                SortBy:    c.DefaultQuery("sort_by", "created_at"),
                SortOrder: c.DefaultQuery("sort_order", "DESC"),
                Search:    c.Query("search"),
                AccountID: &amp;accountID,
                Filters:   make(map[string]interface{}),
        }

        // Get groups from repository
        groups, total, err := h.serviceCoordinator.Repositories.Group().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to retrieve groups: "+err.Error())
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": groups,
                "meta": gin.H{
                        "page":        page,
                        "limit":       limit,
                        "total":       total,
                        "total_pages": (total + int64(limit) - 1) / int64(limit),
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *GroupHandler) GetGroupHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">groupIDStr := c.Param("id")
        groupID, err := uuid.Parse(groupIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid group ID format")
                return
        }</span>

        // Get group from repository by ID
        <span class="cov0" title="0">group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Group not found")
                return
        }</span>

        // Verify group belongs to the account
        <span class="cov0" title="0">if group.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Group not found")
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": group,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *GroupHandler) CreateGroupHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">var req GroupRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Create group through service layer
        <span class="cov0" title="0">group := &amp;entities.Group{
                AccountID:   accountID.String(),
                Name:        req.Data.Attributes.Name,
                MaxUsers:    req.Data.Attributes.MaxUsers,
                MaxLicenses: req.Data.Attributes.MaxLicenses,
                MaxMachines: req.Data.Attributes.MaxMachines,
                Metadata:    req.Data.Attributes.Metadata,
                CreatedAt:   time.Now(),
                UpdatedAt:   time.Now(),
        }

        // Set environment ID if provided
        if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Environment != nil </span><span class="cov0" title="0">{
                group.EnvironmentID = &amp;req.Data.Relationships.Environment.Data.ID
        }</span>

        // Save group to repository
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Group().Create(c.Request.Context(), group); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to create group: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventGroupCreated,
                account,
                events.MakeEventResource(group),
                events.EventMeta{},
        )

        response := gin.H{
                "data": group,
        }

        c.Header("Location", "/api/v1/groups/"+group.ID)
        c.JSON(http.StatusCreated, response)</span>
}

func (h *GroupHandler) UpdateGroupHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">groupIDStr := c.Param("id")
        groupID, err := uuid.Parse(groupIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid group ID format")
                return
        }</span>

        <span class="cov0" title="0">var req GroupRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Get existing group
        <span class="cov0" title="0">group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Group not found")
                return
        }</span>

        // Verify group belongs to the account
        <span class="cov0" title="0">if group.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Group not found")
                return
        }</span>

        // Update group fields
        <span class="cov0" title="0">group.Name = req.Data.Attributes.Name
        if req.Data.Attributes.MaxUsers != nil </span><span class="cov0" title="0">{
                group.MaxUsers = req.Data.Attributes.MaxUsers
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxLicenses != nil </span><span class="cov0" title="0">{
                group.MaxLicenses = req.Data.Attributes.MaxLicenses
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxMachines != nil </span><span class="cov0" title="0">{
                group.MaxMachines = req.Data.Attributes.MaxMachines
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Metadata != nil </span><span class="cov0" title="0">{
                group.Metadata = req.Data.Attributes.Metadata
        }</span>
        <span class="cov0" title="0">group.UpdatedAt = time.Now()

        // Save to repository
        if err := h.serviceCoordinator.Repositories.Group().Update(c.Request.Context(), group); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to update group: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventGroupUpdated,
                account,
                events.MakeEventResource(group),
                events.EventMeta{},
        )

        response := gin.H{
                "data": group,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *GroupHandler) DeleteGroupHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">groupIDStr := c.Param("id")
        groupID, err := uuid.Parse(groupIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid group ID format")
                return
        }</span>

        // Get group from repository
        <span class="cov0" title="0">group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Group not found")
                return
        }</span>

        // Verify group belongs to the account
        <span class="cov0" title="0">if group.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Group not found")
                return
        }</span>

        // Delete group
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Group().Delete(c.Request.Context(), groupID); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to delete group: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventGroupDeleted,
                account,
                events.MakeEventResource(group),
                events.EventMeta{},
        )

        c.Status(http.StatusNoContent)</span>
}</pre>
		
		<pre class="file" id="file3" style="display: none">package handlers

import (
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/gokeys/gokeys/internal/domain/services/events"
        "github.com/google/uuid"
)

// LicenseHandler handles license-related HTTP requests
type LicenseHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

// NewLicenseHandler creates a new license handler
func NewLicenseHandler(serviceCoordinator *services.ServiceCoordinator) *LicenseHandler <span class="cov0" title="0">{
        return &amp;LicenseHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

// ValidateLicenseRequest represents the license validation request
type ValidateLicenseRequest struct {
        LicenseKey         string                 `json:"license_key" binding:"required" example:"LIC-12345-ABCDE-67890-FGHIJ"`
        MachineFingerprint *string                `json:"machine_fingerprint,omitempty" example:"fp-mac-12345678"`
        Environment        *string                `json:"environment,omitempty" example:"production"`
        MachineInfo        map[string]interface{} `json:"machine_info,omitempty" example:"{\"hostname\":\"server-01\",\"os\":\"linux\"}"`
}

// ValidateLicenseResponse represents the license validation response
type ValidateLicenseResponse struct {
        Valid           bool                   `json:"valid" example:"true"`
        License         interface{}            `json:"license,omitempty"`
        Policy          interface{}            `json:"policy,omitempty"`
        Account         interface{}            `json:"account,omitempty"`
        ValidationTime  time.Time              `json:"validation_time" example:"2025-07-12T16:15:30Z"`
        ExpiresAt       *time.Time             `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
        MachinesUsed    int                    `json:"machines_used" example:"2"`
        MachinesAllowed int                    `json:"machines_allowed" example:"5"`
        Claims          map[string]interface{} `json:"claims,omitempty"`
        Errors          []string               `json:"errors,omitempty"`
        Warnings        []string               `json:"warnings,omitempty"`
        CacheHit        bool                   `json:"cache_hit,omitempty" example:"false"`
}

// LicenseInfoResponse represents license information response
type LicenseInfoResponse struct {
        Format    string                 `json:"format"`
        Status    string                 `json:"status,omitempty"`
        AccountID *uuid.UUID             `json:"account_id,omitempty"`
        ProductID *uuid.UUID             `json:"product_id,omitempty"`
        ExpiresAt *time.Time             `json:"expires_at,omitempty"`
        Expired   bool                   `json:"expired,omitempty"`
        Suspended bool                   `json:"suspended,omitempty"`
        Claims    map[string]interface{} `json:"claims,omitempty"`
}

// ValidatePostHandler handles POST /api/v1/licenses/validate
func (lh *LicenseHandler) ValidatePostHandler(c *gin.Context) <span class="cov0" title="0">{
        var req ValidateLicenseRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Perform license validation
        <span class="cov0" title="0">result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
                c.Request.Context(),
                req.LicenseKey,
                req.MachineFingerprint,
                req.Environment,
        )
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to validate license: "+err.Error())
                return
        }</span>

        // Convert to response format
        <span class="cov0" title="0">response := &amp;ValidateLicenseResponse{
                Valid:           result.Valid,
                ValidationTime:  result.ValidationTime,
                ExpiresAt:       result.ExpiresAt,
                MachinesUsed:    result.MachinesUsed,
                MachinesAllowed: result.MachinesAllowed,
                Claims:          result.Claims,
                Errors:          result.Errors,
                Warnings:        result.Warnings,
        }

        // Include entity details if validation was successful
        if result.Valid </span><span class="cov0" title="0">{
                if result.License != nil </span><span class="cov0" title="0">{
                        response.License = sanitizeLicense(result.License)
                }</span>
                <span class="cov0" title="0">if result.Policy != nil </span><span class="cov0" title="0">{
                        response.Policy = sanitizePolicy(result.Policy)
                }</span>
                <span class="cov0" title="0">if result.Account != nil </span><span class="cov0" title="0">{
                        response.Account = sanitizeAccount(result.Account)
                }</span>
        }

        // Broadcast validation event (Ruby: BroadcastEventService.call)
        <span class="cov0" title="0">if result.License != nil &amp;&amp; result.Account != nil </span><span class="cov0" title="0">{
                eventType := events.EventLicenseValidationSucceeded
                if !result.Valid </span><span class="cov0" title="0">{
                        eventType = events.EventLicenseValidationFailed
                }</span>

                <span class="cov0" title="0">meta := events.EventMeta{}
                if len(result.Errors) &gt; 0 </span><span class="cov0" title="0">{
                        meta["code"] = result.Errors[0] // First error as code
                }</span>

                // Broadcast the event
                <span class="cov0" title="0">if err := lh.serviceCoordinator.Events.BroadcastEvent(
                        c.Request.Context(),
                        eventType,
                        result.Account,
                        events.MakeEventResource(result.License),
                        meta,
                ); err != nil </span>{<span class="cov0" title="0">
                        // Log error but don't fail the validation response
                        // Ruby logs this error but continues
                        // Log error but don't fail the validation response
                }</span>
        }

        <span class="cov0" title="0">status := http.StatusOK
        if !result.Valid </span><span class="cov0" title="0">{
                status = http.StatusForbidden
        }</span>

        <span class="cov0" title="0">c.JSON(status, response)</span>
}

// ValidateGetHandler handles GET /api/v1/licenses/validate
func (lh *LicenseHandler) ValidateGetHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseKey := c.Query("license_key")
        if licenseKey == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{
                        "error":   "missing_license_key",
                        "message": "license_key query parameter is required",
                })
                return
        }</span>

        <span class="cov0" title="0">machineFingerprint := c.Query("machine_fingerprint")
        environment := c.Query("environment")

        var machineFingerprintPtr *string
        var environmentPtr *string

        if machineFingerprint != "" </span><span class="cov0" title="0">{
                machineFingerprintPtr = &amp;machineFingerprint
        }</span>
        <span class="cov0" title="0">if environment != "" </span><span class="cov0" title="0">{
                environmentPtr = &amp;environment
        }</span>

        // Perform license validation
        <span class="cov0" title="0">result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
                c.Request.Context(),
                licenseKey,
                machineFingerprintPtr,
                environmentPtr,
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{
                        "error":   "validation_error",
                        "message": "Failed to validate license",
                        "details": err.Error(),
                })
                return
        }</span>

        // Convert to response format
        <span class="cov0" title="0">response := &amp;ValidateLicenseResponse{
                Valid:           result.Valid,
                ValidationTime:  result.ValidationTime,
                ExpiresAt:       result.ExpiresAt,
                MachinesUsed:    result.MachinesUsed,
                MachinesAllowed: result.MachinesAllowed,
                Claims:          result.Claims,
                Errors:          result.Errors,
                Warnings:        result.Warnings,
        }

        // Include entity details if validation was successful
        if result.Valid </span><span class="cov0" title="0">{
                if result.License != nil </span><span class="cov0" title="0">{
                        response.License = sanitizeLicense(result.License)
                }</span>
                <span class="cov0" title="0">if result.Policy != nil </span><span class="cov0" title="0">{
                        response.Policy = sanitizePolicy(result.Policy)
                }</span>
                <span class="cov0" title="0">if result.Account != nil </span><span class="cov0" title="0">{
                        response.Account = sanitizeAccount(result.Account)
                }</span>
        }

        <span class="cov0" title="0">status := http.StatusOK
        if !result.Valid </span><span class="cov0" title="0">{
                status = http.StatusForbidden
        }</span>

        <span class="cov0" title="0">c.JSON(status, response)</span>
}

// QuickValidateHandler handles GET /api/v1/licenses/quick-validate
func (lh *LicenseHandler) QuickValidateHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseKey := c.Query("license_key")
        if licenseKey == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{
                        "error":   "missing_license_key",
                        "message": "license_key query parameter is required",
                })
                return
        }</span>

        // Perform quick validation
        <span class="cov0" title="0">valid, err := lh.serviceCoordinator.LicenseValidation.ValidateLicenseQuick(
                c.Request.Context(),
                licenseKey,
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{
                        "error":   "validation_error",
                        "message": "Failed to validate license",
                        "details": err.Error(),
                })
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "valid":           valid,
                "validation_time": time.Now(),
        }

        status := http.StatusOK
        if !valid </span><span class="cov0" title="0">{
                status = http.StatusForbidden
        }</span>

        <span class="cov0" title="0">c.JSON(status, response)</span>
}

// InfoHandler handles GET /api/v1/licenses/info
func (lh *LicenseHandler) InfoHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseKey := c.Query("license_key")
        if licenseKey == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{
                        "error":   "missing_license_key",
                        "message": "license_key query parameter is required",
                })
                return
        }</span>

        // Get license information
        <span class="cov0" title="0">info, err := lh.serviceCoordinator.LicenseValidation.GetLicenseInfo(
                c.Request.Context(),
                licenseKey,
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{
                        "error":   "info_error",
                        "message": "Failed to get license information",
                        "details": err.Error(),
                })
                return
        }</span>

        // Convert to response format
        <span class="cov0" title="0">response := &amp;LicenseInfoResponse{
                Claims: info,
        }

        if format, ok := info["format"].(string); ok </span><span class="cov0" title="0">{
                response.Format = format
        }</span>
        <span class="cov0" title="0">if status, ok := info["status"].(string); ok </span><span class="cov0" title="0">{
                response.Status = status
        }</span>
        <span class="cov0" title="0">if accountIDStr, ok := info["account_id"].(string); ok </span><span class="cov0" title="0">{
                if accountID, err := uuid.Parse(accountIDStr); err == nil </span><span class="cov0" title="0">{
                        response.AccountID = &amp;accountID
                }</span>
        }
        <span class="cov0" title="0">if productIDStr, ok := info["product_id"].(string); ok </span><span class="cov0" title="0">{
                if productID, err := uuid.Parse(productIDStr); err == nil </span><span class="cov0" title="0">{
                        response.ProductID = &amp;productID
                }</span>
        }
        <span class="cov0" title="0">if expiresAt, ok := info["expires_at"].(*time.Time); ok </span><span class="cov0" title="0">{
                response.ExpiresAt = expiresAt
        }</span>
        <span class="cov0" title="0">if expired, ok := info["expired"].(bool); ok </span><span class="cov0" title="0">{
                response.Expired = expired
        }</span>
        <span class="cov0" title="0">if suspended, ok := info["suspended"].(bool); ok </span><span class="cov0" title="0">{
                response.Suspended = suspended
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, response)</span>
}

// InvalidateCacheHandler handles POST /api/v1/licenses/invalidate-cache
func (lh *LicenseHandler) InvalidateCacheHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseKey := c.Query("license_key")
        if licenseKey == "" </span><span class="cov0" title="0">{
                var req struct {
                        LicenseKey string `json:"license_key" binding:"required"`
                }
                if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                        c.JSON(http.StatusBadRequest, gin.H{
                                "error":   "invalid_request",
                                "message": "license_key is required in query parameter or request body",
                        })
                        return
                }</span>
                <span class="cov0" title="0">licenseKey = req.LicenseKey</span>
        }

        // Invalidate cache
        <span class="cov0" title="0">err := lh.serviceCoordinator.LicenseValidation.InvalidateCache(
                c.Request.Context(),
                licenseKey,
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{
                        "error":   "cache_error",
                        "message": "Failed to invalidate cache",
                        "details": err.Error(),
                })
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "message":     "Cache invalidated successfully",
                "license_key": licenseKey,
        })</span>
}

// ListLicensesHandler handles GET /api/v1/licenses
func (lh *LicenseHandler) ListLicensesHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusUnauthorized, gin.H{
                        "error":   "authentication_required",
                        "message": "Account ID not found in token",
                })
                return
        }</span>

        // Parse query parameters
        <span class="cov0" title="0">page := 1
        if pageStr := c.Query("page"); pageStr != "" </span><span class="cov0" title="0">{
                if p, err := strconv.Atoi(pageStr); err == nil &amp;&amp; p &gt; 0 </span><span class="cov0" title="0">{
                        page = p
                }</span>
        }

        <span class="cov0" title="0">pageSize := 20
        if pageSizeStr := c.Query("page_size"); pageSizeStr != "" </span><span class="cov0" title="0">{
                if ps, err := strconv.Atoi(pageSizeStr); err == nil &amp;&amp; ps &gt; 0 &amp;&amp; ps &lt;= 100 </span><span class="cov0" title="0">{
                        pageSize = ps
                }</span>
        }

        // Build filter
        <span class="cov0" title="0">filter := repositories.ListFilter{
                Page:      page,
                PageSize:  pageSize,
                SortBy:    c.DefaultQuery("sort_by", "created_at"),
                SortOrder: c.DefaultQuery("sort_order", "DESC"),
                Search:    c.Query("search"),
                AccountID: &amp;accountID,
                Filters:   make(map[string]interface{}),
        }

        // Add status filter
        if status := c.Query("status"); status != "" </span><span class="cov0" title="0">{
                filter.Filters["status = ?"] = status
        }</span>

        // Add product filter
        <span class="cov0" title="0">if productID := c.Query("product_id"); productID != "" </span><span class="cov0" title="0">{
                if pid, err := uuid.Parse(productID); err == nil </span><span class="cov0" title="0">{
                        filter.Filters["product_id = ?"] = pid
                }</span>
        }

        // Get licenses
        <span class="cov0" title="0">licenses, total, err := lh.serviceCoordinator.Repositories.License().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{
                        "error":   "database_error",
                        "message": "Failed to retrieve licenses",
                        "details": err.Error(),
                })
                return
        }</span>

        // Sanitize response
        <span class="cov0" title="0">sanitizedLicenses := make([]interface{}, len(licenses))
        for i, license := range licenses </span><span class="cov0" title="0">{
                sanitizedLicenses[i] = sanitizeLicense(license)
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "licenses": sanitizedLicenses,
                "pagination": gin.H{
                        "page":        page,
                        "page_size":   pageSize,
                        "total":       total,
                        "total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
                },
        })</span>
}

// GetLicenseHandler handles GET /api/v1/licenses/:id
func (lh *LicenseHandler) GetLicenseHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseIDStr := c.Param("id")
        licenseID, err := uuid.Parse(licenseIDStr)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{
                        "error":   "invalid_license_id",
                        "message": "Invalid license ID format",
                })
                return
        }</span>

        // Get license
        <span class="cov0" title="0">license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusNotFound, gin.H{
                        "error":   "license_not_found",
                        "message": "License not found",
                })
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{
                "license": sanitizeLicense(license),
        })</span>
}

// ValidateByIDHandler handles license validation by ID (Ruby: validate_by_id)
func (lh *LicenseHandler) ValidateByIDHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseID := c.Param("id")
        if licenseID == "" </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "License ID is required")
                return
        }</span>

        // Get account ID from authentication context
        <span class="cov0" title="0">accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        // Parse license UUID
        <span class="cov0" title="0">licenseUUID, err := uuid.Parse(licenseID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid license ID format")
                return
        }</span>

        // Get license by ID
        <span class="cov0" title="0">license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseUUID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        // Verify license belongs to the account
        <span class="cov0" title="0">if license.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        // Parse query parameters for validation options
        <span class="cov0" title="0">machineFingerprint := c.Query("machine_fingerprint")
        environment := c.Query("environment")
        skipTouch := c.GetHeader("Origin") == "https://app.keygen.sh"

        var machineFingerprintPtr *string
        var environmentPtr *string

        if machineFingerprint != "" </span><span class="cov0" title="0">{
                machineFingerprintPtr = &amp;machineFingerprint
        }</span>
        <span class="cov0" title="0">if environment != "" </span><span class="cov0" title="0">{
                environmentPtr = &amp;environment
        }</span>

        // Perform license validation with scope
        <span class="cov0" title="0">result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
                c.Request.Context(),
                license.Key,
                machineFingerprintPtr,
                environmentPtr,
        )
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to validate license: "+err.Error())
                return
        }</span>

        // Convert to response format
        <span class="cov0" title="0">response := &amp;ValidateLicenseResponse{
                Valid:           result.Valid,
                ValidationTime:  result.ValidationTime,
                ExpiresAt:       result.ExpiresAt,
                MachinesUsed:    result.MachinesUsed,
                MachinesAllowed: result.MachinesAllowed,
                Claims:          result.Claims,
                Errors:          result.Errors,
                Warnings:        result.Warnings,
        }

        // Include entity details if validation was successful
        if result.Valid </span><span class="cov0" title="0">{
                if result.License != nil </span><span class="cov0" title="0">{
                        response.License = sanitizeLicense(result.License)
                }</span>
                <span class="cov0" title="0">if result.Policy != nil </span><span class="cov0" title="0">{
                        response.Policy = sanitizePolicy(result.Policy)
                }</span>
                <span class="cov0" title="0">if result.Account != nil </span><span class="cov0" title="0">{
                        response.Account = sanitizeAccount(result.Account)
                }</span>
        }

        // Broadcast validation event if not skip_touch
        <span class="cov0" title="0">if !skipTouch &amp;&amp; result.License != nil &amp;&amp; result.Account != nil </span><span class="cov0" title="0">{
                eventType := events.EventLicenseValidationSucceeded
                if !result.Valid </span><span class="cov0" title="0">{
                        eventType = events.EventLicenseValidationFailed
                }</span>

                <span class="cov0" title="0">meta := events.EventMeta{}
                if len(result.Errors) &gt; 0 </span><span class="cov0" title="0">{
                        meta["code"] = result.Errors[0]
                }</span>

                <span class="cov0" title="0">lh.serviceCoordinator.Events.BroadcastEvent(
                        c.Request.Context(),
                        eventType,
                        result.Account,
                        events.MakeEventResource(result.License),
                        meta,
                )</span>
        }

        <span class="cov0" title="0">status := http.StatusOK
        if !result.Valid </span><span class="cov0" title="0">{
                status = http.StatusForbidden
        }</span>

        <span class="cov0" title="0">c.JSON(status, response)</span>
}

// QuickValidateByIDHandler handles quick license validation by ID (Ruby: quick_validate_by_id)
func (lh *LicenseHandler) QuickValidateByIDHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseID := c.Param("id")
        if licenseID == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "License ID is required"})
                return
        }</span>

        // Get account ID from authentication context
        <span class="cov0" title="0">accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        // Parse license UUID
        <span class="cov0" title="0">licenseUUID, err := uuid.Parse(licenseID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid license ID format")
                return
        }</span>

        // Get license by ID
        <span class="cov0" title="0">license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseUUID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        // Verify license belongs to the account
        <span class="cov0" title="0">if license.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        // Ruby: skip_touch if origin header is https://app.keygen.sh
        // Skip touch if origin header is https://app.keygen.sh (used in validation logic)
        <span class="cov0" title="0">_ = c.GetHeader("Origin") == "https://app.keygen.sh"

        // Quick validation without scope (Ruby: LicenseValidationService.call(license: license, scope: false, skip_touch: skip_touch))
        result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicenseQuick(
                c.Request.Context(),
                license.Key,
        )
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to validate license: "+err.Error())
                return
        }</span>

        // Ruby response format
        <span class="cov0" title="0">response := gin.H{
                "data": gin.H{
                        "id":   licenseID,
                        "type": "licenses",
                        "attributes": gin.H{
                                "key":        license.Key,
                                "name":       license.Name,
                                "status":     license.Status,
                                "suspended":  license.Suspended,
                                "expires_at": license.ExpiresAt,
                                "created_at": license.CreatedAt,
                                "updated_at": license.UpdatedAt,
                        },
                },
                "meta": gin.H{
                        "ts":     time.Now(),
                        "valid":  result,
                        "detail": getValidationDetail(result, nil),
                        "code":   getValidationCode(result, nil),
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

// ValidateByKeyHandler handles license validation by key (Ruby: validate_by_key)
func (lh *LicenseHandler) ValidateByKeyHandler(c *gin.Context) <span class="cov0" title="0">{
        var req struct {
                Meta *struct {
                        Key   string      `json:"key" binding:"required"`
                        Nonce *string     `json:"nonce,omitempty"`
                        Scope interface{} `json:"scope,omitempty"`
                } `json:"meta"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // Perform license validation by key
        <span class="cov0" title="0">result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
                c.Request.Context(),
                req.Meta.Key,
                nil, // No machine fingerprint for this endpoint
                nil, // No environment for this endpoint
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate license: " + err.Error()})
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": gin.H{
                        "key": req.Meta.Key,
                },
                "meta": gin.H{
                        "ts":     time.Now(),
                        "valid":  result.Valid,
                        "detail": getValidationDetail(result.Valid, result.Errors),
                        "code":   getValidationCode(result.Valid, result.Errors),
                },
        }

        if req.Meta.Nonce != nil </span><span class="cov0" title="0">{
                response["meta"].(gin.H)["nonce"] = *req.Meta.Nonce
        }</span>
        <span class="cov0" title="0">if req.Meta.Scope != nil </span><span class="cov0" title="0">{
                response["meta"].(gin.H)["scope"] = req.Meta.Scope
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, response)</span>
}

// CheckoutLicenseHandler handles license checkout (Ruby: LicenseCheckoutService)
func (lh *LicenseHandler) CheckoutLicenseHandler(c *gin.Context) <span class="cov0" title="0">{
        licenseID := c.Param("id")
        if licenseID == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "License ID is required"})
                return
        }</span>

        // Parse query parameters for checkout options
        <span class="cov0" title="0">var options struct {
                Encrypt   bool     `form:"encrypt"`
                Algorithm string   `form:"algorithm"`
                TTL       *int     `form:"ttl"`
                Include   []string `form:"include"`
        }

        if err := c.ShouldBindQuery(&amp;options); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // For POST requests, also check body
        <span class="cov0" title="0">if c.Request.Method == "POST" </span><span class="cov0" title="0">{
                var req struct {
                        Meta *struct {
                                Encrypt   *bool    `json:"encrypt,omitempty"`
                                Algorithm *string  `json:"algorithm,omitempty"`
                                TTL       *int     `json:"ttl,omitempty"`
                                Include   []string `json:"include,omitempty"`
                        } `json:"meta,omitempty"`
                }

                if err := c.ShouldBindJSON(&amp;req); err == nil &amp;&amp; req.Meta != nil </span><span class="cov0" title="0">{
                        if req.Meta.Encrypt != nil </span><span class="cov0" title="0">{
                                options.Encrypt = *req.Meta.Encrypt
                        }</span>
                        <span class="cov0" title="0">if req.Meta.Algorithm != nil </span><span class="cov0" title="0">{
                                options.Algorithm = *req.Meta.Algorithm
                        }</span>
                        <span class="cov0" title="0">if req.Meta.TTL != nil </span><span class="cov0" title="0">{
                                options.TTL = req.Meta.TTL
                        }</span>
                        <span class="cov0" title="0">if len(req.Meta.Include) &gt; 0 </span><span class="cov0" title="0">{
                                options.Include = req.Meta.Include
                        }</span>
                }
        }

        // Implement license checkout using checkout service
        // This should generate a signed license certificate

        // For GET request, return certificate as attachment
        <span class="cov0" title="0">if c.Request.Method == "GET" </span><span class="cov0" title="0">{
                certificateData := "PLACEHOLDER_CERTIFICATE_DATA" // Actual certificate from checkout service
                c.Header("Content-Disposition", `attachment; filename="`+licenseID+`.lic"`)
                c.Header("Content-Type", "application/octet-stream")
                c.String(http.StatusOK, certificateData)
                return
        }</span>

        // For POST request, return JSON response
        <span class="cov0" title="0">response := gin.H{
                "data": gin.H{
                        "id":          licenseID,
                        "certificate": "PLACEHOLDER_CERTIFICATE_DATA", // Actual certificate from checkout service
                        "algorithm":   options.Algorithm,
                        "encrypted":   options.Encrypt,
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

// CreateLicenseHandler handles POST /api/v1/licenses
func (lh *LicenseHandler) CreateLicenseHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">var req struct {
                Data struct {
                        Type       string `json:"type" binding:"required"`
                        Attributes struct {
                                Name     string                 `json:"name"`
                                Key      string                 `json:"key,omitempty"`
                                Metadata map[string]interface{} `json:"metadata,omitempty"`
                        } `json:"attributes"`
                        Relationships *struct {
                                Policy *struct {
                                        Data struct {
                                                Type string `json:"type"`
                                                ID   string `json:"id"`
                                        } `json:"data"`
                                } `json:"policy,omitempty"`
                                User *struct {
                                        Data struct {
                                                Type string `json:"type"`
                                                ID   string `json:"id"`
                                        } `json:"data"`
                                } `json:"user,omitempty"`
                        } `json:"relationships,omitempty"`
                } `json:"data"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Generate license key if not provided
        <span class="cov0" title="0">licenseKey := req.Data.Attributes.Key
        if licenseKey == "" </span><span class="cov0" title="0">{
                licenseKey = "LIC-" + uuid.New().String()[:8] + "-" + uuid.New().String()[:8]
        }</span>

        // Create license entity
        <span class="cov0" title="0">license := &amp;entities.License{
                AccountID: accountID.String(),
                Name:      req.Data.Attributes.Name,
                Key:       licenseKey,
                Metadata:  req.Data.Attributes.Metadata,
                Status:    "ACTIVE",
                CreatedAt: time.Now(),
                UpdatedAt: time.Now(),
        }

        // Set policy ID if provided
        if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Policy != nil </span><span class="cov0" title="0">{
                license.PolicyID = req.Data.Relationships.Policy.Data.ID
        }</span>

        // Set user ID if provided
        <span class="cov0" title="0">if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.User != nil </span><span class="cov0" title="0">{
                license.UserID = &amp;req.Data.Relationships.User.Data.ID
        }</span>

        // Create license
        <span class="cov0" title="0">if err := lh.serviceCoordinator.Repositories.License().Create(c.Request.Context(), license); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to create license: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := lh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        lh.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventLicenseCreated,
                account,
                events.MakeEventResource(license),
                events.EventMeta{},
        )

        c.JSON(http.StatusCreated, gin.H{
                "data": sanitizeLicense(license),
        })</span>
}

// UpdateLicenseHandler handles PUT /api/v1/licenses/:id
func (lh *LicenseHandler) UpdateLicenseHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">licenseIDStr := c.Param("id")
        licenseID, err := uuid.Parse(licenseIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid license ID format")
                return
        }</span>

        // Get existing license
        <span class="cov0" title="0">license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        // Verify license belongs to the account
        <span class="cov0" title="0">if license.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        <span class="cov0" title="0">var req struct {
                Data struct {
                        Type       string `json:"type" binding:"required"`
                        Attributes struct {
                                Name     *string                `json:"name,omitempty"`
                                Status   *string                `json:"status,omitempty"`
                                Metadata map[string]interface{} `json:"metadata,omitempty"`
                        } `json:"attributes"`
                } `json:"data"`
        }

        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Update fields
        <span class="cov0" title="0">if req.Data.Attributes.Name != nil </span><span class="cov0" title="0">{
                license.Name = *req.Data.Attributes.Name
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Status != nil </span><span class="cov0" title="0">{
                license.Status = entities.LicenseStatus(*req.Data.Attributes.Status)
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Metadata != nil </span><span class="cov0" title="0">{
                license.Metadata = req.Data.Attributes.Metadata
        }</span>
        <span class="cov0" title="0">license.UpdatedAt = time.Now()

        // Update license
        if err := lh.serviceCoordinator.Repositories.License().Update(c.Request.Context(), license); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to update license: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := lh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        lh.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventLicenseUpdated,
                account,
                events.MakeEventResource(license),
                events.EventMeta{},
        )

        c.JSON(http.StatusOK, gin.H{
                "data": sanitizeLicense(license),
        })</span>
}

// DeleteLicenseHandler handles DELETE /api/v1/licenses/:id
func (lh *LicenseHandler) DeleteLicenseHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">licenseIDStr := c.Param("id")
        licenseID, err := uuid.Parse(licenseIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid license ID format")
                return
        }</span>

        // Get existing license
        <span class="cov0" title="0">license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        // Verify license belongs to the account
        <span class="cov0" title="0">if license.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "License not found")
                return
        }</span>

        // Delete license
        <span class="cov0" title="0">if err := lh.serviceCoordinator.Repositories.License().Delete(c.Request.Context(), licenseID); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to delete license: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := lh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        lh.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventLicenseDeleted,
                account,
                events.MakeEventResource(license),
                events.EventMeta{},
        )

        c.Status(http.StatusNoContent)</span>
}

// Sanitization functions to remove sensitive data from responses

func sanitizeLicense(license interface{}) interface{} <span class="cov0" title="0">{
        // This would normally convert the license entity to a safe response format
        // removing sensitive fields like internal IDs, secrets, etc.
        return license
}</span>

func sanitizePolicy(policy interface{}) interface{} <span class="cov0" title="0">{
        // This would normally convert the policy entity to a safe response format
        return policy
}</span>

func sanitizeAccount(account interface{}) interface{} <span class="cov0" title="0">{
        // This would normally convert the account entity to a safe response format
        // removing sensitive fields like private keys, secrets, etc.
        return account
}</span>

// Helper functions for validation response mapping

// getValidationDetail returns validation detail string based on result (Ruby: detail)
func getValidationDetail(valid bool, errors []string) string <span class="cov0" title="0">{
        if valid </span><span class="cov0" title="0">{
                return "valid"
        }</span>
        <span class="cov0" title="0">if len(errors) &gt; 0 </span><span class="cov0" title="0">{
                // Map common error types to Ruby detail strings
                for _, err := range errors </span><span class="cov0" title="0">{
                        switch err </span>{
                        case "LICENSE_NOT_FOUND":<span class="cov0" title="0">
                                return "not found"</span>
                        case "LICENSE_EXPIRED":<span class="cov0" title="0">
                                return "expired"</span>
                        case "LICENSE_SUSPENDED":<span class="cov0" title="0">
                                return "suspended"</span>
                        case "USER_BANNED":<span class="cov0" title="0">
                                return "banned"</span>
                        case "LICENSE_OVERDUE":<span class="cov0" title="0">
                                return "overdue"</span>
                        case "TOO_MANY_MACHINES":<span class="cov0" title="0">
                                return "too many machines"</span>
                        case "FINGERPRINT_SCOPE_MISMATCH":<span class="cov0" title="0">
                                return "fingerprint scope mismatch"</span>
                        case "ENVIRONMENT_SCOPE_MISMATCH":<span class="cov0" title="0">
                                return "environment scope mismatch"</span>
                        }
                }
        }
        <span class="cov0" title="0">return "invalid"</span>
}

// getValidationCode returns validation code based on result (Ruby: code)
func getValidationCode(valid bool, errors []string) string <span class="cov0" title="0">{
        if valid </span><span class="cov0" title="0">{
                return "VALID"
        }</span>
        <span class="cov0" title="0">if len(errors) &gt; 0 </span><span class="cov0" title="0">{
                // Return the first error as code
                return errors[0]
        }</span>
        <span class="cov0" title="0">return "INVALID"</span>
}</pre>
		
		<pre class="file" id="file4" style="display: none">package handlers

import (
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/gokeys/gokeys/internal/domain/services/events"
        "github.com/google/uuid"
)

type MachineHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewMachineHandler(serviceCoordinator *services.ServiceCoordinator) *MachineHandler <span class="cov0" title="0">{
        return &amp;MachineHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

type MachineRequest struct {
        Data struct {
                Type       string `json:"type" binding:"required"`
                Attributes struct {
                        Name        string                 `json:"name,omitempty"`
                        Fingerprint string                 `json:"fingerprint" binding:"required"`
                        Platform    *string                `json:"platform,omitempty"`
                        Hostname    *string                `json:"hostname,omitempty"`
                        IP          *string                `json:"ip,omitempty"`
                        Cores       *int                   `json:"cores,omitempty"`
                        Metadata    map[string]interface{} `json:"metadata,omitempty"`
                } `json:"attributes"`
                Relationships *struct {
                        License *struct {
                                Data *struct {
                                        Type string `json:"type"`
                                        ID   string `json:"id"`
                                } `json:"data"`
                        } `json:"license,omitempty"`
                } `json:"relationships,omitempty"`
        } `json:"data"`
}

// ListMachinesHandler handles GET /api/v1/machines (Ruby: MachinesController#index)
func (h *MachineHandler) ListMachinesHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        // Parse pagination
        <span class="cov0" title="0">page := 1
        limit := 25
        if p := c.Query("page"); p != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(p); err == nil &amp;&amp; parsed &gt; 0 </span><span class="cov0" title="0">{
                        page = parsed
                }</span>
        }
        <span class="cov0" title="0">if l := c.Query("limit"); l != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(l); err == nil &amp;&amp; parsed &gt; 0 &amp;&amp; parsed &lt;= 100 </span><span class="cov0" title="0">{
                        limit = parsed
                }</span>
        }

        // Build filter (Ruby: has_scope filters)
        <span class="cov0" title="0">filter := repositories.ListFilter{
                Page:      page,
                PageSize:  limit,
                SortBy:    c.DefaultQuery("sort_by", "created_at"),
                SortOrder: c.DefaultQuery("sort_order", "DESC"),
                Search:    c.Query("search"),
                AccountID: &amp;accountID,
                Filters:   make(map[string]interface{}),
        }

        // Add license filter if provided
        if licenseID := c.Query("license_id"); licenseID != "" </span><span class="cov0" title="0">{
                filter.Filters["license_id = ?"] = licenseID
        }</span>

        // Get machines from repository
        <span class="cov0" title="0">machines, total, err := h.serviceCoordinator.Repositories.Machine().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to retrieve machines: "+err.Error())
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": machines,
                "meta": gin.H{
                        "page":        page,
                        "limit":       limit,
                        "total":       total,
                        "total_pages": (total + int64(limit) - 1) / int64(limit),
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

// GetMachineHandler handles GET /api/v1/machines/:id (Ruby: MachinesController#show)
func (h *MachineHandler) GetMachineHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">machineID := c.Param("id")
        if machineID == "" </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Machine ID is required")
                return
        }</span>

        // Parse machine UUID (Ruby: FindByAliasService allows ID or fingerprint)
        <span class="cov0" title="0">machineUUID, err := uuid.Parse(machineID)
        if err != nil </span><span class="cov0" title="0">{
                // Try to find by fingerprint if not a valid UUID
                machines, _, err := h.serviceCoordinator.Repositories.Machine().List(c.Request.Context(), repositories.ListFilter{
                        AccountID: &amp;accountID,
                        Filters:   map[string]interface{}{"fingerprint = ?": machineID},
                        PageSize:  1,
                })
                if err != nil || len(machines) == 0 </span><span class="cov0" title="0">{
                        responses.RenderNotFound(c, "Machine not found")
                        return
                }</span>
                <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{"data": machines[0]})
                return</span>
        }

        // Get machine by ID
        <span class="cov0" title="0">machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Verify machine belongs to the account
        <span class="cov0" title="0">if machine.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{"data": machine})</span>
}

// CreateMachineHandler handles POST /api/v1/machines (Ruby: MachinesController#create)
func (h *MachineHandler) CreateMachineHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">var req MachineRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Create machine entity
        <span class="cov0" title="0">machine := &amp;entities.Machine{
                ID:          uuid.New().String(),
                AccountID:   accountID.String(),
                Name:        req.Data.Attributes.Name,
                Fingerprint: req.Data.Attributes.Fingerprint,
                CreatedAt:   time.Now(),
                UpdatedAt:   time.Now(),
        }

        // Set optional fields
        if req.Data.Attributes.Platform != nil </span><span class="cov0" title="0">{
                machine.Platform = *req.Data.Attributes.Platform
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Hostname != nil </span><span class="cov0" title="0">{
                machine.Hostname = *req.Data.Attributes.Hostname
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.IP != nil </span><span class="cov0" title="0">{
                machine.IP = *req.Data.Attributes.IP
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Cores != nil </span><span class="cov0" title="0">{
                machine.Cores = *req.Data.Attributes.Cores
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Metadata != nil </span><span class="cov0" title="0">{
                machine.Metadata = entities.Metadata(req.Data.Attributes.Metadata)
        }</span>

        // Set license relationship if provided
        <span class="cov0" title="0">if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.License != nil &amp;&amp; req.Data.Relationships.License.Data != nil </span><span class="cov0" title="0">{
                machine.LicenseID = req.Data.Relationships.License.Data.ID
        }</span>

        // Save machine to repository
        <span class="cov0" title="0">err = h.serviceCoordinator.Repositories.Machine().Create(c.Request.Context(), machine)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to create machine: "+err.Error())
                return
        }</span>

        // Broadcast machine created event
        <span class="cov0" title="0">account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        if err == nil </span><span class="cov0" title="0">{
                h.serviceCoordinator.Events.BroadcastEvent(
                        c.Request.Context(),
                        events.EventMachineCreated,
                        account,
                        events.MakeEventResource(machine),
                        events.EventMeta{},
                )
        }</span>

        <span class="cov0" title="0">c.Header("Location", "/api/v1/machines/"+machine.ID)
        c.JSON(http.StatusCreated, gin.H{"data": machine})</span>
}

// HeartbeatPingHandler handles POST /api/v1/machines/:id/actions/heartbeats/ping (Ruby: HeartbeatsController#ping)
func (h *MachineHandler) HeartbeatPingHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">machineID := c.Param("id")
        if machineID == "" </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Machine ID is required")
                return
        }</span>

        // Parse machine UUID
        <span class="cov0" title="0">machineUUID, err := uuid.Parse(machineID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid machine ID format")
                return
        }</span>

        // Get machine
        <span class="cov0" title="0">machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Verify machine belongs to the account
        <span class="cov0" title="0">if machine.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Ruby heartbeat logic:
        // - If machine is dead -&gt; resurrect (machine.heartbeat.resurrected)
        // - Else -&gt; ping (machine.heartbeat.ping)
        <span class="cov0" title="0">now := time.Now()
        wasAlive := machine.LastHeartbeatAt != nil

        // Update machine heartbeat
        machine.LastHeartbeatAt = &amp;now
        machine.UpdatedAt = now

        err = h.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to update machine heartbeat: "+err.Error())
                return
        }</span>

        // Broadcast heartbeat event (Ruby: BroadcastEventService.call)
        // Ruby logic: if machine was dead -&gt; resurrect event, else -&gt; ping event
        <span class="cov0" title="0">eventType := events.EventMachineHeartbeatPing
        if !wasAlive </span><span class="cov0" title="0">{
                eventType = events.EventMachineHeartbeatResurrected
        }</span>

        // Get account for event broadcasting
        <span class="cov0" title="0">account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        if err == nil </span><span class="cov0" title="0">{
                if err := h.serviceCoordinator.Events.BroadcastEvent(
                        c.Request.Context(),
                        eventType,
                        account,
                        events.MakeEventResource(machine),
                        events.EventMeta{},
                ); err != nil </span>{<span class="cov0" title="0">
                        // Log error but don't fail the heartbeat response
                        // Log error but don't fail the heartbeat response
                }</span>
        }

        <span class="cov0" title="0">response := gin.H{
                "data": machine,
                "meta": gin.H{
                        "ts":        now,
                        "ping_time": now,
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

// HeartbeatResetHandler handles POST /api/v1/machines/:id/actions/heartbeats/reset (Ruby: HeartbeatsController#reset)
func (h *MachineHandler) HeartbeatResetHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">machineID := c.Param("id")
        if machineID == "" </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Machine ID is required")
                return
        }</span>

        // Parse machine UUID
        <span class="cov0" title="0">machineUUID, err := uuid.Parse(machineID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid machine ID format")
                return
        }</span>

        // Get machine
        <span class="cov0" title="0">machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Verify machine belongs to the account
        <span class="cov0" title="0">if machine.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Reset machine heartbeat (Ruby: sets last_heartbeat_at to nil)
        <span class="cov0" title="0">now := time.Now()
        machine.LastHeartbeatAt = nil
        machine.UpdatedAt = now

        err = h.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to reset machine heartbeat: "+err.Error())
                return
        }</span>

        // Broadcast heartbeat reset event
        <span class="cov0" title="0">account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        if err == nil </span><span class="cov0" title="0">{
                h.serviceCoordinator.Events.BroadcastEvent(
                        c.Request.Context(),
                        events.EventMachineHeartbeatReset,
                        account,
                        events.MakeEventResource(machine),
                        events.EventMeta{},
                )
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": machine,
                "meta": gin.H{
                        "ts":         now,
                        "reset_time": now,
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

// CheckoutMachineHandler handles machine checkout (Ruby: MachineCheckoutService)
func (mh *MachineHandler) CheckoutMachineHandler(c *gin.Context) <span class="cov0" title="0">{
        machineID := c.Param("id")
        if machineID == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Machine ID is required"})
                return
        }</span>

        // Parse query parameters for checkout options
        <span class="cov0" title="0">var options struct {
                Encrypt   bool     `form:"encrypt"`
                Algorithm string   `form:"algorithm"`
                TTL       *int     `form:"ttl"`
                Include   []string `form:"include"`
        }

        if err := c.ShouldBindQuery(&amp;options); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // For POST requests, also check body
        <span class="cov0" title="0">if c.Request.Method == "POST" </span><span class="cov0" title="0">{
                var req struct {
                        Meta *struct {
                                Encrypt   *bool    `json:"encrypt,omitempty"`
                                Algorithm *string  `json:"algorithm,omitempty"`
                                TTL       *int     `json:"ttl,omitempty"`
                                Include   []string `json:"include,omitempty"`
                        } `json:"meta,omitempty"`
                }

                if err := c.ShouldBindJSON(&amp;req); err == nil &amp;&amp; req.Meta != nil </span><span class="cov0" title="0">{
                        if req.Meta.Encrypt != nil </span><span class="cov0" title="0">{
                                options.Encrypt = *req.Meta.Encrypt
                        }</span>
                        <span class="cov0" title="0">if req.Meta.Algorithm != nil </span><span class="cov0" title="0">{
                                options.Algorithm = *req.Meta.Algorithm
                        }</span>
                        <span class="cov0" title="0">if req.Meta.TTL != nil </span><span class="cov0" title="0">{
                                options.TTL = req.Meta.TTL
                        }</span>
                        <span class="cov0" title="0">if len(req.Meta.Include) &gt; 0 </span><span class="cov0" title="0">{
                                options.Include = req.Meta.Include
                        }</span>
                }
        }

        // Implement machine checkout using checkout service
        // This should generate a signed machine certificate

        // For GET request, return certificate as attachment
        <span class="cov0" title="0">if c.Request.Method == "GET" </span><span class="cov0" title="0">{
                certificateData := "PLACEHOLDER_MACHINE_CERTIFICATE_DATA" // Actual certificate from checkout service
                c.Header("Content-Disposition", `attachment; filename="`+machineID+`.lic"`)
                c.Header("Content-Type", "application/octet-stream")
                c.String(http.StatusOK, certificateData)
                return
        }</span>

        // For POST request, return JSON response
        <span class="cov0" title="0">response := gin.H{
                "data": gin.H{
                        "id":          machineID,
                        "certificate": "PLACEHOLDER_MACHINE_CERTIFICATE_DATA", // Actual certificate from checkout service
                        "algorithm":   options.Algorithm,
                        "encrypted":   options.Encrypt,
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

// UpdateMachineHandler handles PUT /api/v1/machines/:id
func (mh *MachineHandler) UpdateMachineHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">machineIDStr := c.Param("id")
        machineID, err := uuid.Parse(machineIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid machine ID format")
                return
        }</span>

        // Get existing machine
        <span class="cov0" title="0">machine, err := mh.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Verify machine belongs to the account
        <span class="cov0" title="0">if machine.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        <span class="cov0" title="0">var req MachineRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Update fields
        <span class="cov0" title="0">if req.Data.Attributes.Name != "" </span><span class="cov0" title="0">{
                machine.Name = req.Data.Attributes.Name
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Platform != nil </span><span class="cov0" title="0">{
                machine.Platform = *req.Data.Attributes.Platform
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Hostname != nil </span><span class="cov0" title="0">{
                machine.Hostname = *req.Data.Attributes.Hostname
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.IP != nil </span><span class="cov0" title="0">{
                machine.IP = *req.Data.Attributes.IP
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Cores != nil </span><span class="cov0" title="0">{
                machine.Cores = *req.Data.Attributes.Cores
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Metadata != nil </span><span class="cov0" title="0">{
                machine.Metadata = req.Data.Attributes.Metadata
        }</span>
        <span class="cov0" title="0">machine.UpdatedAt = time.Now()

        // Update machine
        if err := mh.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to update machine: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := mh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        mh.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventMachineUpdated,
                account,
                events.MakeEventResource(machine),
                events.EventMeta{},
        )

        c.JSON(http.StatusOK, gin.H{
                "data": machine,
        })</span>
}

// DeleteMachineHandler handles DELETE /api/v1/machines/:id
func (mh *MachineHandler) DeleteMachineHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">machineIDStr := c.Param("id")
        machineID, err := uuid.Parse(machineIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid machine ID format")
                return
        }</span>

        // Get existing machine
        <span class="cov0" title="0">machine, err := mh.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Verify machine belongs to the account
        <span class="cov0" title="0">if machine.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Machine not found")
                return
        }</span>

        // Delete machine
        <span class="cov0" title="0">if err := mh.serviceCoordinator.Repositories.Machine().Delete(c.Request.Context(), machineID); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to delete machine: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := mh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        mh.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventMachineDeleted,
                account,
                events.MakeEventResource(machine),
                events.EventMeta{},
        )

        c.Status(http.StatusNoContent)</span>
}</pre>
		
		<pre class="file" id="file5" style="display: none">package handlers

import (
        "net/http"
        "strconv"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/google/uuid"
)

type PlanHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewPlanHandler(serviceCoordinator *services.ServiceCoordinator) *PlanHandler <span class="cov0" title="0">{
        return &amp;PlanHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

// Plans are read-only in most cases, mainly for billing/subscription purposes
func (h *PlanHandler) ListPlansHandler(c *gin.Context) <span class="cov0" title="0">{
        // Parse pagination
        page := 1
        limit := 25
        if p := c.Query("page"); p != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(p); err == nil &amp;&amp; parsed &gt; 0 </span><span class="cov0" title="0">{
                        page = parsed
                }</span>
        }
        <span class="cov0" title="0">if l := c.Query("limit"); l != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(l); err == nil &amp;&amp; parsed &gt; 0 &amp;&amp; parsed &lt;= 100 </span><span class="cov0" title="0">{
                        limit = parsed
                }</span>
        }

        <span class="cov0" title="0">_ = (page - 1) * limit // offset calculation

        // Get visible plans from repository (cached)
        // In Ruby: Plan.visible.reorder('price ASC NULLS FIRST')
        filter := repositories.ListFilter{
                Page:      page,
                PageSize:  limit,
                SortBy:    "price",
                SortOrder: "ASC",
                Filters: map[string]interface{}{
                        "visible = ?": true,
                },
        }

        plans, total, err := h.serviceCoordinator.Repositories.Plan().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to retrieve plans: "+err.Error())
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": plans,
                "meta": gin.H{
                        "page":        page,
                        "limit":       limit,
                        "total":       total,
                        "total_pages": (total + int64(limit) - 1) / int64(limit),
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *PlanHandler) GetPlanHandler(c *gin.Context) <span class="cov0" title="0">{
        planID := c.Param("id")
        if planID == "" </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Plan ID is required"})
                return
        }</span>

        <span class="cov0" title="0">planUUID, err := uuid.Parse(planID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid plan ID format")
                return
        }</span>

        // Get plan from repository by ID
        <span class="cov0" title="0">plan, err := h.serviceCoordinator.Repositories.Plan().GetByID(c.Request.Context(), planUUID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Plan not found")
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": plan,
        }

        c.JSON(http.StatusOK, response)</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package handlers

import (
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/gokeys/gokeys/internal/domain/services/events"
        "github.com/google/uuid"
)

type PolicyHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewPolicyHandler(serviceCoordinator *services.ServiceCoordinator) *PolicyHandler <span class="cov0" title="0">{
        return &amp;PolicyHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

type PolicyRequest struct {
        Data struct {
                Type       string `json:"type" binding:"required"`
                Attributes struct {
                        Name                          string                 `json:"name" binding:"required"`
                        Duration                      *int                   `json:"duration,omitempty"`
                        Strict                        *bool                  `json:"strict,omitempty"`
                        Floating                      *bool                  `json:"floating,omitempty"`
                        Scheme                        *string                `json:"scheme,omitempty"`
                        RequireHeartbeat              *bool                  `json:"require_heartbeat,omitempty"`
                        HeartbeatDuration             *int                   `json:"heartbeat_duration,omitempty"`
                        HeartbeatCullStrategy         *string                `json:"heartbeat_cull_strategy,omitempty"`
                        HeartbeatResurrectionStrategy *string                `json:"heartbeat_resurrection_strategy,omitempty"`
                        HeartbeatBasis                *string                `json:"heartbeat_basis,omitempty"`
                        MachineUniquenessStrategy     *string                `json:"machine_uniqueness_strategy,omitempty"`
                        MachineMatchingStrategy       *string                `json:"machine_matching_strategy,omitempty"`
                        ComponentsStrategy            *string                `json:"components_strategy,omitempty"`
                        ComponentsFingerprint         *string                `json:"components_fingerprint,omitempty"`
                        ExpirationStrategy            *string                `json:"expiration_strategy,omitempty"`
                        ExpirationBasis               *string                `json:"expiration_basis,omitempty"`
                        RenewalBasis                  *string                `json:"renewal_basis,omitempty"`
                        TransferStrategy              *string                `json:"transfer_strategy,omitempty"`
                        AuthenticationStrategy        *string                `json:"authentication_strategy,omitempty"`
                        MachineLeasingStrategy        *string                `json:"machine_leasing_strategy,omitempty"`
                        ProcessLeasingStrategy        *string                `json:"process_leasing_strategy,omitempty"`
                        OverageStrategy               *string                `json:"overage_strategy,omitempty"`
                        MaxMachines                   *int                   `json:"max_machines,omitempty"`
                        MaxProcesses                  *int                   `json:"max_processes,omitempty"`
                        MaxUsers                      *int                   `json:"max_users,omitempty"`
                        MaxCores                      *int                   `json:"max_cores,omitempty"`
                        MaxUses                       *int                   `json:"max_uses,omitempty"`
                        Protected                     *bool                  `json:"protected,omitempty"`
                        RequireCheckIn                *bool                  `json:"require_check_in,omitempty"`
                        CheckInInterval               *string                `json:"check_in_interval,omitempty"`
                        CheckInIntervalCount          *int                   `json:"check_in_interval_count,omitempty"`
                        UsePool                       *bool                  `json:"use_pool,omitempty"`
                        MaxActivations                *int                   `json:"max_activations,omitempty"`
                        MaxDeactivations              *int                   `json:"max_deactivations,omitempty"`
                        Metadata                      map[string]interface{} `json:"metadata,omitempty"`
                } `json:"attributes"`
                Relationships *struct {
                        Product *struct {
                                Data *struct {
                                        Type string `json:"type"`
                                        ID   string `json:"id"`
                                } `json:"data"`
                        } `json:"product,omitempty"`
                        Environment *struct {
                                Data *struct {
                                        Type string `json:"type"`
                                        ID   string `json:"id"`
                                } `json:"data"`
                        } `json:"environment,omitempty"`
                } `json:"relationships,omitempty"`
        } `json:"data"`
}

func (h *PolicyHandler) ListPoliciesHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        // Parse pagination
        <span class="cov0" title="0">page := 1
        limit := 25
        if p := c.Query("page"); p != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(p); err == nil &amp;&amp; parsed &gt; 0 </span><span class="cov0" title="0">{
                        page = parsed
                }</span>
        }
        <span class="cov0" title="0">if l := c.Query("limit"); l != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(l); err == nil &amp;&amp; parsed &gt; 0 &amp;&amp; parsed &lt;= 100 </span><span class="cov0" title="0">{
                        limit = parsed
                }</span>
        }

        // Build filter
        <span class="cov0" title="0">filter := repositories.ListFilter{
                Page:      page,
                PageSize:  limit,
                SortBy:    c.DefaultQuery("sort_by", "created_at"),
                SortOrder: c.DefaultQuery("sort_order", "DESC"),
                Search:    c.Query("search"),
                AccountID: &amp;accountID,
                Filters:   make(map[string]interface{}),
        }

        // Add product filter if provided
        if productID := c.Query("product_id"); productID != "" </span><span class="cov0" title="0">{
                if pid, err := uuid.Parse(productID); err == nil </span><span class="cov0" title="0">{
                        filter.Filters["product_id = ?"] = pid
                }</span>
        }

        // Get policies from repository with filters
        <span class="cov0" title="0">policies, total, err := h.serviceCoordinator.Repositories.Policy().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to retrieve policies: "+err.Error())
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": policies,
                "meta": gin.H{
                        "page":        page,
                        "limit":       limit,
                        "total":       total,
                        "total_pages": (total + int64(limit) - 1) / int64(limit),
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *PolicyHandler) GetPolicyHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">policyIDStr := c.Param("id")
        policyID, err := uuid.Parse(policyIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid policy ID format")
                return
        }</span>

        // Get policy from repository by ID
        <span class="cov0" title="0">policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Policy not found")
                return
        }</span>

        // Verify policy belongs to the account
        <span class="cov0" title="0">if policy.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Policy not found")
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": policy,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *PolicyHandler) CreatePolicyHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">var req PolicyRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Create policy through service layer
        <span class="cov0" title="0">policy := &amp;entities.Policy{
                AccountID:                     accountID.String(),
                Name:                         req.Data.Attributes.Name,
                Duration:                     req.Data.Attributes.Duration,
                HeartbeatDuration:            req.Data.Attributes.HeartbeatDuration,
                HeartbeatCullStrategy:        req.Data.Attributes.HeartbeatCullStrategy,
                HeartbeatResurrectionStrategy: req.Data.Attributes.HeartbeatResurrectionStrategy,
                HeartbeatBasis:               req.Data.Attributes.HeartbeatBasis,
                MachineUniquenessStrategy:    req.Data.Attributes.MachineUniquenessStrategy,
                MachineMatchingStrategy:      req.Data.Attributes.MachineMatchingStrategy,
                ComponentsStrategy:           req.Data.Attributes.ComponentsStrategy,
                ComponentsFingerprint:        req.Data.Attributes.ComponentsFingerprint,
                ExpirationStrategy:           req.Data.Attributes.ExpirationStrategy,
                ExpirationBasis:              req.Data.Attributes.ExpirationBasis,
                RenewalBasis:                 req.Data.Attributes.RenewalBasis,
                TransferStrategy:             req.Data.Attributes.TransferStrategy,
                AuthenticationStrategy:       req.Data.Attributes.AuthenticationStrategy,
                MachineLeasingStrategy:       req.Data.Attributes.MachineLeasingStrategy,
                ProcessLeasingStrategy:       req.Data.Attributes.ProcessLeasingStrategy,
                OverageStrategy:              req.Data.Attributes.OverageStrategy,
                MaxMachines:                  req.Data.Attributes.MaxMachines,
                MaxProcesses:                 req.Data.Attributes.MaxProcesses,
                MaxUsers:                     req.Data.Attributes.MaxUsers,
                MaxCores:                     req.Data.Attributes.MaxCores,
                MaxUses:                      req.Data.Attributes.MaxUses,
                MaxActivations:               req.Data.Attributes.MaxActivations,
                MaxDeactivations:             req.Data.Attributes.MaxDeactivations,
                Protected:                    req.Data.Attributes.Protected,
                CheckInInterval:              req.Data.Attributes.CheckInInterval,
                CheckInIntervalCount:         req.Data.Attributes.CheckInIntervalCount,
                Metadata:                     req.Data.Attributes.Metadata,
                CreatedAt:                    time.Now(),
                UpdatedAt:                    time.Now(),
        }

        // Set boolean fields with nil checks
        if req.Data.Attributes.Strict != nil </span><span class="cov0" title="0">{
                policy.Strict = *req.Data.Attributes.Strict
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Floating != nil </span><span class="cov0" title="0">{
                policy.Floating = *req.Data.Attributes.Floating
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.RequireHeartbeat != nil </span><span class="cov0" title="0">{
                policy.RequireHeartbeat = *req.Data.Attributes.RequireHeartbeat
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.RequireCheckIn != nil </span><span class="cov0" title="0">{
                policy.RequireCheckIn = *req.Data.Attributes.RequireCheckIn
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.UsePool != nil </span><span class="cov0" title="0">{
                policy.UsePool = *req.Data.Attributes.UsePool
        }</span>

        // Set scheme with type conversion
        <span class="cov0" title="0">if req.Data.Attributes.Scheme != nil </span><span class="cov0" title="0">{
                policy.Scheme = entities.LicenseScheme(*req.Data.Attributes.Scheme)
        }</span>

        // Set product ID if provided
        <span class="cov0" title="0">if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Product != nil </span><span class="cov0" title="0">{
                policy.ProductID = req.Data.Relationships.Product.Data.ID
        }</span>

        // Set environment ID if provided
        <span class="cov0" title="0">if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Environment != nil </span><span class="cov0" title="0">{
                policy.EnvironmentID = &amp;req.Data.Relationships.Environment.Data.ID
        }</span>

        // Save policy to repository
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Policy().Create(c.Request.Context(), policy); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to create policy: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventPolicyCreated,
                account,
                events.MakeEventResource(policy),
                events.EventMeta{},
        )

        response := gin.H{
                "data": policy,
        }

        c.Header("Location", "/api/v1/policies/"+policy.ID)
        c.JSON(http.StatusCreated, response)</span>
}

func (h *PolicyHandler) UpdatePolicyHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">policyIDStr := c.Param("id")
        policyID, err := uuid.Parse(policyIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid policy ID format")
                return
        }</span>

        <span class="cov0" title="0">var req PolicyRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Get existing policy
        <span class="cov0" title="0">policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Policy not found")
                return
        }</span>

        // Verify policy belongs to the account
        <span class="cov0" title="0">if policy.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Policy not found")
                return
        }</span>

        // Update policy fields
        <span class="cov0" title="0">policy.Name = req.Data.Attributes.Name
        if req.Data.Attributes.Duration != nil </span><span class="cov0" title="0">{
                policy.Duration = req.Data.Attributes.Duration
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Strict != nil </span><span class="cov0" title="0">{
                policy.Strict = *req.Data.Attributes.Strict
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Floating != nil </span><span class="cov0" title="0">{
                policy.Floating = *req.Data.Attributes.Floating
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Scheme != nil </span><span class="cov0" title="0">{
                policy.Scheme = entities.LicenseScheme(*req.Data.Attributes.Scheme)
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.RequireHeartbeat != nil </span><span class="cov0" title="0">{
                policy.RequireHeartbeat = *req.Data.Attributes.RequireHeartbeat
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.HeartbeatDuration != nil </span><span class="cov0" title="0">{
                policy.HeartbeatDuration = req.Data.Attributes.HeartbeatDuration
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.RequireCheckIn != nil </span><span class="cov0" title="0">{
                policy.RequireCheckIn = *req.Data.Attributes.RequireCheckIn
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.UsePool != nil </span><span class="cov0" title="0">{
                policy.UsePool = *req.Data.Attributes.UsePool
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxMachines != nil </span><span class="cov0" title="0">{
                policy.MaxMachines = req.Data.Attributes.MaxMachines
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxProcesses != nil </span><span class="cov0" title="0">{
                policy.MaxProcesses = req.Data.Attributes.MaxProcesses
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxUsers != nil </span><span class="cov0" title="0">{
                policy.MaxUsers = req.Data.Attributes.MaxUsers
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxCores != nil </span><span class="cov0" title="0">{
                policy.MaxCores = req.Data.Attributes.MaxCores
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxUses != nil </span><span class="cov0" title="0">{
                policy.MaxUses = req.Data.Attributes.MaxUses
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.OverageStrategy != nil </span><span class="cov0" title="0">{
                policy.OverageStrategy = req.Data.Attributes.OverageStrategy
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Metadata != nil </span><span class="cov0" title="0">{
                policy.Metadata = req.Data.Attributes.Metadata
        }</span>
        <span class="cov0" title="0">policy.UpdatedAt = time.Now()

        // Save to repository
        if err := h.serviceCoordinator.Repositories.Policy().Update(c.Request.Context(), policy); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to update policy: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventPolicyUpdated,
                account,
                events.MakeEventResource(policy),
                events.EventMeta{},
        )

        response := gin.H{
                "data": policy,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *PolicyHandler) DeletePolicyHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">policyIDStr := c.Param("id")
        policyID, err := uuid.Parse(policyIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid policy ID format")
                return
        }</span>

        // Get policy from repository
        <span class="cov0" title="0">policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Policy not found")
                return
        }</span>

        // Verify policy belongs to the account
        <span class="cov0" title="0">if policy.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Policy not found")
                return
        }</span>

        // Delete policy
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Policy().Delete(c.Request.Context(), policyID); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to delete policy: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventPolicyDeleted,
                account,
                events.MakeEventResource(policy),
                events.EventMeta{},
        )

        c.Status(http.StatusNoContent)</span>
}</pre>
		
		<pre class="file" id="file7" style="display: none">package handlers

import (
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/gokeys/gokeys/internal/domain/services/events"
        "github.com/google/uuid"
)

type ProductHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewProductHandler(serviceCoordinator *services.ServiceCoordinator) *ProductHandler <span class="cov0" title="0">{
        return &amp;ProductHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

type ProductRequest struct {
        Data struct {
                Type       string `json:"type" binding:"required"`
                Attributes struct {
                        Name                 string                 `json:"name" binding:"required"`
                        Code                 *string                `json:"code,omitempty"`
                        DistributionStrategy *string                `json:"distribution_strategy,omitempty"`
                        URL                  *string                `json:"url,omitempty"`
                        Platforms            []string               `json:"platforms,omitempty"`
                        Metadata             map[string]interface{} `json:"metadata,omitempty"`
                } `json:"attributes"`
                Relationships *struct {
                        Environment *struct {
                                Data *struct {
                                        Type string `json:"type"`
                                        ID   string `json:"id"`
                                } `json:"data"`
                        } `json:"environment,omitempty"`
                } `json:"relationships,omitempty"`
        } `json:"data"`
}

func (h *ProductHandler) ListProductsHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        // Parse pagination
        <span class="cov0" title="0">page := 1
        limit := 25
        if p := c.Query("page"); p != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(p); err == nil &amp;&amp; parsed &gt; 0 </span><span class="cov0" title="0">{
                        page = parsed
                }</span>
        }
        <span class="cov0" title="0">if l := c.Query("limit"); l != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(l); err == nil &amp;&amp; parsed &gt; 0 &amp;&amp; parsed &lt;= 100 </span><span class="cov0" title="0">{
                        limit = parsed
                }</span>
        }

        // Build filter
        <span class="cov0" title="0">filter := repositories.ListFilter{
                Page:      page,
                PageSize:  limit,
                SortBy:    c.DefaultQuery("sort_by", "created_at"),
                SortOrder: c.DefaultQuery("sort_order", "DESC"),
                Search:    c.Query("search"),
                AccountID: &amp;accountID,
                Filters:   make(map[string]interface{}),
        }

        // Get products from repository
        products, total, err := h.serviceCoordinator.Repositories.Product().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to retrieve products: "+err.Error())
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": products,
                "meta": gin.H{
                        "page":        page,
                        "limit":       limit,
                        "total":       total,
                        "total_pages": (total + int64(limit) - 1) / int64(limit),
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *ProductHandler) GetProductHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">productIDStr := c.Param("id")
        productID, err := uuid.Parse(productIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid product ID format")
                return
        }</span>

        // Get product from repository by ID
        <span class="cov0" title="0">product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Product not found")
                return
        }</span>

        // Verify product belongs to the account
        <span class="cov0" title="0">if product.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Product not found")
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": product,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *ProductHandler) CreateProductHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">var req ProductRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Create product through service layer
        <span class="cov0" title="0">product := &amp;entities.Product{
                AccountID:            accountID.String(),
                Name:                 req.Data.Attributes.Name,
                DistributionStrategy: req.Data.Attributes.DistributionStrategy,
                Metadata:             req.Data.Attributes.Metadata,
                CreatedAt:            time.Now(),
                UpdatedAt:            time.Now(),
        }

        // Set optional string fields
        if req.Data.Attributes.Code != nil </span><span class="cov0" title="0">{
                product.Code = *req.Data.Attributes.Code
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.URL != nil </span><span class="cov0" title="0">{
                product.URL = *req.Data.Attributes.URL
        }</span>

        // Set platforms
        <span class="cov0" title="0">if req.Data.Attributes.Platforms != nil </span><span class="cov0" title="0">{
                product.Platforms = entities.ProductPlatforms{
                        Supported: req.Data.Attributes.Platforms,
                }
        }</span>

        // Set environment ID if provided
        <span class="cov0" title="0">if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Environment != nil </span><span class="cov0" title="0">{
                product.EnvironmentID = &amp;req.Data.Relationships.Environment.Data.ID
        }</span>

        // Save product to repository
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Product().Create(c.Request.Context(), product); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to create product: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventProductCreated,
                account,
                events.MakeEventResource(product),
                events.EventMeta{},
        )

        response := gin.H{
                "data": product,
        }

        c.Header("Location", "/api/v1/products/"+product.ID)
        c.JSON(http.StatusCreated, response)</span>
}

func (h *ProductHandler) UpdateProductHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">productIDStr := c.Param("id")
        productID, err := uuid.Parse(productIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid product ID format")
                return
        }</span>

        <span class="cov0" title="0">var req ProductRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Get existing product
        <span class="cov0" title="0">product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Product not found")
                return
        }</span>

        // Verify product belongs to the account
        <span class="cov0" title="0">if product.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Product not found")
                return
        }</span>

        // Update product fields
        <span class="cov0" title="0">product.Name = req.Data.Attributes.Name
        if req.Data.Attributes.Code != nil </span><span class="cov0" title="0">{
                product.Code = *req.Data.Attributes.Code
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.DistributionStrategy != nil </span><span class="cov0" title="0">{
                product.DistributionStrategy = req.Data.Attributes.DistributionStrategy
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.URL != nil </span><span class="cov0" title="0">{
                product.URL = *req.Data.Attributes.URL
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Platforms != nil </span><span class="cov0" title="0">{
                product.Platforms = entities.ProductPlatforms{
                        Supported: req.Data.Attributes.Platforms,
                }
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Metadata != nil </span><span class="cov0" title="0">{
                product.Metadata = req.Data.Attributes.Metadata
        }</span>
        <span class="cov0" title="0">product.UpdatedAt = time.Now()

        // Save to repository
        if err := h.serviceCoordinator.Repositories.Product().Update(c.Request.Context(), product); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to update product: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventProductUpdated,
                account,
                events.MakeEventResource(product),
                events.EventMeta{},
        )

        response := gin.H{
                "data": product,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *ProductHandler) DeleteProductHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">productIDStr := c.Param("id")
        productID, err := uuid.Parse(productIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid product ID format")
                return
        }</span>

        // Get product from repository
        <span class="cov0" title="0">product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Product not found")
                return
        }</span>

        // Verify product belongs to the account
        <span class="cov0" title="0">if product.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Product not found")
                return
        }</span>

        // Delete product
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.Product().Delete(c.Request.Context(), productID); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to delete product: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventProductDeleted,
                account,
                events.MakeEventResource(product),
                events.EventMeta{},
        )

        c.Status(http.StatusNoContent)</span>
}</pre>
		
		<pre class="file" id="file8" style="display: none">package handlers

import (
        "crypto/rand"
        "encoding/hex"
        "net/http"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/gokeys/gokeys/internal/adapters/http/middleware"
        "github.com/gokeys/gokeys/internal/adapters/http/responses"
        "github.com/gokeys/gokeys/internal/domain/entities"
        "github.com/gokeys/gokeys/internal/domain/repositories"
        "github.com/gokeys/gokeys/internal/domain/services"
        "github.com/gokeys/gokeys/internal/domain/services/events"
        "github.com/google/uuid"
)

type WebhookEndpointHandler struct {
        serviceCoordinator *services.ServiceCoordinator
}

func NewWebhookEndpointHandler(serviceCoordinator *services.ServiceCoordinator) *WebhookEndpointHandler <span class="cov0" title="0">{
        return &amp;WebhookEndpointHandler{
                serviceCoordinator: serviceCoordinator,
        }
}</span>

type WebhookEndpointRequest struct {
        Data struct {
                Type       string `json:"type" binding:"required"`
                Attributes struct {
                        Name               *string  `json:"name,omitempty"`
                        URL                string   `json:"url" binding:"required"`
                        Events             []string `json:"events,omitempty"`
                        SignatureAlgorithm *string  `json:"signature_algorithm,omitempty"`
                        APIVersion         *string  `json:"api_version,omitempty"`
                        Enabled            *bool    `json:"enabled,omitempty"`
                        MaxRetries         *int     `json:"max_retries,omitempty"`
                        RetryDelay         *int     `json:"retry_delay,omitempty"`
                        Description        *string  `json:"description,omitempty"`
                } `json:"attributes"`
                Relationships *struct {
                        Environment *struct {
                                Data *struct {
                                        Type string `json:"type"`
                                        ID   string `json:"id"`
                                } `json:"data"`
                        } `json:"environment,omitempty"`
                } `json:"relationships,omitempty"`
        } `json:"data"`
}

func (h *WebhookEndpointHandler) ListWebhookEndpointsHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        // Parse pagination
        <span class="cov0" title="0">page := 1
        limit := 25
        if p := c.Query("page"); p != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(p); err == nil &amp;&amp; parsed &gt; 0 </span><span class="cov0" title="0">{
                        page = parsed
                }</span>
        }
        <span class="cov0" title="0">if l := c.Query("limit"); l != "" </span><span class="cov0" title="0">{
                if parsed, err := strconv.Atoi(l); err == nil &amp;&amp; parsed &gt; 0 &amp;&amp; parsed &lt;= 100 </span><span class="cov0" title="0">{
                        limit = parsed
                }</span>
        }

        // Build filter
        <span class="cov0" title="0">filter := repositories.ListFilter{
                Page:      page,
                PageSize:  limit,
                SortBy:    c.DefaultQuery("sort_by", "created_at"),
                SortOrder: c.DefaultQuery("sort_order", "DESC"),
                Search:    c.Query("search"),
                AccountID: &amp;accountID,
                Filters:   make(map[string]interface{}),
        }

        // Get webhook endpoints from repository
        webhookEndpoints, total, err := h.serviceCoordinator.Repositories.WebhookEndpoint().List(c.Request.Context(), filter)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to retrieve webhook endpoints: "+err.Error())
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": webhookEndpoints,
                "meta": gin.H{
                        "page":        page,
                        "limit":       limit,
                        "total":       total,
                        "total_pages": (total + int64(limit) - 1) / int64(limit),
                },
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *WebhookEndpointHandler) GetWebhookEndpointHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">webhookEndpointIDStr := c.Param("id")
        webhookEndpointID, err := uuid.Parse(webhookEndpointIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid webhook endpoint ID format")
                return
        }</span>

        // Get webhook endpoint from repository by ID
        <span class="cov0" title="0">webhookEndpoint, err := h.serviceCoordinator.Repositories.WebhookEndpoint().GetByID(c.Request.Context(), webhookEndpointID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Webhook endpoint not found")
                return
        }</span>

        // Verify webhook endpoint belongs to the account
        <span class="cov0" title="0">if webhookEndpoint.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Webhook endpoint not found")
                return
        }</span>

        <span class="cov0" title="0">response := gin.H{
                "data": webhookEndpoint,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *WebhookEndpointHandler) CreateWebhookEndpointHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">var req WebhookEndpointRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Generate webhook secret
        <span class="cov0" title="0">secretBytes := make([]byte, 32)
        rand.Read(secretBytes)
        secret := hex.EncodeToString(secretBytes)

        // Create webhook endpoint through service layer
        webhookEndpoint := &amp;entities.WebhookEndpoint{
                AccountID:     accountID.String(),
                URL:           req.Data.Attributes.URL,
                Events:        req.Data.Attributes.Events,
                Secret:        secret,
                SigningSecret: secret,
                Enabled:       true,
                MaxRetries:    3,
                RetryDelay:    5,
                CreatedAt:     time.Now(),
                UpdatedAt:     time.Now(),
        }

        // Set optional fields
        if req.Data.Attributes.Name != nil </span><span class="cov0" title="0">{
                webhookEndpoint.Name = *req.Data.Attributes.Name
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Description != nil </span><span class="cov0" title="0">{
                webhookEndpoint.Description = *req.Data.Attributes.Description
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Enabled != nil </span><span class="cov0" title="0">{
                webhookEndpoint.Enabled = *req.Data.Attributes.Enabled
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxRetries != nil </span><span class="cov0" title="0">{
                webhookEndpoint.MaxRetries = *req.Data.Attributes.MaxRetries
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.RetryDelay != nil </span><span class="cov0" title="0">{
                webhookEndpoint.RetryDelay = *req.Data.Attributes.RetryDelay
        }</span>

        // Set environment ID if provided
        <span class="cov0" title="0">if req.Data.Relationships != nil &amp;&amp; req.Data.Relationships.Environment != nil </span><span class="cov0" title="0">{
                webhookEndpoint.EnvironmentID = &amp;req.Data.Relationships.Environment.Data.ID
        }</span>

        // Save webhook endpoint to repository
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.WebhookEndpoint().Create(c.Request.Context(), webhookEndpoint); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to create webhook endpoint: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventWebhookEndpointCreated,
                account,
                events.MakeEventResource(webhookEndpoint),
                events.EventMeta{},
        )

        response := gin.H{
                "data": webhookEndpoint,
        }

        c.Header("Location", "/api/v1/webhook-endpoints/"+webhookEndpoint.ID)
        c.JSON(http.StatusCreated, response)</span>
}

func (h *WebhookEndpointHandler) UpdateWebhookEndpointHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">webhookEndpointIDStr := c.Param("id")
        webhookEndpointID, err := uuid.Parse(webhookEndpointIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid webhook endpoint ID format")
                return
        }</span>

        <span class="cov0" title="0">var req WebhookEndpointRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
                return
        }</span>

        // Get existing webhook endpoint
        <span class="cov0" title="0">webhookEndpoint, err := h.serviceCoordinator.Repositories.WebhookEndpoint().GetByID(c.Request.Context(), webhookEndpointID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Webhook endpoint not found")
                return
        }</span>

        // Verify webhook endpoint belongs to the account
        <span class="cov0" title="0">if webhookEndpoint.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Webhook endpoint not found")
                return
        }</span>

        // Update webhook endpoint fields
        <span class="cov0" title="0">webhookEndpoint.URL = req.Data.Attributes.URL
        if req.Data.Attributes.Name != nil </span><span class="cov0" title="0">{
                webhookEndpoint.Name = *req.Data.Attributes.Name
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Description != nil </span><span class="cov0" title="0">{
                webhookEndpoint.Description = *req.Data.Attributes.Description
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Events != nil </span><span class="cov0" title="0">{
                webhookEndpoint.Events = req.Data.Attributes.Events
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.Enabled != nil </span><span class="cov0" title="0">{
                webhookEndpoint.Enabled = *req.Data.Attributes.Enabled
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.MaxRetries != nil </span><span class="cov0" title="0">{
                webhookEndpoint.MaxRetries = *req.Data.Attributes.MaxRetries
        }</span>
        <span class="cov0" title="0">if req.Data.Attributes.RetryDelay != nil </span><span class="cov0" title="0">{
                webhookEndpoint.RetryDelay = *req.Data.Attributes.RetryDelay
        }</span>
        <span class="cov0" title="0">webhookEndpoint.UpdatedAt = time.Now()

        // Save to repository
        if err := h.serviceCoordinator.Repositories.WebhookEndpoint().Update(c.Request.Context(), webhookEndpoint); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to update webhook endpoint: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventWebhookEndpointUpdated,
                account,
                events.MakeEventResource(webhookEndpoint),
                events.EventMeta{},
        )

        response := gin.H{
                "data": webhookEndpoint,
        }

        c.JSON(http.StatusOK, response)</span>
}

func (h *WebhookEndpointHandler) DeleteWebhookEndpointHandler(c *gin.Context) <span class="cov0" title="0">{
        accountID, err := middleware.GetAccountID(c)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderUnauthorized(c, "Account ID not found in token")
                return
        }</span>

        <span class="cov0" title="0">webhookEndpointIDStr := c.Param("id")
        webhookEndpointID, err := uuid.Parse(webhookEndpointIDStr)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderBadRequest(c, "Invalid webhook endpoint ID format")
                return
        }</span>

        // Get webhook endpoint from repository
        <span class="cov0" title="0">webhookEndpoint, err := h.serviceCoordinator.Repositories.WebhookEndpoint().GetByID(c.Request.Context(), webhookEndpointID)
        if err != nil </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Webhook endpoint not found")
                return
        }</span>

        // Verify webhook endpoint belongs to the account
        <span class="cov0" title="0">if webhookEndpoint.AccountID != accountID.String() </span><span class="cov0" title="0">{
                responses.RenderNotFound(c, "Webhook endpoint not found")
                return
        }</span>

        // Delete webhook endpoint
        <span class="cov0" title="0">if err := h.serviceCoordinator.Repositories.WebhookEndpoint().Delete(c.Request.Context(), webhookEndpointID); err != nil </span><span class="cov0" title="0">{
                responses.RenderInternalError(c, "Failed to delete webhook endpoint: "+err.Error())
                return
        }</span>

        // Broadcast event
        <span class="cov0" title="0">account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
        h.serviceCoordinator.Events.BroadcastEvent(
                c.Request.Context(),
                events.EventWebhookEndpointDeleted,
                account,
                events.MakeEventResource(webhookEndpoint),
                events.EventMeta{},
        )

        c.Status(http.StatusNoContent)</span>
}</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
